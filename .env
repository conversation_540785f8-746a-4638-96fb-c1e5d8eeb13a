ALLIANCE_FTP_HOST=ftp2.allianceps.com
ALLIANCE_FTP_PASSWORD=xxxx-xxxx-xxxx
ALLIANCE_FTP_USERNAME=Revela
ALLIANCE_RENT_ROLL_FUZZ_FACTOR=45
API_V2_KEY_HMAC_SECRET_KEY=9fd49c1c036fe8ff8376a807155e672a
AWS_ACCESS_KEY_ID=00000000000000000000000000000000
AWS_SECRET_ACCESS_KEY=00000000000000000000000000000000
CF_REDEEM_ATTACHMENT_EXPIRY=0
DATA_LAKE_EGRESS_AWS_ACCESS_KEY_ID=00000000000000000000000000000000
DATA_LAKE_EGRESS_AWS_SECRET_KEY=00000000000000000000000000000000
BACKGROUND_CHECK_KEY=00000000000000000000000000000000
ELASTICSEARCH_URL=*************************************
ERROR_PAGE_URL=https://revela-public.s3.amazonaws.com/heroku_error/heroku_error.html
FIREBASE_FCM_SERVER_KEY=firebasefcmserverkey
GEONAMES_USERNAME=fakeusername
GOOGLE_GEOCODING_API_KEY=AIzaSyCJYY5Ooyqdba-HxdSM6yU3mOJCiXteVf0
INSPECTIFY_API_URL=https://app2-staging.inspectify.com/api/v2
INSPECTIFY_API_TOKEN=fakeinspectifyapitoken
IPINFO_IO_GEOCODING_API_KEY=fake
MAILGUN_INGRESS_SIGNING_KEY=fakekey
MAINTENANCE_PAGE_URL=https://revela-public.s3.amazonaws.com/heroku_error/heroku_maintenance.html
MEZO_CLIENT_TOKEN=000000000
MERCURY_API_URL=https://mercurynetworkapiUAT.com/mercuryAPI.asmx/UpdateAppraisalStatusGlobal
MOUSEFLOW_PROJECT_ID=
NELCO_CLIENT_ID=00000
PENINSULA_API_KEY=fakepeninsulaapikey
PENINSULA_BASE_URL=https://api-test.peninsularisk.io
PENINSULA_PASSWORD=fakepeninsulapassword
PENINSULA_USERNAME=fakepeninsulausername
POSTGRES_HOST=localhost
POSTGRES_PASSWORD=
POSTGRES_USER=revela
POSTGRID_API_KEY=000000000
POSTGRID_ADDRESS_API_KEY=000000000
RECAPTCHA_SECRET_KEY=6LfLIDsUAAAAACt8FJIcN_5DdE7XK-cbxMAB1EwU
RECAPTCHA_SITE_KEY=6LfLIDsUAAAAAAmgMeJngY1wJNs47_LvMmN7vnox
REDIS_URL=redis://localhost:6379
REFERENCE_SERVICES_PASSWORD=fakepassword
REFERENCE_SERVICES_TEST_MODE=YES
REFERENCE_SERVICES_USERNAME=fakeusername
RUBY_DEBUG_LOG_LEVEL=ERROR
S3_ACTIVE_STORAGE_BUCKET=revela-storage
S3_INVOICES_BUCKET=revela-invoices
S3_MAINTENANCE_PHOTO_BUCKET=revela-photos-maintenance
S3_UPLOADS_BUCKET=revela-uploads
SAFE_RENT_ORIGINATOR_ID=100005
SCHEDULED_JOBS=false
SIERRA_LEONE_SFTP_PRIVATE_KEY_BASE64=shhitsprivate
SIERRA_LEONE_SFTP_URL=sierra_leone_sftp_url
SIERRA_LEONE_SFTP_USERNAME=sierra_leone_sftp_username
SFTPTOGO_URL=sftp://someuser:<EMAIL>
SFTPTOGO_WEBHOOK_SECRETS={"alever":"sekret"}
SSL_ENABLED=false
THE_CLOSING_DOCS_CLIENT_ID=faketheclosingdocsclientid
THE_CLOSING_DOCS_HOST=api.staging.payscore.com
THE_CLOSING_DOCS_SECRET_KEY=faketheclosingdocssecretkey
TIN_KEY=00000000000000000000000000000000
TWILIO_AUTH_TOKEN=325774a07dfb8260a9ed3c2fd0b512c8
TWILIO_SID=**********************************
TWILIO_MESSAGING_SERVICE_SID=00000000000000000000000000
TWILIO_COLLECTIONS_SERVICE_SID=00000000000000000000000001
UNIT_API_TOKEN=fake_unit_api_token
UNIT_API_URL=https://api.s.unit.sh
UNIT_JWT_ISS=cc.staging-revela.co
UNIT_UI_URL=https://ui.s.unit.sh
UNIT_WEBHOOK_SECRET=fake_unit_webhook_secret
ZEAMSTER_DEVELOPER_ID=df253
ZILLOW_WEBHOOK_AUTH_TOKEN=fakezillowtoken
