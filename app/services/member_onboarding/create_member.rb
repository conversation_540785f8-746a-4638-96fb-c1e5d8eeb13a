class MemberOnboarding::<PERSON>reateM<PERSON>ber < MemberOnboarding::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def tenant_attributes
    member_attributes = attributes['member'].dup
    member_attributes.delete('student')

    member_attributes['metadata_attributes'] = {
      data: {
        nickname: member_attributes.delete('nickname'),
        middle_name: member_attributes.delete('middle_name'),
        student_id_number: member_attributes.delete('student_id_number'),
        drivers_license_number: member_attributes.delete(
          'drivers_license_number'
        ),
        requested_parking: attributes['requested_parking'],
        room_type: attributes['room_type'],
        food_allergies:
        attributes.dig('dphie_medical_information', 'food_allergies'),
        non_food_allergies:
        attributes.dig('dphie_medical_information', 'non_food_allergies'),
        special_health_conditions:
        attributes.dig('dphie_medical_information',
                       'special_health_conditions'),
        special_medications_or_treatments:
        attributes.dig('dphie_medical_information',
                       'special_medications_or_treatments'),
        dphie_payment_plan: attributes['dphie_payment_plan'],
        financial_aid: attributes['financial_aid'],
        payment_plan_529: attributes['payment_plan_529'],
        florida_prepaid_account: attributes['florida_prepaid_account'],
        vehicle_make: attributes.dig('vehicle', 'make'),
        vehicle_model: attributes.dig('vehicle', 'model'),
        vehicle_year: attributes.dig('vehicle', 'year'),
        vehicle_color: attributes.dig('vehicle', 'color'),
        vehicle_license_plate_state:
        attributes.dig('vehicle', 'license_plate_state'),
        vehicle_license_plate_number:
        attributes.dig('vehicle', 'license_plate_number'),
        emergency_contact_first_name:
        attributes.dig('emergency_contact', 'first_name'),
        emergency_contact_middle_name:
        attributes.dig('emergency_contact', 'middle_name'),
        emergency_contact_last_name:
        attributes.dig('emergency_contact', 'last_name'),
        emergency_contact_nickname:
        attributes.dig('emergency_contact', 'nickname'),
        emergency_contact_phone:
        attributes.dig('emergency_contact', 'phone'),
        emergency_contact_relation:
        attributes.dig('emergency_contact', 'relation'),
        emergency_contact_permission_to_contact:
        attributes.dig('emergency_contact', 'permission_to_contact'),
        university_email: attributes['university_email'],
        year_in_school: attributes['year_in_school'],
        expected_graduation: attributes['anticipated_graduation_date'],
        abroad: attributes['anticipated_study_abroad'],
        pledge_class: attributes['pledge_class'],
        major: attributes['major'],
        university: attributes['university'],
        payment_preference: attributes['payment_preference'],
        completed_member_profile: true
      }.compact_blank
    }.compact_blank

    ssn = member_attributes.delete('social_security_number')
    member_attributes['taxpayer_identification_attributes'] = {
      tin: ssn,
      tin_type: :ssn
    }

    member_attributes
  end
end
