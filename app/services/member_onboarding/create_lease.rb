class MemberOnboarding::CreateLease
  extend Service
  include MemberOnboarding::Submit::Countersigner

  attr_reader :member, :guarantor, :cohort

  def initialize(member:, guarantor:, cohort:)
    @member = member
    @guarantor = guarantor
    @cohort = cohort
  end

  def call
    result = OpenStruct.new(successful?: true)

    return result unless cohort.receives_lease?

    ActiveRecord::Base.transaction do
      lease = create_lease

      if cohort.bill_one_time_charges_immediately?
        # TODO: Can remove `bill_one_time_charges_immediately?` when legacy onboarding is removed
        result = MemberOnboarding::CreateCharges::OneTimeLease.call(
          lease: lease,
          cohort: cohort
        )
        fail ActiveRecord::Rollback unless result.successful?
      end

      if cohort.generates_lease_document?
        generate_document(lease)

        if cohort.signs_lease?
          primary_signers = [lease.primary_tenant]
          primary_signers << lease.primary_guarantor if cohort.guarantor_signs_lease?

          result = ElectronicDocument::RequestSignatures.call(
            document: lease,
            primary_signers: primary_signers,
            user: signature_user,
            countersigner: get_countersigner
          )

          fail ActiveRecord::Rollback unless result.successful?
        end
      end

      result = OpenStruct.new(successful?: true, lease: lease)
    end

    result
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  delegate :property, to: :cohort

  # Using CountersignerConcern for countersigner logic
  def get_countersigner
    super(:lease_agreement)
  end

  def create_lease
    Lease.create!(unit: unit) do |lease|
      lease.start_date = cohort.start_date
      lease.end_date = cohort.end_date

      membership = lease.lease_memberships.primary_tenant.build(tenant: member)

      lease.lease_memberships.guarantor.build(tenant: guarantor) if guarantor

      schedule = lease.build_charge_schedule
      lease.proration = cohort.proration

      cohort.lease_schedule_entries_attrs.each do |attrs|
        schedule.entries.build(
          recurring: attrs[:recurring],
          name: attrs[:name],
          amount: attrs[:amount],
          account: attrs[:account],
          charge_preset: attrs[:charge_preset]
        ) do |entry|
          # This has to be in the block instead of as an attribute because
          # otherwise charge schedule is nil when overriding date setters for
          # clamping
          entry.start_date = attrs[:start_date] if attrs[:start_date]
          entry.end_date = attrs[:end_date] if attrs[:end_date]

          entry.allocations.build(
            amount: attrs[:amount],
            lease_membership: membership
          )
        end
      end

      add_metadata(lease)
    end
  end

  def unit
    property.units.create_with(
      floorplan: floorplan
    ).find_or_create_by!(
      name: 'Unplaced'
    )
  end

  def floorplan
    property.floorplans.create_with(
      bedrooms: 0, bathrooms: 0
    ).find_or_create_by!(
      name: 'Unplaced'
    )
  end

  def signature_user
    Customer.current.representative
  end

  def add_metadata(lease)
    return unless (metadata_attributes = cohort.lease_metadata_attributes).present?

    lease.build_metadata(
      data: metadata_attributes
    )
  end

  # TODO: this was copied from LeasesController.
  def generate_document(lease)
    template_id = cohort.lease_document_template_id

    file = Lease::GenerateDocument.call(
      lease: lease,
      template: Document.find(template_id)
    )

    save_document(lease, file)
    save_document_pdf(lease, file)
  end

  def save_document(lease, file)
    document = Document.new(
      parent: lease,
      upload: file,
      override_content_type: Mime::Type.lookup_by_extension(:docx).to_s,
      template: lease.document_template,
      template_options: {
        template_type: :unexecuted_lease_template,
        lease_id: lease.id
      }
    )

    document.save!

    document.update!(direct_upload_url: document.direct_upload_url)
  end

  def save_document_pdf(lease, file)
    file.open.rewind
    without_tags = DocxFilling.remove_signature_tags(file)
    Libreconv.convert(without_tags.path, file.path)

    Document.new(
      parent: lease,
      upload: file,
      override_content_type: Mime::Type.lookup_by_extension(:pdf).to_s,
      template_options: {
        template_type: 'unexecuted_lease',
        lease_id: lease.id
      }
    ).tap do |doc|
      doc.upload_file_name = lease.signature_file_name(false)
      doc.save!
      doc.update!(direct_upload_url: doc.direct_upload_url)
    end
  end
end
