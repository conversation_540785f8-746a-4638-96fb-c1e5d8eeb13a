class MemberOnboarding::CreateGuarantor < MemberOnboarding::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def tenant_attributes
    guarantor_attributes = attributes['guarantor'].dup

    guarantor_attributes['metadata_attributes'] = {
      data: {
        drivers_license_number: guarantor_attributes.delete(
          'drivers_license_number'
        ),
        completed_member_profile: true
      }
    }

    ssn = guarantor_attributes.delete('social_security_number')
    guarantor_attributes['taxpayer_identification_attributes'] = {
      tin: ssn,
      tin_type: :ssn
    }

    guarantor_attributes
  end
end
