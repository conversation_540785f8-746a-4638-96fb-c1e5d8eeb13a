class MemberOnboarding::Tenants::Submit
  extend ::Service
  attr_reader(*%i[wizard params request existing_membership])

  def initialize(wizard:, params:, request:)
    @wizard = wizard
    @params = params
    @request = request
    @existing_membership = wizard.existing_membership
  end

  delegate :cohort, :onboarding, :member, to: :wizard

  def member_profile_attrs
    wizard.steps['member_profile']&.profile_attributes
  end

  def guarantor_profile_attrs
    wizard.steps['guarantor_profile']&.profile_attributes
  end

  def call # rubocop:disable Metrics
    result = OpenStruct.new(successful?: true)

    ActiveRecord::Base.transaction do
      ActiveRecord::Base.with_advisory_lock(wizard.redis_key, transaction: true) do
        result = prevent_double_submit!
        fail ActiveRecord::Rollback unless result.successful?

        result = update_member!
        fail ActiveRecord::Rollback unless result.successful?

        result = create_guarantor!
        fail ActiveRecord::Rollback unless result.successful?

        guarantor = result.guarantor

        result = ensure_membership_agreement!(guarantor)
        fail ActiveRecord::Rollback unless result.successful?

        membership = result.membership

        result = ensure_membership_charges!(membership)
        fail ActiveRecord::Rollback unless result.successful?

        result = ensure_lease!(guarantor)
        fail ActiveRecord::Rollback unless result.successful?

        lease = result.lease

        result = ensure_risk_release_auto_enrollment!(
          lease: lease,
          submission_membership_agreement: membership
        )
        fail ActiveRecord::Rollback unless result.successful?

        result = ensure_risk_release_enrollment_and_installments!(
          lease: lease,
          submission_membership_agreement: membership
        )
        fail ActiveRecord::Rollback unless result.successful?

        # Send Invite
        [member, guarantor].compact.each do |tenant|
          Tenant::SendWelcomeEmail.call(tenant)
        end

        # Final result
        result = OpenStruct.new(successful?: true, membership: membership, lease: lease)
        yield result
      end
    end
    result
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  rescue ActiveRecord::RecordNotFound => e
    OpenStruct.new(successful?: false, errors: e.message)
  end

  private

  def prevent_double_submit!
    if wizard.submitted?(check_redis: true)
      OpenStruct.new(successful?: false, errors: 'Preventing Double Submission')
    else
      OpenStruct.new(successful?: true, tenant: member)
    end
  end

  def update_member!
    member.update!(member_profile_attrs) if onboarding.information_collection.present?
    OpenStruct.new(successful?: true, tenant: member)
  end

  def create_guarantor!
    if !cohort.accepts_guarantor? ||
       (!cohort.requires_guarantor? && wizard.steps['guarantor_profile']&.skip_guarantor == true)
      OpenStruct.new(successful?: true, guarantor: nil)
    else
      email = guarantor_profile_attrs[:email]
      guarantor = Tenant.resident.find_or_initialize_by(email:) do |g|
        g.assign_attributes(guarantor_profile_attrs)
      end
      guarantor.save!
      OpenStruct.new(successful?: true, guarantor: guarantor)
    end
  end

  def ensure_membership_agreement!(guarantor)
    MemberOnboarding::CreateMembership.new(
      member: member,
      guarantor: guarantor,
      cohort: cohort,
      params: params,
      request: request
    ).call(existing_agreement: existing_membership)
  end

  def ensure_membership_charges!(membership)
    MemberOnboarding::CreateCharges::Membership.call(membership: membership, cohort: cohort)
  end

  def ensure_lease!(guarantor)
    MemberOnboarding::CreateLease.call(member: member, guarantor: guarantor, cohort: cohort)
  end

  def ensure_risk_release_auto_enrollment!(
    lease: nil,
    submission_membership_agreement: nil
  )
    unless onboarding.membership_agreement&.include_damage_waiver?
      return OpenStruct.new(successful?: true)
    end

    lease_membership = lease&.primary_lease_membership

    # TODO: clean this up now that membership is always required in configured onboarding
    membership_agreement = submission_membership_agreement ||
                           Tenant::SimpleAgreementsQuery.current_simple_agreement(tenant: member)
    simple_agreement_membership = membership_agreement&.primary_membership

    RiskRelease::Enrollment.create!(
      start_date: MemberOnboarding::DamageWaiverDefaults.coverage_start_date,
      end_date: MemberOnboarding::DamageWaiverDefaults.coverage_end_date,
      simple_agreement_membership: simple_agreement_membership,
      lease_membership: lease_membership
    )

    OpenStruct.new(successful?: true)
  end

  def ensure_risk_release_enrollment_and_installments!(
    lease: nil,
    submission_membership_agreement: nil
  )
    should_enroll = cohort.offer_damage_waiver? && wizard.steps['risk_management_program'].accepted
    return OpenStruct.new(successful?: true) unless should_enroll

    risk_release_configuration = onboarding.risk_release
    lease_membership = lease&.primary_lease_membership

    # TODO: clean this up now that membership is always required in configured onboarding
    membership_agreement = submission_membership_agreement ||
                           Tenant::SimpleAgreementsQuery.current_simple_agreement(tenant: member)
    simple_agreement_membership = membership_agreement&.primary_membership

    MemberOnboarding::CreateRiskReleaseEnrollment.call(
      risk_release_configuration: risk_release_configuration,
      simple_agreement_membership: simple_agreement_membership,
      lease_membership: lease_membership
    )
  end
end
