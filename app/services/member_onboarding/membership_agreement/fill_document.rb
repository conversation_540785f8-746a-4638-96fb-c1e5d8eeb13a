class MemberOnboarding::MembershipAgreement::FillDocument
  extend Service
  include DocxFilling

  attr_reader :membership_agreement, :pdf, :template_document

  def initialize(membership_agreement:, pdf: true, template_document_id: nil)
    @membership_agreement = membership_agreement
    @pdf = pdf
    @template_document = Document.find(template_document_id)
  end

  def call
    docx = generate_docx(template: template)

    if pdf
      pdf_tempfile = Tempfile.new(['membership_agreement', '.pdf'])

      Libreconv.convert(docx.path, pdf_tempfile.path)

      pdf_tempfile
    else
      docx
    end
  end

  private

  def docx_values
    property = membership_agreement.property
    chapter = property.company
    member = membership_agreement.primary_tenant
    landlord = landlord(property)

    values = {
      'todays_date' => Time.zone.today.to_fs(:human_date),
      'meta[chapter_name]' => chapter.name,
      'meta[chapter_state_with_prefix]' => region_with_prefix(chapter.address),
      'meta[chapter_address]' => chapter.address.simple_address,
      'property_address' => property.address.simple_address,
      'meta[landlord_name]' => landlord&.name,
      'tenants[0].name' => member.name,
      'meta[university_name]' => chapter.meta(:university)
    }

    if pdf
      values.merge!(
        'tenants[0].signature' => '',
        'tenants[0].signature_name' => ''
      )
    end

    values
  end

  def pdf_values
    docx_values
  end

  def template
    DocumentFilling::DocumentCache.fetch_or_download(
      document: template_document
    )
  end

  def region_with_prefix(address)
    name = address.region_name

    if name.start_with?(/A|E|I|O|U/)
      "an #{name}"
    else
      "a #{name}"
    end
  end

  def landlord(property)
    return nil unless Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])

    landlord_name = case property.name
                    when 'Georgia Phi'
                      'Georgia Phi Housing Corporation'
                    when 'Indiana Zeta'
                      'Indiana Zeta Housing Corporation'
                    when 'North Carolina Omega'
                      'North Carolina Omega House Corporation'
                    when 'Iowa Beta'
                      'Sigma Alpha Epsilon Fraternity - Iowa Beta Riverside Corporation'
                    when 'Louisiana Epsilon'
                      'Louisiana Epsilon Home Corporation of Baton Rouge Louisiana Inc'
                    when 'Minnesota Alpha'
                      'Minnesota Alpha Association of Sigma Alpha Epsilon'
                    when 'Missouri Alpha'
                      'Sigma Alpha Epsilon Club of Columbia Missouri'
                    when 'Texas Tau'
                      'Texas Tau for Sigma Alpha Epsilon Housing Authority'
                    when 'Texas Rho'
                      'Sigma Alpha Epsilon Texas Rho House Corporation'
                    when 'Mississippi Theta'
                      'House Corporation of Mississippi Theta of Sigma Alpha Epsilon'
                    when 'Alabama Alpha-Mu'
                      'Alabama Alpha Mu Fraternal Association'
                    when 'Pennsylvania Alpha-Zeta'
                      'Pennsylvania Alpha Zeta Association of the Sigma Alpha Epsilon Fraternity'
                    when 'Wyoming Alpha'
                      'Wyoming Alpha SAE Fraternity House Corporation'
                    when 'Ohio Lambda'
                      'Housing Corporation of Ohio Lambda'
                    when 'Colorado Chi'
                      'Colorado Chi House Corporation'
                    when 'Illinois Delta'
                      'Illinois Delta House Corporation'
                    when 'Colorado Phi'
                      'Colorado Phi House Corporation'
                    when 'California Lambda'
                      'California Lambda House Corporation'
                    when 'California Beta'
                      return nil # Billing Only
                    else
                      property.company.name.gsub(
                        'Chapter of Sigma Alpha Epsilon Fraternity',
                        'Housing LLC'
                      )
                    end

    Company.all.find_by!(name: landlord_name)
  rescue ActiveRecord::RecordNotFound
    raise "Unable to determine landlord for '#{property.name}'"
  end
end
