class MemberOnboarding::CreateCharges::Base
  extend Service

  attr_reader :cohort

  def initialize(cohort:)
    @cohort = cohort
  end

  def call
    return success_result unless should_process?

    ActiveRecord::Base.transaction do
      if cohort.should_group_invoices?
        process_grouped_charges
      else
        process_individual_charges
      end
    end

    success_result
  rescue ActiveRecord::RecordInvalid => e
    failure_result(e.record.errors.full_messages)
  end

  private

  def should_process?
    agreement.present? && charge_memberships.any?
  end

  def process_grouped_charges
    grouped_charges.each do |billing_term, memberships|
      create_charge_invoice(
        presets: memberships.map(&:charge_preset),
        net_d: billing_term.is_a?(Integer) ? billing_term : nil,
        due_date: billing_term.is_a?(Date) ? billing_term : nil
      )
    end
  end

  def process_individual_charges
    if cohort.is_a?(GreekHousing::Cohort)
      charge_memberships.each do |cm|
        create_charge_invoice(presets: [cm])
      end
    else
      charge_memberships.each do |cm|
        create_charge_invoice(
          presets: [cm.charge_preset],
          net_d: cm.effective_net_d,
          due_date: cm.due_date
        )
      end
    end
  end

  def grouped_charges
    charge_memberships.group_by do |cm|
      cm.due_date.presence || cm.effective_net_d
    end
  end

  def create_charge_invoice(presets:, net_d: nil, due_date: nil)
    Accounting::ChargeFromPreset.call!(
      charge_presets: presets,
      date: Time.zone.today,
      net_d: net_d,
      due_date: due_date
    ) do |invoice|
      assign_invoice_attributes(invoice)
    end
  end

  def assign_invoice_attributes(invoice)
    fail NotImplementedError
  end

  def charge_memberships
    fail NotImplementedError
  end

  def success_result
    OpenStruct.new(successful?: true)
  end

  def failure_result(errors)
    OpenStruct.new(successful?: false, errors: errors)
  end
end
