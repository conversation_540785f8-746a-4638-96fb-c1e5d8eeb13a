class Tunisia::DebitCard::Refresh < Tunisia::Service
  ENDPOINT = '/cards'.freeze

  def initialize(company:)
    super()
    @company = company
  end

  def call
    response = get(ENDPOINT, request)
    json = response.body.with_indifferent_access
    @data = json[:data]

    ensure_no_deleted_cards!

    ActiveRecord::Base.transaction do
      @data.each do |debit_card_data|
        process_debit_card_data!(debit_card_data)
      end
    end

    OpenStruct.new(successful?: true)
  end

  private

  def process_debit_card_data!(debit_card_data)
    case debit_card_data
    in {
      id: tunisia_card_id,
      attributes: attributes,
      relationships: {
        account: { data: { id: deposit_account_id } },
        customer: { data: { id: customer_id } }
      }
    }
      if customer_id != @company.tunisia_customer_id
        fail "Debit card #{tunisia_card_id} does not belong to company #{@company.id}"
      end

      deposit_account = company_deposit_accounts_map[deposit_account_id]

      fail "Tunisia::DepositAccount #{deposit_account_id} not found" if deposit_account.nil?

      find_or_create_debit_card!(deposit_account, tunisia_card_id, attributes)
    else
      fail "Unexpected debit card data format: #{debit_card_data}"
    end
  end

  def find_or_create_debit_card!(deposit_account, tunisia_card_id, attributes)
    case attributes
    in {
        createdAt: card_created_at,
        last4Digits: last_four,
        expirationDate: expiration,
        status: status
      }

      debit_card = deposit_account.debit_cards.find_or_initialize_by(tunisia_card_id:)

      debit_card.update!(
        last_four: last_four,
        expiration: expiration,
        status: status,
        card_created_at: card_created_at
      )
    else
      fail "Unexpected attributes format: #{attributes}"
    end
  end

  def debit_card_ids
    @debit_card_ids ||= @data.pluck(:id)
  end

  def deposit_account_ids
    @deposit_account_ids ||= @data.map do |debit_card_data|
      debit_card_data.dig(:relationships, :account, :data, :id)
    end
  end

  def company_deposit_accounts_map
    return @company_deposit_accounts_map if defined?(@company_deposit_accounts_map)

    company_deposit_accounts = Tunisia::DepositAccount.where(bank_account: @company.bank_accounts)
                                                      .where(tunisia_id: deposit_account_ids)

    @company_deposit_accounts_map = company_deposit_accounts.index_by(&:tunisia_id)
  end

  def request
    {
      filter: {
        customerId: @company.tunisia_customer_id
      }
    }
  end

  def ensure_no_deleted_cards!
    missing_cards = Tunisia::DebitCard
                    .joins(:deposit_account)
                    .where(deposit_account: { bank_account: @company.bank_accounts })
                    .where.not(tunisia_card_id: debit_card_ids)

    card_missing = missing_cards.exists?

    return unless card_missing

    missing_tunisia_card_ids = missing_cards.pluck(:tunisia_card_id).join(', ')

    fail "Debit card with tunisia_card_ids #{missing_tunisia_card_ids} missing from response"
  end
end
