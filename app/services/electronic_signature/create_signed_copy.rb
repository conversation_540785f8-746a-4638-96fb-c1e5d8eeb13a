class ElectronicSignature::CreateSignedCopy
  extend Service

  attr_reader :document, :electronic_signatures, :template_type

  def initialize(document:, electronic_signatures:, template_type:)
    @document = document
    @electronic_signatures = electronic_signatures
    @template_type = template_type
  end

  def call
    replacements = {}

    tempfiles = []

    electronic_signatures.primary_signer.order(id: :asc).each.with_index do |signature, i|
      tempfiles << (tempfile = signature.signature_png_tempfile)

      klass = signature.recipient_type.underscore.pluralize
      key = "{#{klass}[#{i}].signature}"
      replacements[key] = {
        height: 60,
        width: 240,
        path: tempfile.path
      }
    end

    electronic_signatures.countersigner.order(id: :asc).each.with_index do |signature, i|
      tempfiles << (tempfile = signature.signature_png_tempfile)

      key = "{countersigners[#{i}].signature}"
      replacements[key] = {
        height: 60,
        width: 240,
        path: tempfile.path
      }
    end

    template_url = electronic_signature_request.unexecuted_document.direct_upload_url

    tempfiles << (source = Down.download(template_url))
    tempfiles << (destination = Tempfile.new("#{template_type}_a"))

    Omnidocx::Docx.write_images_to_doc(replacements, source, destination)

    destination.rewind
    doc = DocxFilling::ReplaceUno.build(destination.path)

    # Add signature tags
    initials = ->(person) { [person.first_name.first, person.last_name.first].join.upcase }

    electronic_signatures.primary_signer.order(id: :asc).each.with_index do |signature, i|
      klass = signature.recipient_type.underscore.pluralize
      doc.replace("{#{klass}[#{i}].signature_name}", signature.full_name, true)
      doc.replace("{#{klass}[#{i}].initials}", initials[signature.recipient], true)
      doc.replace("{#{klass}[#{i}].signature_date}", signature.created_at.to_fs(:short_date), true)
      doc.replace("{#{klass}[#{i}].signature_datetime}", signature.created_at.to_fs(:short_datetime), true)
    end

    electronic_signatures.countersigner.order(id: :asc).each.with_index do |signature, i|
      doc.replace("{countersigners[#{i}].signature_name}", signature.full_name, true)
      doc.replace("{countersigners[#{i}].initials}", initials[signature.recipient], true)
      doc.replace("{countersigners[#{i}].signature_date}", signature.created_at.to_fs(:short_date), true)
      doc.replace("{countersigners[#{i}].signature_datetime}", signature.created_at.to_fs(:short_datetime), true)
    end

    # TODO: Remove
    # if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
    #   case document
    #   when Agreements::SimpleAgreement
    #     MemberOnboarding::MembershipAgreement::FillDocument.new(
    #       membership_agreement: document
    #     ).send(:docx_values).each do |key, value|
    #       doc.replace("{#{key}}", value, true)
    #     end
    #   end
    # end

    # Remove unused fields
    doc.replace(/{[^\s]+}/, '', true)
    tempfiles << (final = Tempfile.new("#{template_type}_b"))
    doc.commit(final.path)
    final.rewind

    pdf_tempfile = Tempfile.new
    Libreconv.convert(final.path, pdf_tempfile.path)

    signed_copy = Document.new(
      parent: document,
      template: electronic_signature_request.unexecuted_copy,
      template_options: {
        template_type: template_type,
        lease_id: (document.id if document.is_a?(Lease))
      },
      override_content_type: Mime::Type.lookup_by_extension(:pdf).to_s,
      upload: pdf_tempfile
    )

    executed = template_type.starts_with?('executed')
    signed_copy.upload_file_name = document.signature_file_name(executed)

    signed_copy.save!

    signed_copy.update!(direct_upload_url: signed_copy.direct_upload_url)

    tempfiles.each(&:unlink)

    OpenStruct.new(successful?: true, signed_copy: signed_copy)
  end

  delegate :electronic_signature_request, to: :document
end
