##
# Creates or updates (by +id+) a +Vendor+ for +company+ from +params+.
# Assigns default address country.
class Vendor::Persist
  extend Service

  attr_reader :params

  def initialize(params)
    @params = params
  end

  def call
    @vendor = Vendor.find_or_initialize_by(id: params[:id])
    vendor.assign_attributes(vendor_attributes)

    vendor.address&.country ||= 'United States of America'

    vendor.save!

    successful
  rescue ActiveRecord::RecordInvalid
    unsuccessful
  end

  private

  attr_reader :vendor

  def vendor_attributes
    params.require(:vendor).permit(
      :name, :kind, :website, :notes, :business_type,
      :preferred_disbursement, :unconsolidated_checks,
      tags: [],
      vendor_contacts_attributes: vendor_contacts_attributes,
      address_attributes: Address::PERMISSABLE_ATTRIBUTES,
      taxpayer_identification_attributes: taxpayer_attributes,
      insurance_policies_attributes: Insurance::Policy::PERMISSABLE_ATTRIBUTES,
      metadata_attributes: [data: {}]
    )
  end

  def vendor_contacts_attributes
    %i[id _destroy first_name last_name email phone]
  end

  def taxpayer_attributes
    %i[id tin tin_type]
  end

  def successful
    OpenStruct.new(successful?: true, vendor: vendor)
  end

  def unsuccessful
    OpenStruct.new(successful?: false, vendor: vendor,
                   errors: vendor.errors.full_messages)
  end
end
