class ManagementContract::GenerateDocument
  extend Service
  include DocxFilling

  attr_reader :management_contract, :template

  def initialize(management_contract:, template:)
    @management_contract = management_contract
    @template = template
  end

  def call
    tempfile = Down.download(template.direct_upload_url)

    generate_docx(template: tempfile)
  end

  protected

  def docx_values
    pdf_values
  end

  def pdf_values
    values = {
      entity_name: management_contract.company.name
    }

    management_contract.company.owners.each.with_index do |owner, index|
      %i[name first_name last_name email phone].each do |method|
        values["owners[#{index}].#{method}"] = owner.send(method)
      end
    end

    {
      todays_date: Time.zone.today,
      start_date: management_contract.start_date,
      end_date: management_contract.end_date
    }.each do |key, value|
      next if value.blank?

      values[key] = value.to_fs(:human_date)
      values["#{key}.month_name"] = value.strftime('%B')
      values["#{key}.day_ordinal"] = value.day.ordinalize
      values["#{key}.year"] = value.year
    end

    values['approval_amount'] = management_contract.maintenance_limit.format
    values['approval_amount.expanded'] = expanded_dollar_amount(management_contract.maintenance_limit)

    management_contract.properties.order(name: :asc).each.with_index do |property, index|
      key = "properties[#{index}]"

      values["#{key}.address"] = property.address.simple_address
      values["#{key}.address.county"] = property.address.county
    end

    management_contract.metadata_values.each do |key, value|
      values["meta[#{key}]"] = value
    end

    values
  end

  def expanded_dollar_amount(amount)
    dollars = amount.to_i

    cents = (amount % amount.currency.subunit_to_unit).cents

    str = "#{dollars.humanize.titleize} Dollars"
    str += " and #{cents.humanize.titleize} Cents" if cents.positive?

    str
  end
end
