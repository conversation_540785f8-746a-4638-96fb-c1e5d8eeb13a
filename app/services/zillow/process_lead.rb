class Zillow::ProcessLead
  extend Service
  attr_reader :params

  def initialize(params)
    @params = params
  end

  def call
    identified do
      if listing.present?
        params = ActionController::Parameters.new(guest_card: guest_card_hash(listing.property))
        GuestCard::Create.call!(params:)
      else
        properties.each do |property|
          params = ActionController::Parameters.new(guest_card: guest_card_hash(property))
          GuestCard::Create.call!(params:)
        end
      end
    end
    self.class.resp_success
  rescue => e
    return self.class.resp_error(errors: [e.message])
  end

  def guest_card_hash(property)
    {
      property_id: property.id,
      floorplan_id: listing&.floorplan&.id,
      portfolio_id: property.portfolio&.id,
      move_in_date: explicit_move_in_date || move_in_timeframe_date,
      style:,
      source: 'Zillow.com',
      data: guest_card_data_hash
    }.compact_blank
  end

  def guest_card_data_hash
    {
      raw_zillow_payload: params.to_unsafe_h,
      notes: params[:message],
      message: params[:message],
      first_name: params[:name]&.split&.first,
      last_name: params[:name]&.split&.last,
      email: params[:email],
      phone: Phony.normalize(params[:phone], default_country_code: 'US')
    }.compact_blank
  end

  def style
    return nil unless params['numBedroomsSought']

    return :four_bedroom if (num_br = params['numBedroomsSought'].to_i) > 4

    GuestCard.styles.invert[num_br]
  end

  def explicit_move_in_date
    return nil if params['movingDate'].blank?

    Date.strptime(params['movingDate'], '%Y%m%d')
  end

  def move_in_timeframe_date
    return nil if params['moveInTimeframe'].blank?

    case params['moveInTimeframe']
    when 'asap'
      Time.zone.today
    when 'week'
      3.days.from_now
    when 'month'
      14.days.from_now
    when 'twoWeeks'
      10.days.from_now
    when 'twoMonths'
      45.days.from_now
    end.to_date
  end

  def identified(&)
    parse = Zillow::Hotpads::Identifiers.parse
    unless params['providerModelId'].blank? || params['providerModelId'].to_s == '0'
      @ids = parse.hotpads_model_id(params['providerModelId'])
    end

    @ids = parse.hotpads_listing_id(params['listingId']) if @ids.blank?
    customer.activate(&)
  end

  def customer = @customer ||= Customer.find(@ids[:customer_id])

  def zillow_claim = @zillow_claim ||= Zillow::Claim.find(@ids[:zillow_claim_id])

  def listing = @listing ||= Listing.find_by(id: @ids[:listing_id])

  def properties
    @property ||= [listing&.property].compact.presence || zillow_claim.properties
  end
end
