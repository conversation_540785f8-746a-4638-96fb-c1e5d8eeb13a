class Accounting::CreatePayablePayment
  NOTIFICATION_CUSTOMERS = %w[mavenprops].freeze

  extend Service

  def initialize(params, invoices, accounting_context, user)
    @params = params
    @invoices = invoices
    @accounting_context = accounting_context
    @user = user
  end

  def call
    if params.dig(:payment, :kind) == 'ach'
      create_electronic_payment.tap do |result|
        if result.successful?
          payment = result.payment

          if payment.payee.primary_contact&.email.present?
            ElectronicPaymentsMailer
              .electronic_deposit_initiated(payment)
              .deliver_later
          end

          notify_support(payment)
        end
      end
    else
      create_check_payment
    end
  end

  private

  attr_reader :params, :invoices, :accounting_context, :user

  def create_electronic_payment
    PaymentProcessing::CreateElectronicPayment.call \
      params: params,
      payee: payee,
      invoices: invoices,
      user: user,
      accounting_context: accounting_context,
      payment_method: payment_method,
      merchant_account: merchant_account,
      receivable: false
  end

  def create_check_payment
    payment = Payment.create!(payment_params) do |payment|
      payment.payer = accounting_context.entity
      payment.payee = payee
      payment.invoices = invoices
      payment.description = 'Payment' if payment.description.blank?
      payment.debit_bank_account = bank_account
      payment.date ||= Time.zone.today
      if payment.check_number.blank? && payment.check?
        payment.check_number = bank_account&.next_check_number
      end
    end

    OpenStruct.new(successful?: true, payment: payment)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  def payee
    invoices.first.seller
  end

  def bank_account
    BankAccount.find_by(id: params[:bank_account_id])
  end

  def payment_method
    payee.default_electronic_deposit_account
  end

  def merchant_account
    MerchantAccountsQuery
      .new(bank_account.merchant_accounts)
      .search
      .with_ach_credit
      .first!
  end

  # TODO: remove
  def notify_support(payment)
    return unless NOTIFICATION_CUSTOMERS.include?(Customer.current_subdomain)

    return unless payment.payer == Customer.current.client_entity

    return unless payment.payee.is_a?(Property) || payment.payee.is_a?(Company)

    support_user = PropertyManager.find(1)

    support_user.notifications.create!(
      resource: payment,
      kind: :electronic_payment_created,
      title: 'Electronic Disbursement Created',
      description: payment.payee.name,
      link: payment.send(:routes).accounting_payment_url(
        payment,
        subdomain: Customer.current_subdomain
      )
    )
  rescue StandardError => e
    Honeybadger.notify(e)
    nil
  end

  def payment_params
    params.require(:payment).permit(Payment::PERMISSABLE_ATTRIBUTES)
  end
end
