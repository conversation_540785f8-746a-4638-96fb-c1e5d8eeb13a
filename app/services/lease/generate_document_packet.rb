class Lease::GenerateDocumentPacket < Lease::GenerateDocument
  def initialize(lease:, templates:)
    @lease = lease
    @templates = templates
  end

  def call
    generate_docx(template: packet_tempfile)
  end

  private

  def packet_tempfile
    if page_tempfiles.many?
      Tempfile.new('lease_packet').tap do |tempfile|
        Omnidocx::Docx.merge_documents(
          page_tempfiles.map(&:path), tempfile.path, true
        )
      end
    else
      page_tempfiles.first
    end
  end

  def page_tempfiles
    @page_tempfiles ||= begin
      tempfiles = []

      @templates.sort_by(&:name).each do |template|
        tempfile = Down.download(template.direct_upload_url)

        if template.template_options['vehicle_count']
          vehicles.each do |vehicle|
            tempfiles << make_vehicle_page(tempfile, vehicle)
          end
        elsif (template.template_options['tenant_count'] || 1).to_i < tenant_count
          (0...tenant_count).each do |i|
            tempfiles << make_tenant_n(tempfile, i)
          end
        else
          tempfiles << tempfile
        end
      end

      tempfiles
    end
  end

  def tenant_count
    lease.tenants.count
  end

  def make_tenant_n(tempfile, n)
    output_file = Tempfile.new(%w[docx .docx])
    doc = DocxReplace::Doc.new(tempfile.path)
    doc.replace(/{tenants\[0\]/, "{tenants[#{n}]", true)
    doc.replace(/{vehicles\[0\]/, "{vehicles[#{n}]", true)
    doc.commit(output_file.path)
    output_file.open
    output_file.rewind
    output_file
  end

  def make_vehicle_page(tempfile, vehicle)
    vehicle_index = vehicles.find_index(vehicle)
    tenant = vehicle.owner
    tenant_index = lease.tenants.find_index(tenant)

    output_file = Tempfile.new(%w[docx .docx])
    doc = DocxReplace::Doc.new(tempfile.path)
    doc.replace(/{tenants\[0\]/, "{tenants[#{tenant_index}]", true)
    doc.replace(/{vehicles\[0\]/, "{vehicles[#{vehicle_index}]", true)
    doc.commit(output_file.path)
    output_file.open
    output_file.rewind
    output_file
  end
end
