class Lease::GenerateDocument
  extend Service
  include PdfFilling
  include DocxFilling

  attr_reader :lease, :template

  def initialize(lease:, template:)
    @lease = lease
    @template = template
  end

  def call
    tempfile = Down.download(template.direct_upload_url)

    if docx?(tempfile)
      generate_docx(template: tempfile)
    else
      generate_pdf(template: tempfile.path)
    end
  end

  protected

  def pdf_values
    values = {
      created_at: lease.created_at.to_fs(:human_date),
      execution_date: execution_date.to_fs(:human_date),
      tenant_names: tenants.map(&:name).to_sentence,
      occupant_names: occupant_names,
      non_occupant_names: non_occupant_names,
      unit_name: unit.name,
      unit_address: unit.address.simple_address,
      unit_square_feet: unit.square_feet,
      unit_bedrooms: unit.bedrooms,
      unit_bathrooms: unit.bathrooms,
      unit_price: unit.price.format,
      property_name: property.name,
      property_address: property_address,
      property_phone: property.formatted_phone,
      start_date: lease.start_date.to_datetime.to_fs(:human_date),
      end_date: lease.end_date.to_datetime.to_fs(:human_date),
      occupant_count: occupant_count,
      adult_count: adult_count,
      minor_count: minor_count,
      first_month: first_month.to_datetime.to_fs(:short_date),
      term_length: term_length,
      entity_name: property.company.name,
      academic_year: academic_year
    }

    {
      todays_date: Time.zone.today,
      start_date: lease.start_date,
      end_date: lease.end_date,
      execution_date: execution_date
    }.each do |key, value|
      values[key.to_s] = value.to_fs(:human_date)
      values["#{key}.month_name"] = value.strftime('%B')
      values["#{key}.day_ordinal"] = value.day.ordinalize
      values["#{key}.year"] = value.year
    end

    values['rent_due_day'] = rent_due_day
    values['rent_due_day.ordinal'] = rent_due_day.ordinalize

    values['unit.floor'] = unit.floor
    values['unit.floor_ordinal'] = unit.floor&.ordinalize

    {
      security_deposit: security_deposit,
      monthly_amount: monthly_amount,
      monthly_rent: monthly_rent,
      current_deposit: current_deposit,
      due_at_move_in: due_at_move_in,
      prorated_rent: prorated_rent,
      term_amount: term_amount,
      individual_term_amount: individual_term_amount
    }.each do |key, value|
      values[key] = value.format
      values["#{key}.expanded"] = expanded_dollar_amount(value)
    end

    values['installment_count'] = installment_count
    values['installment_count.expanded'] = installment_count.humanize

    lease.charge_schedule_entries.recurring.each do |charge|
      charge_name = charge.name.parameterize.underscore
      account_name = charge.account.name.parameterize.underscore
      monthly_amount = charge.amount
      term_amount = charge.term_amount

      # TODO: Handle duplicate / time range dependent entries of the same charge
      [charge_name, account_name].each do |name|
        key = "monthly_charges[#{name}]"
        values[key] ||= monthly_amount.format
        values["#{key}.expanded"] ||= expanded_dollar_amount(monthly_amount)
        values["#{key}.individual_distribution"] ||= \
          charge.allocations.map do |ca|
            "#{ca.lease_membership.name} (#{ca.amount.format})"
          end.to_sentence

        key = "term_amount[#{name}]"
        values[key] = term_amount.format
        values["#{key}.expanded"] = expanded_dollar_amount(term_amount)
        values["#{key}.individual_distribution"] = \
          charge.allocations.map do |ca|
            "#{ca.lease_membership.name} (#{ca.term_amount.format})"
          end.to_sentence
      end
    end

    lease.charge_schedule_entries.one_time.each do |charge|
      charge_name = charge.name.parameterize.underscore
      account_name = charge.account.name.parameterize.underscore
      amount = charge.amount

      [charge_name, account_name].each do |name|
        key = "one_time_charges[#{name}]"
        values[key] = amount.format
        values["#{key}.expanded"] = expanded_dollar_amount(amount)
        values["#{key}.individual_distribution"] = \
          charge.allocations.map do |ca|
            "#{ca.lease_membership.name} (#{ca.amount.format})"
          end.to_sentence
      end
    end

    damage_presets = configuration
                     .charge_presets
                     .where.not(kind: %i[application lease rent])
    damage_presets.each do |preset|
      key = "charges[#{preset.name.parameterize.underscore}]"
      values[key] = preset.amount.format
      values["#{key}.expanded"] = expanded_dollar_amount(preset.amount)
    end

    lease.lease_memberships.each.with_index do |membership, i|
      tenant = membership.tenant
      tenant_key = "tenants[#{i}]"

      values["#{tenant_key}.name"] = tenant.name
      values["#{tenant_key}.phone"] = \
        tenant.phone&.phony_formatted(normalize: :US)
      values["#{tenant_key}.email"] = tenant.email
      values["#{tenant_key}.date_of_birth"] = \
        tenant.date_of_birth&.to_datetime&.to_fs(:human_date)
      values["#{tenant_key}.age"] = tenant.age
      values["#{tenant_key}.move_in_date"] = \
        membership.move_in_date.to_datetime.to_fs(:human_date)
      values["#{tenant_key}.move_out_date"] = \
        membership.move_out_date.to_datetime.to_fs(:human_date)

      # Tenant Metadata
      tenant.metadata_values.each do |key, value|
        values["#{tenant_key}.meta[#{key}]"] = value
      end

      app_membership = tenant.lease_application_memberships.last
      if app_membership
        application = app_membership.lease_application
        applicant = app_membership.applicant
        values["#{tenant_key}.ssn"] = applicant&.social_security_number
        values["#{tenant_key}.date_of_birth"] = applicant&.birth_date
        values["#{tenant_key}.drivers_license"] = \
          applicant&.drivers_license_number
        values["#{tenant_key}.address"] = applicant&.address&.simple_address
        values["#{tenant_key}.mobile_phone"] = applicant&.mobile_phone
        values["#{tenant_key}.home_phone"] = applicant&.home_phone
        values["#{tenant_key}.relation_to_primary_applicant"] = \
          applicant&.relation_to_primary_applicant
      end

      if lease.renewal?
        values["#{tenant_key}.address"] = lease.unit.address.simple_address
      end

      values["#{tenant_key}.forwarding_address"] = \
        tenant.forwarding_address&.simple_address

      if tenant.taxpayer_identification
        values["#{tenant_key}.ssn"] ||= tenant.taxpayer_identification.tin
        values["#{tenant_key}.ssn_last_four"] = \
          tenant.taxpayer_identification.tin[-4..]
      end
    end

    if application
      application.emergency_contacts.each.with_index do |contact, i|
        values["emergency_contacts[#{i}].name"] = contact.name
        values["emergency_contacts[#{i}].phone"] = contact.formatted_phone
        values["emergency_contacts[#{i}].relation"] = contact.relation
      end

      values['pets'] = application.pets if application.version < 5
    end

    pets = Pet.where(id: lease.pets)

    if application && application.version >= 5
      pets = pets.or(Pet.where(lease_application: application))
    end

    pets.each.with_index do |pet, index|
      values["pets[#{index}].name"] = pet.name
      values["pets[#{index}].type"] = pet.kind.titleize
      values["pets[#{index}].breed"] = if pet.kind_other?
                                         pet.kind_detail
                                       else
                                         pet.breed
                                       end
      values["pets[#{index}].age"] = pet.age
      values["pets[#{index}].weight"] = pet.weight
      values["pets[#{index}].color"] = pet.color
      values["pets[#{index}].service_animal"] = pet.service_animal
    end

    vehicles.each.with_index do |vehicle, i|
      reservation = vehicle.parking_reservation

      values["vehicles[#{i}].make"] = vehicle.make
      values["vehicles[#{i}].model"] = vehicle.model
      values["vehicles[#{i}].year"] = vehicle.year
      values["vehicles[#{i}].color"] = vehicle.color
      values["vehicles[#{i}].license_plate_state"] = vehicle.state
      values["vehicles[#{i}].license_plate_number"] = \
        vehicle.license_plate
      values["vehicles[#{i}].parking_space"] = reservation&.name
      values["vehicles[#{i}].parking_space_address"] = \
        reservation&.address&.simple_address
      values["vehicles[#{i}].start_date"] = \
        reservation&.start_date || lease.start_date
      values["vehicles[#{i}].end_date"] = \
        reservation&.end_date || lease.end_date
    end

    # Utilities
    Utilities::UTILITY_TYPES.each do |type|
      key = "#{type}_utility"
      values[key] = property.send(key)&.name
    end

    # Lease Metadata
    lease.metadata_values.each do |key, value|
      values["meta[#{key}]"] = value
    end

    # TODO: Remove
    if Customer.current_subdomain == 'pmi-utah' && lease.id == 81
      values['monthly_charges[rent]'] = '$1,500.00'
    end

    values.transform_values! { |v| v.nil? ? '' : v }

    values
  end

  private

  def application
    app = nil

    lease.tenants.each do |tenant|
      app_membership = tenant.lease_application_memberships.last
      app = app_membership.lease_application if app_membership
    end

    app
  end

  def security_deposit
    lease.security_deposit_amount
  end

  def first_month
    start = lease.start_date
    if start.beginning_of_month == start
      start
    else
      start.next_month.beginning_of_month
    end
  end

  def monthly_amount
    lease.amount
  end

  def monthly_rent
    account = configuration.rent_preset&.account
    amounts = lease.charge_schedule_entries
                   .recurring
                   .where(account: account)
                   .map(&:amount)
    Money.sum(amounts)
  end

  def current_deposit
    tenant = lease.tenants.first
    credits = tenant.paid_payments.where('payments.date <= ?', lease.start_date)
    Money.sum(credits.map(&:credit))
  end

  def due_at_move_in
    if Customer.current_subdomain == 'ccp'
      Accounting::Prorater.new(lease, lease.start_date).call(monthly_rent)
    elsif Customer.current_subdomain == 'shamrock'
      monthly_amount + security_deposit - current_deposit
    elsif Customer.current_subdomain == 'bueno'
      today = Time.zone.today
      if today.next_month.beginning_of_month - today < 6
        security_deposit + prorated_rent + monthly_amount - current_deposit
      else
        security_deposit + prorated_rent - current_deposit
      end
    elsif Customer.current_subdomain == 'gebrael'
      monthly_amount + lease.move_in_costs_amount
    elsif Customer.current_subdomain == 'grawood-pm'
      monthly_amount + security_deposit
    elsif Customer.current_subdomain.start_with?('pmi')
      monthly_amount + security_deposit
    elsif Customer.current_subdomain == 'senihmgt'
      prorated_rent + lease.move_in_costs_amount
    else
      monthly_amount + security_deposit + prorated_rent - current_deposit
    end
  end

  def prorated_rent
    if lease.start_date.day == 1 # Full month, no proration
      Money.zero
    else
      Accounting::Prorater.new(lease, lease.start_date).call(monthly_rent)
    end
  end

  def property_address
    # TODO: not really, but for shelby manor.
    if Customer.current_subdomain == 'shamrock'
      unit.address.dup.tap do |address|
        address.line_two = nil
      end.simple_address
    else
      property.address.simple_address
    end
  end

  def execution_date
    lease.created_at
  end

  def term_length
    duration = ActiveSupport::Duration.build(
      lease.end_date.to_datetime.to_i - lease.start_date.to_datetime.to_i
    )

    parts = duration.parts.slice(:years, :months, :weeks)

    # Yeah.
    if Customer.current_subdomain == 'brown' &&
       parts[:months] && parts[:months] > 5
      case parts[:weeks]
      when 3
        parts[:months] += 1
        parts.delete(:weeks)
      when 1
        parts.delete(:weeks)
      end
    end

    parts.map do |k, v|
      "#{v} #{k.to_s.pluralize(v)}"
    end.join(', ')
  end

  def term_months
    [*lease.start_date..lease.end_date].map(&:beginning_of_month).uniq
  end

  def installment_count
    term_months.count
  end

  def individual_term_amount
    term_amount / tenants.count.to_f
  end

  def academic_year
    year = lease.start_date.year
    month = lease.start_date.month

    if month >= 6
      [year, year + 1].join(' - ')
    else
      [year - 1, year].join(' - ')
    end
  end

  def vehicles
    @vehicles ||= begin
      vq = Vehicle.left_joins(:parking_reservation)
      vehicles = vq.where(parking_reservations: { tenant: lease.tenants })

      # if application
      #   vehicles = vehicles.or(vq.where(lease_application: application))
      # end

      vehicles = vehicles.order(created_at: :desc).uniq

      # Keep vehicles aligned with tenant spots on multiple documents
      if lease.tenants.many?
        vehicles.sort_by! do |v|
          lease.lease_memberships.find_by(tenant_id: v.owner.id).id
        end
      end

      vehicles
    end
  end

  def docx?(file)
    Marcel::MimeType.for(file) == Mime::Type.lookup_by_extension(:docx)
  end

  def docx_values
    pdf_values
  end

  def expanded_dollar_amount(amount)
    dollars = amount.to_i

    cents = (amount % amount.currency.subunit_to_unit).cents

    str = "#{dollars.humanize.titleize} Dollars"
    str += " and #{cents.humanize.titleize} Cents" if cents.positive?

    str
  end

  def occupant_count
    lease_memberships.reject(&:guarantor?).count
  end

  def adult_count
    tenants.count { |t| t.age.nil? || t.age >= 18 }
  end

  def minor_count
    tenants.count { |t| t.age && t.age < 18 }
  end

  def rent_due_day
    configuration.rent_due_day
  end

  def occupant_names
    lease_memberships.reject(&:guarantor?).map(&:name).to_sentence
  end

  def non_occupant_names
    lease_memberships.select(&:guarantor?).map(&:name).to_sentence
  end

  delegate :tenants, :unit, :property, :configuration, :lease_memberships,
           :term_amount,
           to: :lease
end
