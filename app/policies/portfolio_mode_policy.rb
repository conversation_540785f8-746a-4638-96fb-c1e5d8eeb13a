##
# For a given property manager, determines the content and destination of the
# portfolio sidebar tab and property content top tab. This is so that users who
# only have access to one property are taken there immediately, users who have
# access to no groups only see properties, and users with many groups are taken
# there by default. Also controls some breadcrum backlinks.
class PortfolioModePolicy
  attr_reader :user, :portfolio_text, :portfolio_path

  def initialize(user)
    @user = user

    @show_portfolio_link = true

    if @user.portfolios.many?
      @portfolio_text = 'Portfolios'
      @portfolio_path = routes.portfolios_path
    elsif direct_membership?
      @show_portfolio_link = false
      @portfolio_text = 'Property'
      @portfolio_path = routes.property_path(@user.properties.first)
    else
      @portfolio_text = 'Properties'
      @portfolio_path = routes.properties_path
    end
  end

  # Whether or not there should be a backlink to the portfolio in properties,
  # units, tenants pages
  def show_portfolio_link?
    @show_portfolio_link
  end

  private

  def routes
    Rails.application.routes.url_helpers
  end

  def direct_membership?
    @user.property_memberships.one? &&
      @user.property_memberships.first.target_type == 'Property'
  end
end
