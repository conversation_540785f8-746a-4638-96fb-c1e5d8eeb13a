class Payment::Voidable
  def self.voidable?(payment)
    new(payment).voidable?
  end

  attr_reader :payment

  def initialize(payment)
    @payment = payment
  end

  def voidable?
    return false if payment.reversed?
    return true if payment.check?
    return approved_status? if payment.profit_stars?
    return zeamster_voidable? if payment.zeamster?
    return paylease_voidable? if payment.pay_lease?
  end

  private

  def approved_status?
    payment.profit_stars_transaction.status == 'Approved'
  end

  def zeamster_voidable?
    payment.zeamster_transaction.pending_origination? ||
      payment.zeamster_transaction.approved?
  end

  def paylease_voidable?
    payment.pay_lease_transaction.response_code == 2 # processing
  end
end
