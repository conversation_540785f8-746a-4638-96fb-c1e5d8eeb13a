class AgreementDecorator < ApplicationDecorator
  delegate_all

  def description
    'Membership'
  end

  def member_description
    'Contact'
  end

  def header
    "#{description} Summary"
  end

  def subheader
    name
  end

  def formatted_start_date
    start_date&.to_fs(:short_date) || 'Unspecified'
  end

  def formatted_end_date
    end_date&.to_fs(:short_date) || 'Unspecified'
  end

  def ledger_button_filters
    case object
    when Lease
      # TODO: Remove, associate membership with sublease
      if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]) &&
         (agreement = object.primary_tenant.simple_agreements.last)
        return { simple_agreement_id: agreement.id }
      end

      { lease_id: object.id }
    when Agreements::SimpleAgreement
      { simple_agreement_id: object.id }
    else
      { tenant_id: object.primary_tenant.id }
    end
  end
end
