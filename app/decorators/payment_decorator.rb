class PaymentDecorator < ApplicationDecorator
  delegate_all

  def status_icon
    case status
    when 'completed'
      'checkmark'
    when 'pending'
      'minus'
    when 'processing'
      'spinner'
    when 'failed', 'canceled'
      'remove'
    end
  end

  def status_color
    case status
    when 'completed'
      'green'
    when 'pending'
      'yellow'
    when 'processing'
      'blue'
    when 'failed', 'canceled'
      'red'
    end
  end

  def status_text
    if profit_stars?
      profit_stars_transaction.status_text
    elsif zeamster?
      zeamster_transaction.status.titleize
    elsif pay_lease?
      pay_lease_transaction.status_text
    else
      status.capitalize
    end
  end

  def status_summary
    if profit_stars?
      profit_stars_transaction.status_summary
    elsif zeamster?
      zeamster_transaction.status_summary
    elsif pay_lease?
      pay_lease_transaction.status_summary
    end
  end

  def kind_icon
    case kind
    when 'check'
      'mail outline'
    when 'ach'
      'exchange'
    when 'credit'
      'credit card'
    end
  end

  def kind_text
    case kind
    when 'ach' then 'ACH'
    when 'credit' then 'Card Payment'
    else
      kind.titleize
    end
  end

  def reference_text
    if check_number.present?
      if check?
        "Check #{check_number}"
      else
        check_number
      end
    elsif short_reference_number
      if ach?
        "ACH #{short_reference_number}"
      else
        "CC #{short_reference_number}"
      end
    else
      "# #{id}"
    end
  end

  def formatted_reference_number
    reference_number.presence&.upcase
  end

  def short_reference_number
    formatted_reference_number&.last(4)
  end
end
