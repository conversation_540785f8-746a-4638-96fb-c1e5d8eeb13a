class TenantDecorator < ApplicationDecorator
  include TaxpayerDecorating

  delegate_all

  def ledger_button_filters
    agreement_ledger_filters || lease_ledger_filters || tenant_ledger_filters
  end

  def assigned_onboarding
    return @assigned_onboarding if defined?(@assigned_onboarding)

    onboarding_resolver = MemberOnboarding::Tenants::Resolver.new(self).tap(&:call)
    @assigned_onboarding = onboarding_resolver.onboarding
  end

  # We can't remove MemberOnboarding::Completion yet since default onboarding
  # doesn't create an Assignment record yet
  # so there's no way to know if a member completed a default onboarding
  def assigned_onboarding_completed?
    return @assigned_onboarding_completed if defined?(@assigned_onboarding_completed)

    @assigned_onboarding_completed = MemberOnboarding::Completion.exists?(
      tenant: self,
      configuration: assigned_onboarding
    )
  end

  def assigned_onboarding_explicitly_assigned?
    active_onboarding_assignment.present?
  end

  def onboarding_status_icon
    return nil if assigned_onboarding.nil?

    assigned_onboarding_completed? ? 'check' : 'hourglass outline'
  end

  private

  def agreement_ledger_filters
    return nil unless CustomerSpecific::Behavior.member_ledgers_enabled?

    agreement = simple_agreements.last

    return nil unless agreement

    { simple_agreement_id: agreement.id } if agreement
  end

  def lease_ledger_filters
    lease = current_lease

    { lease_id: lease.id } if lease
  end

  def tenant_ledger_filters
    { tenant_id: id }
  end
end
