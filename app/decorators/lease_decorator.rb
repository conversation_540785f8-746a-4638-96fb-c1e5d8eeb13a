class LeaseDecorator < AgreementDecorator
  def title
    tenants.map(&:name).join(', ')
  end

  def subtitle
    "#{property.name} #{unit.name}"
  end

  def term
    "#{h.day start_date} to #{h.day end_date}"
  end

  def duration
    days = (lease.end_date - lease.start_date).to_i
    months = (days / 30.4).round.to_i

    if months.positive?
      "#{months} Month #{I18n.t('lease_term')}"
    else
      "#{days} Day #{I18n.t('lease_term')}"
    end
  end

  def renewal_status
    if lease.renewed?
      lease.renewal.start_date.to_fs(:short_date)
    elsif lease.month_to_month?
      'Month to Month'
    elsif lease.rollover?
      'Rollover'
    else
      'None'
    end
  end

  def tenant_portal_description
    desc = if Customer.current.home_owners_association? || Customer.current_subdomain.start_with?('chio-mu')
             'agreement'
           else
             'lease'
           end

    if end_date.past?
      "Your #{desc} expired on #{lease.end_date.to_fs(:human_date)}."
    elsif month_to_month?
      "Your #{desc} will autorenew on #{lease.end_date.to_fs(:human_date)}."
    elsif rollover? && end_date.between?(Time.zone.now, 30.days.from_now)
      "Your #{desc} will become month-to-month after #{lease.end_date.to_fs(:human_date)}."
    else
      "You have #{h.time_ago_in_words(lease.end_date)} left on your #{desc}."
    end
  end

  def description
    if Customer.current.home_owners_association?
      'Agreement'
    else
      I18n.t('lease_term')
    end
  end

  def member_description
    I18n.t('tenant_term')
  end

  def subheader
    "#{duration}, #{kind.titleize}"
  end
end
