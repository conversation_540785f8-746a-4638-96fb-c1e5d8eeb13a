class InvoiceDecorator < ApplicationDecorator
  delegate_all

  def status
    @status ||= if paid?
                  'Paid'
                elsif waived?
                  'Waived'
                elsif Time.zone.today > due_date
                  # TODO: Use late?
                  # Note: This is wrong across time zones
                  'Overdue'
                else
                  "Due #{human_due_time}"
                  # Why NOOP?
                end
  end

  def human_due_time
    if due_date.today?
      'today'
    elsif due_date.tomorrow?
      'tomorrow'
    else
      "in #{h.time_ago_in_words(due_date.end_of_day)
             .titleize
             .gsub('About ', '')
             .gsub('Almost', '')
             .gsub('Less Than', '')
             .gsub('Over', '')}"
    end
  end

  def status_color
    case status
    when 'Paid' then 'green'
    when /Due/ then 'orange'
    when 'Overdue' then 'red'
    when 'Waived' then 'blue'
    end
  end
end
