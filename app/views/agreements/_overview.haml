:ruby
  monthly_amount = if agreement.is_a?(Lease)
                     recurring_charges = agreement.charge_schedule.active_recurring_entries
                     Money.sum(recurring_charges.map(&:amount))
                   else
                     Money.zero
                   end

%h4.ui.horizontal.divider.header{ style: 'margin-top: 3em' }
  Overview

.ui.two.column.divided.stackable.tiny.statistics.grid
  - unless agreement.instance_of?(Lending::Loan)
    .statistic.column
      .value
        = link_to_if current_property_manager,
          agreement.property,
          property_path(agreement.property)
      .label Property
    - if agreement.instance_of?(Lease)
      .statistic.column
        .value
          = link_to_if current_property_manager,
            agreement.unit,
            unit_path(agreement.unit)
        .label Unit
  - else
    .statistic.column
      .value= link_to agreement.borrower, company_path(agreement.borrower)
      .label Borrower
    .statistic.column
      .value= link_to agreement.property, property_path(agreement.property)
      .label Property

.ui.divider
.ui.equal.width.column.divided.stackable.tiny.statistics.grid
  .statistic.column
    .value
      = agreement.start_date&.strftime('%-m/%-d/%Y') || 'Unspecified'
    .label= agreement.instance_of?(Lending::Loan) ? 'Commencement Date' : 'Start Date'
  .statistic.column
    .value
      = agreement.end_date&.strftime('%-m/%-d/%Y') || 'Unspecified'
    .label= agreement.instance_of?(Lending::Loan) ? 'Maturity Date' : 'End Date'

  - unless Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]) && !current_property_manager
    - unless agreement.instance_of?(Lending::Loan)
      .statistic.column
        .value= monthly_amount.format
        .label Monthly Amount
      .statistic.column
        .value
          - one_time_charges = agreement.charge_schedule_entries.where(recurring: false)
          = Money.sum(one_time_charges.map(&:amount)).format
        .label One Time Fees
    - else
      .statistic.column
        .value
          = "#{number_with_precision(agreement.term_length, precision: 1, strip_insignificant_zeros: true)} Years"
        .label Term Length
