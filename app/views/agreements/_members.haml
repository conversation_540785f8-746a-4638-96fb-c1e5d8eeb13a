%h4.ui.horizontal.divider.header{ style: 'margin-top: 3em' }
  = agreement.member_description.pluralize
- agreement.lease_memberships.each do |membership|
  %h3.ui.header
    - tenant = membership.tenant
    = link_to_if current_property_manager,
      tenant.name,
      tenant_path(tenant)
    .sub.header
      - if agreement.instance_of?(Lease)
        = membership.role.titleize
        %br
      = membership.tenant.email
  - if agreement.instance_of?(Lease) && !membership.guarantor?
    .ui.bulleted.list
      .item
        Move In Date: #{membership.move_in_date.strftime('%-m/%-d/%Y')}
      .item
        Move Out Date: #{membership.move_out_date.strftime('%-m/%-d/%Y')}
      - if current_property_manager && membership.itemized_damages.present?
        = link_to 'Itemized Damages',
          leasing_lease_itemized_damage_path(agreement, membership.itemized_damages),
          class: 'item'

- if agreement.is_a?(Agreements::SimpleAgreement)
  - agreement.memberships.each do |membership|
    %h3.ui.header
      - member = membership.tenant
      = link_to_if current_property_manager,
        member.name,
        tenant_path(member)
      .sub.header
        = membership.role.titleize
        %br
        = member.email
