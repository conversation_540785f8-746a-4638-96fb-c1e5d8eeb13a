- s.with_item title: 'Property' do
  = link_to listing.property.name, listing.property.url

- unless listing.property.single_unit_type?
  - s.with_item title: 'Floorplan' do
    = link_to listing.floorplan.name, listing.property.url

- s.with_item title: 'Published Since' do
  - if listing.published?
    = listing.published_at.to_fs(:short_date)
  - else
    Unpublished

- s.with_item title: 'Days on Market' do
  - if listing.published?
    = listing.days_on_market
  - else
    Unpublished

- s.with_item title: 'Availability' do
  - date = listing.date_available

  - if date.nil?
    Unavailable
  - elsif date <= Time.zone.now
    Available Now
  - else
    = listing.date_available.to_fs(:short_date)
    (in #{time_ago_in_words(listing.date_available)})

- s.with_item title: 'Listing Agent' do
  - if listing.agent
    = link_to listing.agent.name, listing.agent.url
  - else
    Unspecified

- s.with_item title: 'Market Rate' do
  = listing.price.format
  &nbsp;
  (#{listing.price_per_square_foot.format} / sqft.)

- s.with_item title: 'Layout' do
  = listing.short_layout_description
