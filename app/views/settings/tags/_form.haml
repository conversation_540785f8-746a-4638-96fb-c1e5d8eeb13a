:ruby
  id = if tag.persisted?
         :edit_tag_form
       else
         :new_tag_form
       end

= form_with id: id, model: [:settings, tag], class: 'ui form', local: false do |f|

  = hidden_field_tag :type, params[:type]

  .required.field
    = f.label :tag
    = f.text_field :tag, class: 'ui input', required: true

  - if tag.new_record?
    .required.field
      = f.label :taggable_type, 'Type'
      = f.semantic_dropdown :taggable_type,
      tabs.map { |tab| [tab.title, tab.taggable_type] },
      { selected: @tab.taggable_type },
      class: 'ui search selection dropdown'

  - if CustomerSpecific::Behavior.property_scoped_tags?
    .field
      = f.label :property_id, 'Property'

      = f.semantic_dropdown :property_id,
        [['All Properties', nil]] + current_property_manager.properties.unarchived.pluck(:name, :id),
        { selected: tag.property&.id },
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'All Properties' }, property_tag_dropdown: true }

  .ui.bottom.embedded.error.message


:javascript
  initializeSemanticFields();

- if CustomerSpecific::Behavior.property_scoped_tags?
  :javascript
    if ($('#type').val() !== 'agreements_simple_agreements') {
      $('[data-property-tag-dropdown]').closest('.field').hide();
    } else {
      $('[data-property-tag-dropdown]').closest('.field').show();
    }

    $('#tag_taggable_type').on('change', function() {
      $('[data-property-tag-dropdown]').dropdown('clear');

      var selectedType = $(this).val();

      if (selectedType === 'Agreements::SimpleAgreement') {
        $('[data-property-tag-dropdown]').closest('.field').slideDown(100);
      } else {
        $('[data-property-tag-dropdown]').closest('.field').slideUp(100);
      }
    });
