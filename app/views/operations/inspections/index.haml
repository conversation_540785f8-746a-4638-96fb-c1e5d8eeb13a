.action-bar
  .ui.container
    %h1.ui.header
      = t('activerecord.models.inspection/report').pluralize

    .action.buttons
      - unless current_property_manager.role.in?(['House Director', 'Chapter Advisor / Officer', 'House Corporation Board Member']) && CustomerSpecific::Behavior.disable_based_on_role_name?
        = link_to operations_inspection_templates_path,
          class: 'ui button' do
          Manage Templates

      - if Feature.enabled?(:inspections_importer, Customer.current)
        - require_permission :export_operations_inspections? do
          = export_button

        - require_permission :import_operations_inspections? do
          = link_to organization_data_imports_inspections_path,
            class: 'ui button' do
            %i.upload.icon
            Import

      = link_to new_operations_inspection_path, class: 'ui button' do
        %i.plus.icon
        New
        = t('activerecord.models.inspection/report')

.action-container
  .ui.container{ style: 'margin: 1em auto;' }
    = render 'flash_notices'

    - if @all_inspections.none?
      = render EmptyStateComponent.new(icon: 'tasks') do |component|
        - component.with_header do
          = t('activerecord.models.inspection/report').pluralize
          analyze the health of your current and prospective assets.
        - component.with_paragraph do
          You will see
          = t('activerecord.models.inspection/report').pluralize.downcase
          appear here automatically for certain
          scheduled events, such as move-outs, as well as inspections that you
          create manually, such as annual reviews, certifications, or prospect
          analysis.
        - component.with_paragraph do
          Before you begin, you can also review our
          = succeed '.' do
            = link_to "#{t('activerecord.models.inspection/report').pluralize.downcase} documentation",
                        '/kb/categories/inspections', target: '_blank'
        = link_to "Prepare an #{t('activerecord.models.inspection/report')}",
            new_operations_inspection_path,
            class: 'ui primary button'
    - else
      = ActionIndex::Inspections::Reports.(self,
        collection: @filtered_inspections,
        user: current_property_manager,
        partial: { path: 'operations/inspections/table',
        locals: { inspections: @filtered_inspections } })

= render 'shared/confirm_bulk_archiving_modal'
