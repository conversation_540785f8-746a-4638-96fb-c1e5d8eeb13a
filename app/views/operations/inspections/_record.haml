- questions = report.template.questions.reorder(section: :asc, id: :asc)

.ui.vertical.segment#inspection-tables
  %h2.ui.header
    #{index + 1}. #{record.target.service_area.name}

  - if record.comments.present?
    %h4.ui.header
      Service Area Comments
    %p
      = simple_format(record.comments)

  - if record.property
    = render partial: 'operations/inspections/category',
      locals: { name: 'Property',
      questions: questions.property,
      record: record,
      room_index: 0 }

  - if record.unit
    = render partial: 'operations/inspections/category',
      locals: { name: 'Unit',
      questions: questions.unit,
      record: record,
      room_index: 0 }

    - record.bedroom_count.times do |i|
      = render partial: 'operations/inspections/category',
        locals: { name: "Bedroom #{i + 1}",
        questions: questions.bedroom,
        record: record,
        room_index: i }

    - record.bathroom_count.times do |i|
      = render partial: 'operations/inspections/category',
        locals: { name: "Bathroom #{i + 1}",
        questions: questions.bathroom,
        record: record,
        room_index: i }
