= form_with model: [:operations, inspection_template],
  html: { class: 'ui form' } do |f|

  .field
    = f.label :name
    = f.text_field :name,
      placeholder: 'e.g. Move-In Inspection, Property Walk, Exterior Inspection, etc.'

  - if inspection_template.new_record?
    .clearfix
      = f.submit 'Next', class: 'right floated ui button'

  - if inspection_template.persisted?
    .clearfix
      = f.submit 'Save', class: 'right floated ui button'

- if inspection_template.persisted?
  = render partial: 'category_tabs'
