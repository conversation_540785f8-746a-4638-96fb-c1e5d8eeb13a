.action-bar
  .ui.container
    %h1.ui.header
      Receivables
    .action.buttons
      - unless current_property_manager.role.name.in?(['House Director', 'Chapter Advisor / Officer', 'House Corporation Board Member']) && CustomerSpecific::Behavior.disable_based_on_role_name?
        - require_permission :record_income_for_accounting_receivables? do
          - unless Feature.enabled?(:record_nontenant_revenue, Customer.current)
            = link_to 'Record Income', new_accounting_receivables_income_path,
              class: 'ui button'
        - require_permission :create_bill_for_accounting_receivables? do
          = link_to 'Create Bill', new_accounting_receivables_invoice_path,
            class: 'ui button'
      - unless current_property_manager.role.name.in?(['Chapter Advisor / Officer'])
        - require_permission :export_accounting_receivables? do
          = export_button

.action-container{ style: 'padding-top: 1em' }
  .ui.wide.container
    .ui.container
      = render 'flash_notices'

    %div{ style: 'padding: 0 2rem;' }
      = ActionIndex::Accounting::Receivables.(self,
        collection: @invoices,
        user: current_property_manager,
        partial: { path: 'accounting/shared/invoices/table',
        locals: { invoices: @invoices,
        accounting_side: accounting_side } })

      %div{ style: 'padding-top: 1rem;' }
        = paginate @invoices
