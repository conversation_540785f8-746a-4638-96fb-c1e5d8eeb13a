.action-bar
  .ui.container
    %h1.ui.header
      Payables
    .action.buttons
      - unless current_property_manager.role.name.in?(['House Director', 'Chapter Advisor / Officer', 'House Corporation Board Member']) && CustomerSpecific::Behavior.disable_based_on_role_name?
        - require_permission :show_accounting_payables_batches? do
          = link_to 'Batches', accounting_payables_batches_path,
            class: 'ui button'
        - require_permission :manage_invoice_payments_for_accounting_payables? do
          = link_to 'Make Payment', new_accounting_payables_payment_path,
            class: 'ui button'
        - require_permission :create_accounting_payables? do
          = link_to 'Enter Invoice', new_accounting_payables_invoice_path,
            class: 'ui button'
        - require_permission :manage_credits_against_accounting_payables? do
          = link_to 'Enter Credit', new_accounting_payment_path(payment_kind: :credit_note),
            class: 'ui button'
      - unless current_property_manager.role.name.in?(['Chapter Advisor / Officer']) && CustomerSpecific::Behavior.disable_based_on_role_name?
        - require_permission :export_accounting_payables? do
          = export_button

.action-container{ style: 'padding-top: 1em' }
  .ui.wide.container
    .ui.container
      = render 'flash_notices'

    %div{ style: 'padding: 0 2rem;' }
      = ActionIndex::Accounting::Payables.(self,
        collection: @invoices,
        user: current_property_manager,
        partial: { path: 'accounting/shared/invoices/table',
        locals: { invoices: @invoices,
        accounting_side: accounting_side } })

      %div{ style: 'padding-top: 1rem;' }
        = paginate @invoices
