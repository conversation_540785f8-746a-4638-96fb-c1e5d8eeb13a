= form_for [:accounting, budget], html: { class: 'ui form' } do |f|
  .required.field
    = f.label :company_id, 'Entity'
    = f.semantic_dropdown :company_id,
      [['Select', nil]] + Company.pluck(:name, :id),
      {},
      data: { options: { placeholder: 'Select' } },
      class: 'ui search selection dropdown'

  - if budget.company.present?
    .field
      = f.label :property_id, 'Property'
      = f.semantic_dropdown :property_id,
        [['Entity Only', nil]] + budget.company.properties.pluck(:name, :id),
        {},
        data: { options: { placeholder: 'Entity Only' } },
        class: 'ui search selection dropdown'

  .required.field
    = f.label :year, 'Fiscal Year'
    = f.number_field :year

  .required.field
    = f.label :file, 'Budget File'
    %i
      An .xlsx file containing a yearly budget. Click
      = link_to 'here',
        template_accounting_budgets_path(company_id: budget.company_id, format: :xlsx)
      to download a template.
    %br
    %br
    = f.file_field :file

  .clearfix
    = f.submit class: 'ui primary submit button'

:javascript
  $('#budget_company_id').dropdown('setting', 'onChange', function(id) {
    $.get('/accounting/budgets/new.js?company_id=' + id);
  });
