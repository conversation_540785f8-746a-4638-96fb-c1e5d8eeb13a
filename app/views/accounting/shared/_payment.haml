.printable.paper
  %img.ui.right.floated.tiny.image{ src: Customer.current.logo_url }

  .clearfix
    %h1.ui.header
      = payment.description.titleize
      .sub.header{ title: "Created #{payment.created_at.to_fs(:short_datetime)}" }
        - unless payment.credit_note?
          Payment
        Receipt from #{day payment.date}
        - if payment.check_number.present?
          %br
          = payment.decorate.kind_text
          = payment.check_number

  %br

  .ui.four.column.stackable.grid
    .column
      .ui.list
        .item
          .content
            .header
              - if payment.credit_note?
                Credit To
              - else
                From
            .description
              = render partial: 'shared/invoice/billing_address',
                locals: { addressable: payment.payer }
    .column
      .ui.list
        .item
          .content
            .header
              - if payment.credit_note?
                From
              - else
                To
            .description
              = render partial: 'shared/invoice/billing_address',
                locals: { addressable: payment.payee }

  .ui.four.column.divided.tiny.stackable.statistics.grid{ style: 'margin: 3em 0 1em;' }
    .ui.statistic.column
      .value= payment.amount.format
      .label Amount
    .ui.statistic.column
      .value= payment.date.strftime('%D')
      .label Date
    .ui.statistic.column
      .value= payment.status_text
      .label Status
    .ui.statistic.column
      .value.no-wrap= payment.kind_text
      .label Method

  - if payment.status_summary
    %div{ class: "#{payment.status_color} ui small message" }
      = payment.status_summary

  - if payment.convenience_fee.positive?
    .ui.small.message
      This payment incurred an associated
      = payment.convenience_fee.format
      convenience fee.

  .ui.very.basic.vertical.segment
    %h4.ui.dividing.header
      Applied to Invoices
    - if payment.invoice_payments.any?
      = render Accounting::Payments::InvoicesTableComponent.new(payment: payment)
    - else
      %p{ style: 'color: gray;' }
        None

  - if payment.credit.positive?
    .ui.info.message
      This document has #{payment.credit.format} of available credit.

  = render partial: 'shared/accounting/attachments_list',
    locals: { commercial_document: payment }

  - if payment.note.present?
    %br
    %br

    %h4.ui.dividing.header
      Note
    %p= simple_format(payment.note)

  = render Accounting::CommercialDocuments::JournalEntriesListComponent.new(payment)
