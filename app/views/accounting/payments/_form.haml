.ui.narrow.container#payment-form{ style: '' }
  = form_with model: [:accounting, payment],
    local: false,
    class: 'ui form' do |f|

    - if params[:tenant_id].present?
      = render Accounting::PaymentForm::TenantPreambleComponent.new(f)
    - else
      = render Accounting::PaymentForm::PreambleComponent.new(f)

    = render Accounting::PaymentForm::DetailsComponent.new(f)

    = render Accounting::PaymentForm::LedgerComponent.new(f)

    .ui.very.basic.vertical.segment
      .ui.error.message

      .field
        - if payment.new_record?
          = f.submit 'Save',
            class: 'primary ui submit button'
          = f.submit 'Save and New',
            class: 'ui basic submit button',
            name: :new
        - else
          = f.submit 'Update',
            class: 'ui primary submit button'
