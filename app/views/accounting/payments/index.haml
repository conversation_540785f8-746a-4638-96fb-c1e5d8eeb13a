.action-bar
  .ui.container
    %h1.ui.header
      Payments
    .action.buttons
      - if Feature.enabled?(:record_nontenant_revenue, Customer.current)
        = link_to 'Record Revenue', new_accounting_revenue_path,
            class: 'ui button'

      - unless current_property_manager.role.name.in?(['House Director', 'Chapter Advisor / Officer', 'House Corporation Board Member']) && CustomerSpecific::Behavior.disable_based_on_role_name?
        - require_permission :create_accounting_payments? do
          = link_to new_accounting_payment_path, class: 'ui button' do
            %i.plus.icon
            Enter Payment

      - unless current_property_manager.role.name.in?(['Chapter Advisor / Officer'])
        - require_permission :export_accounting_payments? do
          = export_button

.action-container{ style: 'padding-top: 1em' }
  .ui.wide.container
    .ui.container
      = render 'flash_notices'

    %div{ style: 'padding: 0 2rem;' }
      = ActionIndex::Accounting::Payments.(self,
        collection: @payments,
        user: current_property_manager,
        partial: { path: 'accounting/payments/table',
        locals: { payments: @payments } })

      %div{ style: 'padding-top: 1rem;' }
        = paginate @payments
