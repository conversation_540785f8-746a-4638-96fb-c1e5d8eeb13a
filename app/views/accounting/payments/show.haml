- page_title payment

= render ActionSidebar::ContainerComponent.new do |c|
  - c.with_main do
    = render 'flash_notices'

    .ui.two.column.grid
      .column
        .ui.breadcrumb
          = link_to 'Payments', accounting_payments_path, class: 'section'
          %i.right.angle.icon.divider
          %h1.ui.header
            - if payment.credit_note?
              Credit Note ##{payment.id}
            - else
              Payment ##{payment.id}

      .column
        = ActionsMenu::Payment.(payment, current_property_manager)
        - require_permission :print_accounting_payments? do
          = print_button

    = render partial: 'accounting/shared/payment', locals: { payment: payment }

    = render partial: 'management/audit_logs/loader',
      locals: { auditable: payment }

  - unless current_property_manager.role.name.in?(['Chapter Advisor / Officer', 'House Director']) && CustomerSpecific::Behavior.disable_based_on_role_name?
    - c.with_sidebar do |s|
      = render partial: 'sidebar', locals: { s: s, payment: payment }

= render partial: 'apply_modal', locals: { payment: payment }
