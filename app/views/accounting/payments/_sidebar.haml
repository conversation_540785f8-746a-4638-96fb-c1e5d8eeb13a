:ruby
  portfolio = nil
  entity = nil
  owners = nil
  property = nil
  tenant = nil
  vendor = nil

  property = payment.payer if payment.payer.is_a?(Property)
  property ||= payment.payee if payment.payee.is_a?(Property)

  entity = payment.payer if payment.payer.is_a?(Company) && payment.payer.customer_managed?
  entity ||= payment.payee if payment.payee.is_a?(Company) && payment.payee.customer_managed?

  tenant = payment.payer if payment.payer.is_a?(Tenant)
  tenant ||= payment.payee if payment.payee.is_a?(Tenant)

  membership = payment.journal_entries.map(&:lease_membership).compact.uniq.first
  lease = membership.lease if membership

  vendor = payment.payer if payment.payer.is_a?(Vendor)
  vendor ||= payment.payee if payment.payee.is_a?(Vendor)

  entity ||= property&.company
  owners ||= entity&.owners
  portfolio ||= entity&.portfolio

  ledger = payment.ledger&.decorate

- if portfolio
  - s.with_item title: 'Portfolio' do
    = link_to portfolio.name, portfolio_path(portfolio)

- if entity
  - s.with_item title: 'Entity' do
    = link_to entity.name, entity.url

- if owners.present?
  - s.with_item title: 'Owners' do
    .ui.list
      - owners.each do |owner|
        = link_to owner.name, owner_path(owner), class: 'item'

- if property
  - s.with_item title: 'Property' do
    = link_to property.name, property_path(property)

- if tenant
  - s.with_item title: I18n.t('tenant_term') do
    = link_to tenant.name, tenant.url

- if lease
  - s.with_item title: 'Lease' do
    = link_to "Lease #{lease.id}", leasing_lease_path(lease)

- if vendor
  - s.with_item title: 'Vendor' do
    = link_to vendor.name, vendor_path(vendor)

- if ledger
  - s.with_item title: 'Ledger' do
    = link_to ledger.sidebar_description, ledger.report_path

- unless payment.credit_note?
  - if payment.reconciled?
    - s.with_item title: 'Reconciled' do
      %ul.link.list{ style: 'padding: 0;' }
        - payment.reconciliations.each do |reconciliation|
          :ruby
            bank_account = reconciliation.bank_account

            description = [
              reconciliation.statement_date.strftime('%B %Y'),
              bank_account.name
            ].join(' - ')

            url = organization_bank_account_reconciliation_path(
              bank_account, reconciliation
            )

          = link_to description, url, class: 'item'
  - else
    - s.with_item title: 'Unreconciled'
