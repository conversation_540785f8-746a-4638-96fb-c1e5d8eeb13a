- require_permission :export_accounting_journal_entries? do
  = export_button class_name: 'right floated ui button'

.ui.breadcrumb
  = link_to 'Journals', accounting_journals_path, class: 'section'
  %i.right.angle.icon.divider
  = link_to @journal.name, accounting_journal_path(@journal), class: 'section'
  %i.right.angle.icon.divider
  %h1.ui.header
    = @account.display_name
    .sub.header
      = @balance_date.to_fs(:short_date)
      Balance of
      = @balance.format

= ActionIndex::Accounting::Amounts.(self,
  collection: @amounts,
  user: current_property_manager,
  partial: { path: 'accounting/accounts/amounts_table',
  locals: { amounts: @amounts } })

%br

= paginate @amounts
