= form_for @vendor, html: { class: 'ui form' } do |f|
  .two.required.fields
    .field
      = f.label :name
      = f.text_field :name

    .field
      = f.label :kind, 'Type'
      = f.semantic_dropdown :kind,
        Vendor.kinds.keys.sort.map { |k| [k.humanize, k] }

  .two.fields
    .field
      = f.label :business_type, 'Entity Type'
      = f.semantic_dropdown :business_type,
        Taxpayer.business_type_dropdown_values,
        { selected: f.object&.business_type },
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

    = f.fields_for :taxpayer_identification do |t|
      = render partial: 'shared/taxpayer_fields', locals: { f: t }

  .fields
    .ten.wide.field
      = f.label :tags
      = f.tags_field

    .six.wide.field
      = f.label :website
      = f.url_field :website, placeholder: 'https://www.example-vendor.com'

  .field
    = f.label :notes
    = f.text_area :notes, rows: 2

  %h4.ui.dividing.header
    Accounts Payable

  .field
    .ui.checkbox
      = f.check_box :unconsolidated_checks
      = f.label :unconsolidated_checks, 'Prefer Unconsolidated Checks'

  .field
    = f.label :preferred_disbursement, 'Preferred Payment Method'
    = f.semantic_dropdown :preferred_disbursement,
      HasPaymentMethods.vendor_remittance_options,
      {},
      class: 'ui search selection dropdown',
      data: { options: { placeholder: 'Unspecified' } }

  %h4.ui.dividing.header
    Address
  = render partial: 'shared/address_fields', locals: { f: f }

  %h4.ui.dividing.header
    Contacts

  #vendor_contacts
    = f.fields_for :vendor_contacts do |c|
      = render partial: 'vendor_contact_fields', locals: { f: c }

  .field
    = link_to_add_association f,
      :vendor_contacts,
      class: 'ui small basic button',
      'data-association-insertion-node' => '#vendor_contacts',
      'data-association-insertion-method' => 'append' do
      %i.plus.icon
      Add Contact

  = f.submit class: 'ui primary submit button'
