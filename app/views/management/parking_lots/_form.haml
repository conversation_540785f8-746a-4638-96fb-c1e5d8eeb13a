= form_for @parking_lot, html: { class: 'ui form' } do |f|
  .field
    = f.label :property_id, 'Property'
    = f.semantic_dropdown :property_id,
      [['None', '']] + current_property_manager.properties.pluck(:name, :id),
      { selected: @parking_lot.property_id },
      class: 'ui fluid search selection dropdown',
      data: { options: { placeholder: 'None' } }

  .required.field
    = f.label :name
    = f.text_field :name

  .required.field
    = f.label :space_count
    = f.number_field :space_count

  %h4.ui.dividing.header
    Address
  = f.fields_for :address do |a|
    .two.fields
      .required.field
        = a.label :line_one, 'Line One'
        = a.text_field :line_one
      .field
        = a.label :line_two, 'Line Two'
        = a.text_field :line_two
    .three.fields
      .required.field
        = a.label :city
        = a.text_field :city
      .required.field
        = a.label :region, 'State'
        = a.text_field :region
      .required.field
        = a.label :postal_code, 'Zip Code'
        = a.text_field :postal_code

  = f.submit class: 'ui right floated submit button'
