= f.label :onboardable_property_ids, 'Property', style: 'display: inline-block;'
%i.right.floated.ui.icon.info.circle{
  data: {
    content: 'Select one or more properties, or leave blank to include all',
    variation: 'very wide'
  },
  style: 'color: gray; margin-left: 0.1rem;'
}

= f.semantic_dropdown :onboardable_property_ids,
                      property_options,
                      {},
                      multiple: true,
                      data: { options: { multiple: true,
                      placeholder: 'Leave blank to allow all properties' } },
                      class: 'ui multiple search selection dropdown',
                      disabled: disabled
