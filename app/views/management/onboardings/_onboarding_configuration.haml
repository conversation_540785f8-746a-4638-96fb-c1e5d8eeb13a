= render 'breadcrumb'

.action-bar
  .ui.container
    %h1.ui.header
      New Onboarding Module
.ui.info.message
  %h5 Set Up Member Onboarding
  Use this setup to launch an onboarding process for invited members in their member portal.

- if @properties_filtered
  .ui.yellow.message
    Only the properties you have access to were copied for this onboarding. Any others were skipped.

.ui.container
  = render Management::Onboardings::FormComponent.new(user: current_property_manager,
    token: @token,
    member_onboarding_configuration: @member_onboarding_configuration)

.footer
  .ui.very.relaxed.grid
    .two.column.row
      .left.aligned.column
        = link_to 'Cancel', onboardings_path, class: 'ui button'
      .right.aligned.column
        %button.ui.primary.button{ type: :submit, form: 'onboarding-form' } Next
