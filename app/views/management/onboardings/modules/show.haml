.action-bar{style: 'padding: 2rem 2rem 0; margin-bottom: 2rem;'}
  .ui.grid
    .fourteen.wide.column
      .ui.breadcrumb
        = link_to 'Members', tenants_path, class: 'section'
        %i.right.angle.icon.divider
        = link_to 'Onboarding Setup', onboardings_path, class: 'section'
        %i.right.angle.icon.divider
        %h1.ui.header
          = @member_onboarding_configuration.name
      - if @member_onboarding_configuration.archived?
        .ui.warning.message{style: 'margin-bottom: 1rem; margin-top: 0'}
          .header
            Archived
          %p
            This was archived on #{(@member_onboarding_configuration.archived_at || 1.day.ago).to_fs(:human_datetime)}.
          %br
    .two.wide.column
      = ActionsMenu::MemberOnboarding.(@member_onboarding_configuration)

.ui.very.padded.stackable.grid
  .ui.three.wide.column
    = Sidebar::OnboardingSetup::Persisted.(@member_onboarding_configuration, @module_id)
  .ui.form.twelve.wide.column
    = form_with model: @record,
      disabled: true,
      id: 'onboarding-form',
      url: '',
      class: 'ui form' do |f|
      = render partial: Management::Onboardings::ModuleDetails.module_form_partial(@module_id), locals: { f: f, record: @record }

:javascript
  $('#onboarding-form').find('input').attr('disabled', true);
  $('#onboarding-form').find('button').attr('disabled', true);
  $('#onboarding-form').find('textarea').attr('disabled', true);
  $('#onboarding-form').find('a').addClass("disabled");
  $('#onboarding-form').find('.ui.dropdown').addClass("disabled");
  $('#onboarding-form').find('.question-container').addClass("disabled");
