.ui.container
  .ui.breadcrumb
    = link_to 'Members', tenants_path, class: 'section'
    %i.right.angle.icon.divider
    = link_to 'Onboarding Setup', onboardings_path, class: 'section'
    %i.right.angle.icon.divider
    = @onboarding.name
    %i.right.angle.icon.divider

  .action-bar
    .ui.container
      %h2 Assign Specific Members to Onboarding

  .ui.info.message
    %h2.header
      Select Members from Properties
    %p Assign specific members by finding them in properties to assign to this onboarding. Members within selected properties will be prompted to start onboarding after they log into their member portal
    %p This field is optional. You can set this onboarding module as a default for specific properties from the Properties page after submission.
  
  .ui.container
    = form_with model: nil, scope: :assignments, url: onboarding_assignments_path, method: :post, class: 'ui form' do |f|
      #property-selector.required.field{
        data: {
          controller: 'member-onboarding--assignments',
          'member-onboarding--assignments-dom-id': 'property-selection-editor',
          'member-onboarding--assignments-content-url': property_contacts_onboarding_assignments_url(@onboarding),
          'member-onboarding--assignments-content-url-query-param-name': 'property_id',
          'member-onboarding--assignments-existing-defaults-value':  @has_existing_defaults.pluck(:id).map(&:to_s)
        }
      }
        .field
          = f.label :property_ids, 'Properties'
          = f.semantic_dropdown :property_ids,
            unassigned_properties.pluck(:name, :id),
            { selected: params.dig(:assignments, :property_ids) },
            multiple: true,
            data: { options: { placeholder: 'Add properties(s)',  }, sidebar_selector_target: 'dropDown' },
            class: 'ui search selection dropdown'
          = f.hidden_field :sub_selection_exclusions, value: initial_exclusions.to_json, data: { 'member-onboarding--assignments-target' => 'subSelectionExclusions' }
        .field
          .ui.very.relaxed.grid
            .two.column.row
              .left.aligned.column
                = link_to 'Skip', onboardings_path, class: 'ui button'
              .right.aligned.column
                = f.submit 'Assign Members', class: 'ui button primary'
        #existing-default.ui.mini.modal
          %i.black.close.icon
          .header
            Default Exists
          .content
            %p.warn
            %p By continuing, the newly created onboarding will be applied instead.
          .actions
            .ui.basic.cancel.button
              Cancel
            .ui.primary.approve.button
              Continue

  #property-selection-editor.sidebar.menu.ui.right.vertical.very.wide{ style: 'padding: 1em;' }
    .ui.header
      %h2.selection-group-name

    .ui.info.message.embedded.middle
      %p
        Adjust the specific members you want to assign.

    .ui.container
      .ui.icon.fluid.input.large
        %input.selection-group-member-search{type: 'search', name: 'search_term', placeholder: 'Search for members'}
        %i.search.icon
      %p
      .selection-group-members
