:ruby
  counts = portfolio.occupancy_count
  property_count = counts.property_count
  unit_count = counts.unit_count
  tenant_count = counts.tenant_count
  occupancy = (counts.occupancy * 100).to_i
  path = portfolio.setup? ? portfolio : portfolio_setup_path(portfolio)

= link_to path, class: 'ui card' do
  .content
    .header
      %i.cubes.icon
      = portfolio.name
    .meta
      Portfolio with #{pluralize(property_count, 'Property')}

  - if portfolio.setup?
    .extra.content
      %span.left.floated
        = pluralize(unit_count, 'Units')
      %span.right.floated
        = pluralize(tenant_count, 'Tenants')

    .extra.content
      %span.left.floated
        #{occupancy}% Occupancy

  - else
    .extra.content
      %i.yellow.warning.icon
      Requires Setup
