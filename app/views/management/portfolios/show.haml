.action-bar
  .ui.container
    .ui.breadcrumb
      = Breadcrumb::Portfolio.(@portfolio, user: current_property_manager)
      %h1.ui.header
        = @portfolio.name

    .action.buttons
      - if Customer.current_subdomain == 'brown' && @portfolio.id == 10
        = link_to new_leasing_commercial_lease_path(portfolio_id: @portfolio.id),
          class: 'right floated ui button' do
          %i.plus.icon
          Add Commercial Lease

      - require_permission :export_portfolio_portfolios? do
        - if Feature.enabled?(:data_exporting, Customer.current)
          = link_to 'Export',
            export_portfolio_path(@portfolio),
            remote: true,
            class: 'ui button'

      - require_permission :create_portfolio_properties? do
        = link_to new_property_path(portfolio_id: @portfolio.id),
          class: 'right floated ui button' do
          %i.plus.icon
          Add Property

      - require_permission :update_portfolio_portfolios? do
        = link_to edit_portfolio_path(@portfolio),
          class: 'right floated ui button' do
          %i.write.icon
          Edit

.action-container{ style: 'padding-top: 1em; overflow-y: scroll;' }
  .ui.container
    - if @portfolio.properties.none?
      .ui.narrow.container
        .ui.info.message
          .header Empty Portfolio
          %p
            This portfolio does not contain any properties yet. Click
            %i Add Property
            above to add one.

    = ActionIndex::Properties.(self,
      collection: @properties,
      user: current_property_manager,
      partial: { path: 'management/properties/table',
      locals: { properties: @properties } })

- if Feature.enabled?(:onboarding_setup, Customer.current)
  = render 'turn_on_auto_assignment_onboarding_modal', auto_assignment_onboarding_options: []
  = render 'remove_default_modal'
