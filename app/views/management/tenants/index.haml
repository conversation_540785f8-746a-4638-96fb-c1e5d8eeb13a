.action-bar
  .ui.container
    %h1.ui.header
      = I18n.t('tenant_term').pluralize

    .action.buttons
      = export_button(class_name: 'ui excel button')

.action-container{ style: 'padding-top: 1em; overflow-y: scroll;' }
  .ui.container
    = render 'flash_notices'

    = ActionIndex::Tenants.(self,
      collection: @tenants,
      user: current_property_manager,
      partial: { path: 'management/tenants/table',
      locals: { tenants: @tenants } })

    %div{ style: 'padding-top: 1rem;' }
      = paginate @tenants
