- page_title @tenant.name

= ActionsMenu::Tenant.(@tenant, current_property_manager)

= link_to reports_ledger_path(filters: @tenant.decorate.ledger_button_filters),
  class: 'right floated basic ui button' do
  %i.file.alternate.outline.icon
  Ledger

- if Feature.enabled?(:tenant_profile_enter_payment, Customer.current)
  = link_to new_accounting_payment_path(tenant_id: @tenant.id),
    class: 'right floated basic ui button' do
    %i.money.check.alternate.icon
    Record Payment

- if @tenant.current_lease
  .ui.breadcrumb
    = Breadcrumb::Tenant.(@tenant, user: current_property_manager)
    %h1.ui.header
      %span{ title: @tenant.name }
        = @tenant.name.truncate(50)
      .sub.header
        Active #{I18n.t('tenant_term')}
      - @tenant.tags.each do |tag|
        .ui.tiny.tag.label= tag

- else
  .ui.breadcrumb
    = link_to 'Tenants', tenants_path
    %i.right.angle.icon.divider
    %h1.ui.header
      %span{ title: @tenant.name }
        = @tenant.name.truncate(50)
      .sub.header
        Inactive #{I18n.t('tenant_term')}

= render partial: 'shared/tenant', locals: { tenant: @tenant }
