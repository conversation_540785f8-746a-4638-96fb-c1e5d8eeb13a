.action-bar
  .ui.container
    %h1.ui.header
      Properties
    .action.buttons
      - require_permission :export_portfolio_properties? do
        = export_button

      - require_permission :create_portfolio_portfolios? do
        = link_to new_portfolio_path, class: 'ui button' do
          %i.ui.plus.icon
          New Portfolio

      - require_permission :create_portfolio_properties? do
        = link_to new_property_path(portfolio_id: current_property_manager.portfolios.first&.id),
          class: 'ui button' do
          %i.ui.plus.icon
          New Property

.action-container{ style: 'padding-top: 1em; overflow-y: scroll;' }
  .ui.container
    = ActionIndex::Properties.(self,
      collection: @properties,
      user: current_property_manager,
      partial: { path: 'management/properties/table',
      locals: { properties: @properties } })

-# A lot of specs has no portfolio, but this should not happen in production.
- if Feature.enabled?(:onboarding_setup, Customer.current) && Portfolio.exists?
  = render 'management/portfolios/turn_on_auto_assignment_onboarding_modal', auto_assignment_onboarding_options: []
  = render 'management/portfolios/remove_default_modal'
