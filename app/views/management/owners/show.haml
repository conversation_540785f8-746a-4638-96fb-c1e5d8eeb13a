= ActionsMenu::Owner.(@owner)

.ui.breadcrumb
  = link_to 'Owners', owners_path, class: 'section'
  %i.right.angle.icon.divider
  %h1.ui.header
    = page_title @owner.name
    .sub.header
      - if @owner.archived?
        (Archived) &bull;
      - @owner.companies.each do |company|
        = link_to company.name, manage_company_path(company)
      - if @owner.tags.any?
        %div
          - @owner.tags.each do |tag|
            .ui.tiny.tag.label= tag

.ui.stackable.grid
  .stretched.row
    .column
      .ui.segment
        %h4.ui.header
          Contact Information
        .ui.list
          .item
            %i.ui.mail.icon
            .content
              = @owner.email
              - if @owner.unconfirmed_email.present?
                &mdash; change to #{@owner.unconfirmed_email} awaiting
                confirmation
          .item
            %i.ui.phone.icon
            .content
              = @owner.phone&.phony_formatted(normalize: :US)

  .row
    .column
      .ui.segment
        %h4.ui.header
          Owner Portal
          .sub.header
            - if @owner.confirmed?
              Last Signed In on #{@owner.current_sign_in_at&.to_fs(:human_date)}
            - elsif @owner.confirmation_sent_at
              Invite Sent #{@owner.confirmation_sent_at.to_fs(:human_datetime)}
            - else
              Invite Not Sent

        .ui.horizontal.list
          - if @owner.confirmed?
            = link_to 'Send Password Reset',
              send_password_reset_owner_path(@owner),
              class: 'item'
          - elsif @owner.confirmation_sent_at
            = link_to 'Resend Invite',
              send_confirmation_instructions_owner_path(@owner),
              class: 'item'
          - else
            = link_to 'Send Invite',
              send_confirmation_instructions_owner_path(@owner),
              class: 'item'

  .row
    .column
      .ui.segment
        = render partial: 'shared/simple_property_list',
          locals: { properties: PropertiesQuery.for_owner(@owner) }

  .row
    .column
      .ui.segment
        %h4.ui.header
          Notes
        - if @owner.notes.present?
          = simple_format @owner.notes
        - else
          %p No notes

  .row
    .column
      .ui.segment
        = render partial: 'shared/documents_panel',
          locals: { attachable: @owner }

= render partial: 'shared/communications/tray',
  locals: { contact: @owner }
