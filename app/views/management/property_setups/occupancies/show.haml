:ruby
  current_status = if @lease.persisted?
                     'occupied'
                   elsif @downtime&.not_rent_ready?
                     'not_rent_ready'
                   elsif @downtime&.down?
                     'down'
                   else
                     'vacant'
                   end

%h2.ui.header
  Occupancy

= form_with url: property_setup_occupancy_path(@property),
  model: @form,
  scope: :occupancy,
  method: :patch,
  html: { class: 'ui form', multipart: true } do |f|

  .field
    = f.label :reason, 'Unit Status', for: 'unit_status'
    = f.semantic_dropdown :reason,
      [['Vacant', 'vacant'],
      ['Down', 'down'],
      ['Not Rent Ready', 'not_rent_ready'],
      ['Occupied', 'occupied']],
      { selected: current_status },
      id: 'unit_status'

  #downtime-fields
    %h4.ui.dividing.header
      Downtime

    .two.fields
      .required.field
        = f.label :down_start_date, 'Start Date'
        = f.semantic_date_field :down_start_date

      .field
        = f.label :down_end_date, 'End Date'
        = f.semantic_date_field :down_end_date

    .field
      = f.label :note
      = f.text_area :note, rows: 2

  #lease-fields
    = f.fields_for :lease_memberships do |lmf|
      %h4.ui.dividing.header
        Current Lease

      .two.fields
        .field
          = f.label :start_date
          = f.semantic_date_field :start_date

        .field
          = f.label :end_date
          = f.semantic_date_field :end_date

      .two.fields
        .field
          = f.label :executed_at, 'Execution Date'
          = f.semantic_date_field :executed_at

        .field
          = lmf.label :move_in_date, 'Move-In Date'
          = lmf.semantic_date_field :move_in_date

      .field
        = f.label :lease_document
        = f.file_field :lease_document

      .two.fields
        .field
          = f.label :deposit, 'Held Deposit'
          = f.money_field :deposit

        = f.fields_for :charge_schedule_entries do |cf|
          .field
            = cf.label :amount, cf.object.name
            = cf.money_field :amount

      %br
      = lmf.fields_for :tenant do |tf|
        %h4.ui.dividing.header
          = I18n.t('tenant_term')
        .two.fields
          .field
            = tf.label :first_name, 'First Name'
            = tf.text_field :first_name

          .field
            = tf.label :last_name, 'Last Name'
            = tf.text_field :last_name

        .two.fields
          .field
            = tf.label :email, 'Email'
            = tf.email_field :email

          .field
            = tf.label :phone, 'Phone'
            = tf.text_field :phone,
              value: tf.object.model.formatted_phone

        .field
          = tf.label :tags, "#{I18n.t('tenant_term')} Tags"
          = tf.tags_field

  %br
  .clearfix
    = link_to 'Back',
      property_setup_floorplan_path(@property),
      class: 'ui basic button'

    = f.submit 'Next',
      class: 'right floated ui button'

  :javascript
    var leaseFields = $('#lease-fields');
    var downtimeFields = $('#downtime-fields');

    var hideLease = #{@lease.new_record? ? 'true' : 'false'}
    var hideDowntime = #{@downtime.new_record? ? 'true' : 'false'}

    if (hideDowntime) {
      downtimeFields.hide();
    }

    if (hideLease) {
      leaseFields.hide();
    }

    $('#unit_status').dropdown('setting', 'onChange', function (id) {
      if (id === 'occupied') {
        leaseFields.show(200);
        downtimeFields.hide(200);
      } else {
        leaseFields.hide(200);

        if (id === 'down' || id === 'not_rent_ready') {
          downtimeFields.show(200);
        } else {
          downtimeFields.hide(200);
        }
      }
    });
