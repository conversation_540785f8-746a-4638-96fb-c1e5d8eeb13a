- properties = bank_account.owner.properties.unarchived
- hide_property_field = (transaction_context == 'forward' || properties.size <= 1)

= form_with model: [bank_account, transaction],
            url: organization_tunisia_bank_account_card_activity_path(bank_account, transaction), 
            method: :patch, 
            local: false, 
            id: 'transaction-form', 
            class: 'ui form' do |f|

  .ui.info.message{ data: { tunisia__bank_accounts__card_activities__transaction_target: 'contextMessage' } }
    %p Creating a receipt records an invoice and payment from the selected vendor.

  .required.field
    = f.label :description, 'Description'
    = f.text_field :description,
                   data: { tunisia__bank_accounts__card_activities__transaction_target: 'transactionDescription',
                   action: 'tunisia--bank-accounts--card-activities--transaction#updateDescription' }

  .two.fields
    .required.field
      = f.label :vendor_id, 'Vendor'
      = f.semantic_dropdown :vendor_id,
                            VendorsQuery.new.search.pluck(:name, :id),
                            { prompt: 'Select Vendor' },
                            class: 'ui dropdown search vendor',
                            data: { tunisia__bank_accounts__card_activities__transaction_target: 'vendorSelector',
                            action: 'tunisia--bank-accounts--card-activities--transaction#updateVendor' }
      - require_permission :create_portfolio_vendors? do
        %div{ style: 'height: 8px;' }
        = link_to "+ Add New Vendor", '#', data: { modal: :vendor_form_modal }

    .required.field
      = f.label :account_id, 'Account'
      = f.semantic_dropdown :account_id,
                            plutus_accounts.map { |account| [account.display_name, account.id] },
                            { prompt: 'Select' },
                            class: 'ui dropdown search',
                            data: { tunisia__bank_accounts__card_activities__transaction_target: 'accountSelector',
                            action: 'tunisia--bank-accounts--card-activities--transaction#updateAccount',
                            account_id: transaction.account_id }

  - if properties.one?
    = f.hidden_field :property_id, value: properties.first.id, data: { tunisia__bank_accounts__card_activities__transaction_target: 'propertySelector' }
  - elsif !hide_property_field
    .required.field
      = f.label :property_id, 'Property', style: 'display: inline;'
      %i.ui.icon.info.circle{ data: { content: "Select a property to apply property-level accounting to this transaction's journal entries. This could affect budget and financial reporting. Select All Properties for entity-level accounting.", variation: 'very wide' }, style: 'color: gray;' }
      = f.semantic_dropdown :property_id,
                            [['All Properties', nil]] + properties.pluck(:name, :id),
                            {},
                            class: 'ui dropdown search selection',
                            data: { tunisia__bank_accounts__card_activities__transaction_target: 'propertySelector',
                            action: 'tunisia--bank-accounts--card-activities--transaction#updateProperty',
                            options: { placeholder: 'Select' } }

  = f.hidden_field :lock_version

  .field{ :style => ('display: none;' if transaction_context == 'create') }
    = render 'forward_invoice_table', f:

  .field
    = render 'select_invoice_table', f:, transaction:

  .upload-field{ style: ("display: none;" if transaction.reciept_or_affidavit_attached?) }
    .field{ class: class_names(required: CustomerSpecific::Behavior.accounting_receipt_required?) }
      = f.label :receipt do
        = link_to "Missing receipt?",
          '#',
          data: { modal: :missing_receipt_modal },
          style: 'float: right; font-weight: normal;',
          id: 'missing_receipt_link'

        Attachment
      = f.uppy_dashboard_field :receipt, multiple: false, accept: ['image/*', 'application/pdf'],
                                use_s3: false, use_active_storage: true, max_number_of_files: 1,
                                height: 225,
                                note: 'Images or PDF files only, maximum file size of 25 MB'

  = render 'receipt_segment', transaction: transaction

  .ui.error.message

= render 'shared/add_vendor_modal', transaction: transaction
= render 'missing_receipt_modal', transaction: transaction

:javascript
  $('.info.circle.icon').popup({
  });
