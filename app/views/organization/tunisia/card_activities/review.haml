:ruby
  transaction_context = Customer.current.fee_management? ? 'forward' : 'create'
  matching_invoice_url = matching_invoices_organization_tunisia_bank_account_card_activity_path(@bank_account, @transaction)

%div{
  data: {
    controller: 'tunisia--bank-accounts--card-activities--transaction',
    tunisia__bank_accounts__card_activities__transaction: {
      matching_invoice_url_value: matching_invoice_url,
      transaction_context_value: transaction_context
    }
  }
}
  .ui.breadcrumb
    = link_to 'Transactions in Review',
      organization_tunisia_bank_account_card_activities_path(@bank_account),
      class: 'section'
    %i.right.angle.icon.divider

    %h1.ui.header{ style: 'margin-bottom: 6px;' }
      Review Transaction (#{@transaction.amount.format})

    %div.subheader{ style: 'display: flex; align-items: center; margin-bottom: 20px;' }
      %p{ style: 'color: #********; font-size: 16px; margin: 0;' } 
        #{@transaction.merchant_name}, #{@transaction.transaction_at.to_fs(:short_date)}
      %span.ui.label{ style: 'background-color: #AC9103; color: white; margin-left: 14px;' } Needs Review

    = render 'transaction_context_toggle', transaction_context:

  .ui.hidden.divider

  = render 'form', bank_account: @bank_account, transaction: @transaction, transaction_context:

  .ui.hidden.divider
  
  .footer
    - if next_transaction
      %button.ui.primary.button{ type: :submit, form: 'transaction-form', name: 'next', data: { disable: true } } Mark as Ready & Next
    %button.ui.button{ type: :submit, form: 'transaction-form', class: class_names(primary: !next_transaction), data: { disable: true } }
      Mark as Ready & Exit
