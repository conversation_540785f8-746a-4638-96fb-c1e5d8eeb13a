= ActionsMenu::Employee.(@employee, user: current_property_manager)

- if current_property_manager.role.can_see_audits?
  - require_permission :view_activity_for_organization_employees? do
    = link_to 'View Activity',
      audits_organization_employee_path(@employee),
      class: 'right floated ui button'

.ui.breadcrumb
  = link_to 'Employees', organization_employees_path, class: 'section'
  %i.right.angle.icon.divider
  %h1.ui.header
    = page_title @employee.name
    .sub.header
      = @employee.subheader

.ui.list
  .item
    %i.ui.mail.icon
    .content
      = @employee.email
  .item
    %i.phone.icon
    .content
      = @employee.formatted_phone || 'Unknown'

%h3.ui.header
  = pluralize(@employee.property_memberships.count, 'Property Memberships')
.ui.selection.list
  - @employee.property_memberships.each do |item|
    = link_to item.target.url, class: 'item' do
      %i.large.cubes.icon{ style: 'padding: 0.2em 0.3em 0 0.3em;' }
      .content
        .header
          = item.target.name
        .meta
          = item.target.class.to_s.titlecase
