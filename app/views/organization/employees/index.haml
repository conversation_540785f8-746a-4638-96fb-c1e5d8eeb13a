.action-bar
  .ui.container
    %h1.ui.header
      Employees
    .action.buttons
      - if current_property_manager.role.administrator?
        - require_permission :export_organization_employees? do
          = export_button(class_name: 'ui excel button')

        - require_permission :create_organization_employees? do
          = link_to new_organization_employee_path, class: 'ui button' do
            %i.add.user.icon
            Add Employee

.action-container{ style: 'padding-top: 1em; overflow-y: scroll;' }
  .ui.container
    = render 'flash_notices'

    = ActionIndex::Employees.(self,
      collection: @employees,
      user: current_property_manager,
      partial: { path: 'organization/employees/table',
      locals: { employees: @employees } })

    %div{ style: 'padding-top: 1rem;' }
      = paginate @employees
