= ActionsMenu::Account.(@account, current_property_manager)

- require_permission :export_organization_charts_of_accounts? do
  = export_button class_name: 'right floated ui button'

.ui.breadcrumb
  = link_to 'Charts of Accounts',
    organization_charts_of_accounts_path,
    class: 'section'
  %i.right.angle.icon.divider

  = link_to @account.tenant.name,
    organization_chart_of_accounts_path(@account.tenant),
    class: 'section'
  %i.right.angle.icon.divider

  %h1.ui.header
    = @account.display_name
    .sub.header
      = @account.subtitle

= ActionIndex::Accounting::Amounts.(self,
  collection: @amounts,
  user: current_property_manager,
  partial: { path: 'accounting/accounts/amounts_table',
  locals: { amounts: @amounts } })

%br

= paginate @amounts
