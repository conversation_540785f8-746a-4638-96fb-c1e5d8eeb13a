.action-bar
  .ui.container
    %h1.ui.header
      Configurations

    .action.buttons
      - if Feature.enabled?(:tag_management, Customer.current) && current_property_manager.role.administrator?
        - require_permission :manage_settings_tags? do
          = link_to settings_tags_path,
            class: 'ui button' do
            %i.tag.icon
            Manage Tags

      - require_permission :create_organization_configurations? do
        = link_to new_organization_configuration_path,
          class: 'ui button' do
          %i.plus.icon
          Add Configuration

.action-container{ style: 'padding-top: 2em; overflow-y: scroll;' }
  .ui.container
    = ActionTable::Configurations.(params, @configurations)
