= render ActionSidebar::ContainerComponent.new do |c|
  - c.with_main do
    = render 'flash_notices'

    = ActionsMenu::BankAccount.(@bank_account, current_property_manager)

    = link_to 'New Payment Batch',
      '#',
      class: 'right floated ui basic button',
      data: { modal: :new_deposit_batch_modal }

    - if @bank_account.tunisia_account? && Feature.enabled?(:unit_debit_card_transactions, Customer.current)
      - require_permission :manage_transactions_in_review_for_organization_bank_accounts? do
        = link_to "Transactions in Review (#{@transactions_count})",
          organization_tunisia_bank_account_card_activities_path(@bank_account),
          class: 'right floated ui basic button'

    .ui.breadcrumb
      = link_to 'Bank Accounts',
        organization_bank_accounts_path,
        class: 'section'
      %i.right.angle.divider.icon
      %h1.ui.header
        = @bank_account.name
        .sub.header
          - if @bank_account.archived?
            Archived
          = link_to @bank_account.owner.name,
            organization_company_path(@bank_account.owner)
          = @bank_account.account_type.humanize
          Account

    :ruby
      active_tab = if request.path.ends_with?('batches_tab')
                     :batches
                   elsif request.path.ends_with?('transactions_tab')
                     :transactions
                   elsif request.path.ends_with?('checks_tab')
                     :checks
                   elsif request.path.ends_with?('ut_cards_tab')
                     :tunisia_cards
                   elsif request.path.ends_with?('ut_activity_tab')
                     :tunisia_activity
                   elsif request.path.ends_with?('ut_account_tab')
                     :tunisia_account
                   else
                     :reconciliation_history
                   end

    .ui.top.attached.tabular.menu#bank-account-tabs
      %a.item{ 'data-tab' => 'reconciliation_history_tab',
      class: ('active' if active_tab == :reconciliation_history) }
        Reconciliation History

      %a.item{ 'data-tab' => 'batches_tab',
      class: ('active' if active_tab == :batches) }
        Batches

      %a.item{ 'data-tab' => 'transactions_tab',
      class: ('active' if active_tab == :transactions) }
        Ledger

      - if @bank_account.tunisia_account?
        %a.item{ 'data-tab' => 'ut_cards_tab',
        class: ('active' if active_tab == :tunisia_cards) }
          Cards

        %a.item{ 'data-tab' => 'ut_activity_tab',
        class: ('active' if active_tab == :tunisia_activity) }
          Bank Activity

        %a.item{ 'data-tab' => 'ut_statements_tab',
        class: ('active' if active_tab == :tunisia_statements) }
          Statements

    .ui.bottom.attached.tab.segment{ 'data-tab' => 'reconciliation_history_tab',
      class: ('active' if active_tab == :reconciliation_history) }
      = render partial: 'reconciliation_history',
        locals: { bank_account: @bank_account,
        reconciliations: @reconciliations }

    .ui.bottom.attached.tab.segment{ 'data-tab' => 'batches_tab',
      class: ('active' if active_tab == :batches) }
      = render partial: 'batches',
        locals: { bank_account: @bank_account,
        batches: @batches }

    .ui.bottom.attached.tab.segment{ 'data-tab' => 'transactions_tab',
      class: ('active' if active_tab == :transactions) }
      = render partial: 'transactions',
        locals: { bank_account: @bank_account,
        ledger_amounts: @transactions_amounts }

    - if @bank_account.tunisia_account?
      - tunisia_id = @bank_account.tunisia_deposit_account.tunisia_id
      .ui.bottom.attached.tab.segment{ 'data-tab' => 'ut_cards_tab',
        class: ('active' if active_tab == :tunisia_cards) }
        = render partial: 'organization/bank_accounts/tunisia/cards',
            locals: { company_id: @entity.id,
            tunisia_id: tunisia_id,
            tunisia_customer_token: tunisia_customer_token }

      .ui.bottom.attached.tab.segment{ 'data-tab' => 'ut_activity_tab',
        class: ('active' if active_tab == :tunisia_activity) }
        = render partial: 'organization/bank_accounts/tunisia/activity',
          locals: { company_id: @entity.id,
          tunisia_id: tunisia_id,
          tunisia_customer_token: tunisia_customer_token }
          
      .ui.bottom.attached.tab.segment{ 'data-tab' => 'ut_statements_tab',
        class: ('active' if active_tab == :tunisia_statements) }
        = render partial: 'organization/bank_accounts/tunisia/statements',
          locals: { bank_account: @bank_account,
          tunisia_customer_token: tunisia_customer_token,
          company_id: @entity.id }


    = render partial: 'new_deposit_batch_modal',
      locals: { deposit_batch: @bank_account.deposit_batches.build }

    = render partial: 'tunisia_dispute_charge_modal'

    - if @bank_account.tunisia_account?
      = render Tunisia::TwoFactor::ModalComponent.new(company: @entity)

  - c.with_sidebar do |s|
    = render partial: 'sidebar', locals: { s: s, bank_account: @bank_account }

:javascript
  $('.tabular.menu#bank-account-tabs .item').tab({
    history: true,
    onLoad: function (tabPath) {  
      var guarded = $(`[data-tab=${tabPath}] [data-controller=tunisia--guard]`);
      if(guarded.length > 0) {
        guarded[0].dispatchEvent(new CustomEvent('show'));
      }
    }
  });

  setTimeout(function() {
    $('#two_factor_modal').modal(window.modalDefaults);
  }, 200);

