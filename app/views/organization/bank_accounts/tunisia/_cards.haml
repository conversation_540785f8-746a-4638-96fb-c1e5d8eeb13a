%div{ data: { controller: 'tunisia--underflow' } }
  = render Tunisia::TwoFactor::GuardComponent.new(company_id: company_id,
          token: tunisia_customer_token) do
    = link_to 'Open a Card',
            new_organization_tunisia_bank_account_card_path(@bank_account),
            class: 'ui button primary',
            style: 'margin-bottom: 3rem;'

    = render partial: 'organization/bank_accounts/tunisia/card_details',
            locals: {tunisia_customer_token: tunisia_customer_token}

    = render partial: 'organization/bank_accounts/tunisia/multiple_cards',
            locals: { company_id: @entity.id,
            tunisia_id: tunisia_id,
            tunisia_customer_token: tunisia_customer_token }

:javascript
  $('#tunisia_card_details_modal').modal(
    Object.assign({detachable: false}, window.modalDefaults));
  $('unit-elements-multiple-cards').on('unitMultipleCardsCardClicked', event => {
    event.detail.then(card => {
      var card_elt = $("#tunisia_card_details_modal unit-elements-card");
      if (card_elt.attr('card-id') === card.data.id.toString()) {
        $('#tunisia_card_details_modal').modal('show');
      } else {
        card_elt.one('unitOnLoad', (_load_event) => {
          $('#tunisia_card_details_modal').modal('show');
        })
        card_elt.attr('card-id', card.data.id);
        card_elt.trigger('unitRequestRefresh');
      }
    })
  });
