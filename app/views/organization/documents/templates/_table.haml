:ruby
  formatted_template_options = lambda { |template|
    template.template_options.except(
      'property_id',
      'portfolio_id',
      'customer_id',
      'template_type'
    ).map { |key, value| "#{key.titleize}: #{value}" }.join(', ')
  }

  formatted_template_type = lambda { |template|
    type = template.template_options['template_type']&.to_sym
    Document::TEMPLATE_KINDS[type] || 'Unspecified'
  }

%table.ui.very.basic.single.line.selectable.sortable.unstackable.table
  %thead
    %tr
      %th.no-sort.center.aligned Name
      %th.no-sort.center.aligned Updated
      %th.no-sort.center.aligned Type
      %th.no-sort.center.aligned Options
      %th.no-sort.center.aligned Scope
      %th.no-sort
  %tbody
    - templates.each do |template|
      %tr
        %td= link_to_if current_property_manager.can.download_organization_documents?, template.filename, template.expiring_url
        %td.center.aligned= template.updated_at.to_fs(:short_datetime)
        %td.center.aligned= formatted_template_type[template]
        %td.center.aligned= formatted_template_options[template]
        %td
          - if template.template_options['property_id']
            - property = Property.find(template.template_options['property_id'])
            Property /
            = link_to property.name, property.url
          - elsif template.template_options['portfolio_id']
            - portfolio = Portfolio.find(template.template_options['portfolio_id'])
            Portfolio /
            = link_to portfolio.name, portfolio.url
          - elsif template.template_options['customer_id']
            Any
        %td.center.aligned
          - require_permission :download_organization_documents? do
            = link_to 'Download', template.expiring_url,
              download: true
            &emsp;
          - if current_property_manager.email.include?('revela.co')
            - require_permission :download_organization_documents? do
              = link_to 'Sample',
                sample_organization_documents_template_path(template),
                download: true
              &emsp;
          - require_permission :destroy_organization_documents? do
            = link_to 'Delete',
              organization_documents_template_path(template),
              method: :delete,
              data: { confirm: "Remove #{template.filename}?" }
