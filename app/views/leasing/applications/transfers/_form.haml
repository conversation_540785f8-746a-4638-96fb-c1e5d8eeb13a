= form_with model: @application,
  id: :application_transfer_form,
  url: leasing_application_transfers_path(@application),
  method: :post,
  local: false,
  class: 'ui form' do |f|

  .field
    = f.label :property_id, 'Property'
    = f.semantic_dropdown :property_id,
      available_property_options,
      { selected: @selected_property.id },
      class: 'ui search selection dropdown'

  - if @configuration.apply_to_floorplans?
    .field
      = f.label :floorplan_id, 'Floor Plan'
      = f.semantic_dropdown :floorplan_id,
        available_floorplan_options,
        {},
        class: 'ui search selection dropdown'
  - elsif @configuration.apply_to_units?
    .field
      = f.label :unit_id, 'Unit'
      = f.semantic_dropdown :unit_id,
        available_unit_options,
        {},
        class: 'ui search selection dropdown'

  .ui.error.message

  .clearfix
    = f.submit 'Submit', class: 'ui primary submit button'

:javascript
  initializeSemanticFields();

  $('#lease_application_property_id').dropdown('setting', 'onChange', function () {
    $('#application_transfer_form').submit();
  });
