= form_with model: @move_out,
  url: leasing_lease_move_out_path(@lease, @move_out),
  local: false,
  class: 'ui form' do |f|

  .ui.top.attached.info.message{ style: 'margin-top: 0;' }
    .header Move-Out Inspection
    %p Create or review an inspection to turn over the unit.

  .ui.attached.clearing.segment
    - if @move_out.inspection
      %p
        = link_to @move_out.inspection.name,
          operations_inspection_path(@move_out.inspection),
          target: '_blank'
    - else
      .field
        .ui.checkbox.orange.slider
          = f.check_box :perform_inspection
          = f.label :perform_inspection, 'Assign Inspection'

      .two.fields
        .required.field
          = f.label :inspection_assignee, 'Assignee'
          = f.semantic_search_field :inspection_assignee,
            placeholder: 'Vendor or Employee',
            url: "/operations/projects/0/assignments.json?q={query}",
            data: { options: { type: 'category' } }

        .field
          = f.label :inspection_date, 'Scheduled Date'
          = f.semantic_date_field :inspection_date

      .required.field
        = f.label :inspection_template_id, 'Template'
        = f.semantic_dropdown :inspection_template_id,
          [['Select', nil]] + Inspection::Template.pluck(:name, :id),
          { selected: @move_out.configuration.move_out_inspection_template_id },
          data: { options: { placeholder: 'Select' } }

  .ui.attached.info.message
    .header Rekey Work Order
    %p Create or review a work order to rekey the unit.

  .ui.bottom.attached.clearing.segment
    - if @move_out.rekey_maintenance_ticket
      %p
        = link_to @move_out.rekey_maintenance_ticket.subject,
          maintenance_ticket_path(@move_out.rekey_maintenance_ticket),
          target: '_blank'
    - else
      .field
        .ui.checkbox.orange.slider
          = f.check_box :perform_rekey
          = f.label :perform_rekey, 'Assign Rekey'

      .two.fields
        .required.field
          = f.label :rekey_assignee, 'Assignee'
          = f.semantic_search_field :rekey_assignee,
            placeholder: 'Vendor or Employee',
            url: "/operations/projects/0/assignments.json?q={query}",
            data: { options: { type: 'category' } }

        .field
          = f.label :rekey_date, 'Scheduled Date'
          = f.semantic_date_field :rekey_date

      .field
        = f.label :rekey_subject, 'Subject'
        = f.text_field :rekey_subject,
          value: "Rekey #{@move_out.lease.unit.name}"

    .ui.error.message

    = link_to 'Back',
      forwarding_addresses_leasing_lease_move_out_path(@lease, @move_out),
      class: 'ui basic button'

    = f.submit 'Next',
      class: 'right floated ui button'
