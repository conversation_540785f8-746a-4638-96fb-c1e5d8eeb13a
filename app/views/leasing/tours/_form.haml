:ruby
  property_dropdown_values = current_property_manager
                             .properties
                             .pluck(:name, :id)

  tour_guide_dropdown_values = EmployeesQuery
                               .for_user(current_property_manager)
                               .map { |user| [user.name, user.id] }

= form_with model: [:leasing, tour],
  id: :tour_form,
  local: local,
  html: { class: 'ui form' } do |f|

  .ui.error.message

  = f.hidden_field :lead_id, value: tour.lead_id
  = f.hidden_field :guest_card_id, value: tour.guest_card_id

  .two.required.fields
    .field
      = f.label :property_id, 'Property'
      = f.semantic_dropdown :property_id,
        [['Select', '']] + property_dropdown_values,
        { selected: tour.property_id },
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

    .field
      = f.label :tour_guide_id, 'Tour Guide'
      = f.semantic_dropdown :tour_guide_id,
        [['Select', '']] + tour_guide_dropdown_values,
        { selected: tour.tour_guide_id },
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

  .required.field
    = f.label :time
    = f.semantic_date_field :time,
      {},
      type: 'datetime',
      minDate: Time.zone.now.iso8601

  .field
    = f.label :message
    = f.text_area :message,
      rows: 3,
      placeholder: ToursMailer::DEFAULT_MESSAGE

  .clearfix
    %button.right.floated.ui.submit.button
      %i.open.envelope.outline.icon
      Schedule Tour

:javascript
  $('form#tour_form').form({
    inline: true,
    on: 'submit',
    fields: {
      tour_time: {
        rules: [
          {
            type:'empty',
            prompt: 'Please select or enter a time.'
          },
        ],
      },
    },
  });
