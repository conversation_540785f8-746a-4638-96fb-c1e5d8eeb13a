- s.with_item title: 'Portfolio' do
  = link_to lease.portfolio.name, portfolio_path(lease.portfolio)

- s.with_item title: 'Entity' do
  = link_to lease.company.name, lease.company.url

- if lease.company.owners.any?
  - s.with_item title: 'Owners' do
    .ui.list
      - lease.company.owners.each do |owner|
        = link_to owner.name, owner_path(owner), class: 'item'

- s.with_item title: 'Property' do
  = link_to lease.property.name, property_path(lease.property)

- s.with_item title: 'Unit' do
  = link_to lease.unit.name, unit_path(lease.unit)

- if Feature.enabled?(:next_rent_date_sidebar, Customer.current)
  - s.with_item title: 'Next Rent Date' do
    = render Leasing::Lease::Sidebar::NextRentDateComponent.new(lease: lease)
