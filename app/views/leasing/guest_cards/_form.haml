= form_with model: [:leasing, @guest_card],
  id: :guest_card_form,
  class: 'ui form' do |f|

  = f.fields_for :data do |df|
    .two.required.fields
      .field
        = df.label :first_name, 'First Name'
        = df.text_field :first_name,
          data: { validate: 'first_name' },
          value: @guest_card.first_name

      .field
        = df.label :last_name, 'Last Name'
        = df.text_field :last_name,
          data: { validate: 'last_name' },
          value: @guest_card.last_name

    .two.fields
      .field
        = df.label :email, 'Email'
        = df.email_field :email,
          value: @guest_card.email

      .field
        = df.label :phone
        = df.text_field :phone,
          value: @guest_card.formatted_phone

    .two.fields
      .required.field
        = f.label :property_id, 'Property'
        = f.semantic_dropdown :property_id,
          [['Unspecified', nil]] + current_property_manager.properties.pluck(:name, :id),
          {},
          class: 'ui search selection dropdown',
          data: { validate: 'property', options: { placeholder: 'Unspecified' } }

      .field
        = f.label :style
        = f.semantic_dropdown :style,
          [['Unspecified', nil]] + GuestCard.styles.keys.map { |k| [k.titleize, k] },
          {},
          class: 'ui search selection dropdown',
          data: { options: { placeholder: 'Unspecified' } }

    .two.fields
      .field
        = f.label :move_in_date, 'Desired Move-In Date'
        = f.semantic_date_field :move_in_date

      .field
        = f.label :source
        = f.semantic_dropdown :source,
          ['Unspecified'] + Source.pluck(:name),
          { selected: @guest_card.external_source&.source },
          class: 'ui search selection dropdown',
          data: { options: { placeholder: 'Unspecified', allowAdditions: true } }

    .field
      = df.label :message, 'Notes'
      = df.text_area :message, rows: 3,
        value: @guest_card.message || @guest_card.notes

  = f.submit 'Submit', class: 'ui primary submit button'

:javascript
  $('form#guest_card_form').form({
    on: 'submit',
    inline: true,
    fields: {
      first_name: ['empty'],
      last_name: ['empty'],
    }
  });
