- s.with_item title: '<PERSON>der' do
  = link_to loan.lender.name, loan.lender.url

- s.with_item title: '<PERSON>rrow<PERSON>' do
  = link_to loan.borrower.name, loan.borrower.url

- s.with_item title: 'Property' do
  = link_to loan.property.name, loan.property.url

- s.with_item title: 'Loan Number' do
  = loan.loan_number

- s.with_item title: 'Origination Date' do
  = loan.origination_date.to_fs(:human_date)

- s.with_item title: 'Repayment Method' do
  = loan.repayment_type.titleize

- s.with_item title: 'Beginning Principal' do
  = loan.principal.format

- s.with_item title: 'Interest Rate' do
  = number_to_percentage loan.interest_rate_percentage

- s.with_item title: 'Frequency' do
  = loan.payment_frequency.titleize

- s.with_item title: 'Tags' do
  - if loan.tags.none?
    None

  - loan.tags.each do |tag|
    = link_to tag.tag,
      leasing_agreements_path(type: 'loans', filters: { tag: tag.id }),
      class: 'ui small tag label'

- s.with_item title: 'Attachments' do
  - if loan.attachments.none?
    None

  - else
    .ui.link.list
      - agreement.attachments.each do |attachment|
        = link_to attachment.filename,
          attachment.expiring_url,
          class: 'item'
