.ui.three.column.divided.stackable.padded.grid#search_results
  - results.columns.each do |name, grouped_results|
    .column
      %h3.ui.center.aligned.header= name
      - grouped_results.each do |category, results|
        .category-header
          = category
        .ui.middle.aligned.selection.list
          - results.each do |result|
            = render partial: 'result', locals: { result: result }
      - if grouped_results.none?
        %p
          No Results.
