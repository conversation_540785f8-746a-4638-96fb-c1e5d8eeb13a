:ruby
  property_options = \
    [['Select', nil]] +
    Property
    .order(name: :asc)
    .includes(company: :metadata)
    .map do |property|
      [
        [
          property.name,
          property.company.meta(:university)&.titleize
        ].compact.join(' - '),
        property.id
      ]
    end

.ui.narrow.container
  %h1.ui.header
    - if wrp?
      Tenant
    - else
      Member
    Onboarding

  - if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
    = render partial: 'preamble_sae', locals: { form: @form }
  - elsif Customer.current_subdomain.start_with?('sigep-azbeta')
    = render partial: 'preamble_sigep_azbeta'
  - elsif Customer.current_subdomain.start_with?('wrp')
    = render partial: 'preamble_wrp'
  - elsif Customer.current_subdomain.start_with?('dphie')
    = render partial: 'preamble_dphie'

  = form_with model: @form,
    url: member_onboardings_path,
    scope: :member_onboarding_form,
    remote: true,
    local: false,
    class: 'ui form' do |f|

    .ui.very.padded.basic.vertical.segment
      .equal.width.required.fields
        .field
          = f.label :property_id do
            - if dphie?
              Chapter
            - else
              Property
          = f.semantic_dropdown :property_id,
            property_options,
            {},
            class: 'ui search selection dropdown',
            data: { options: { placeholder: 'Select' } }

        - if dphie?
          .field
            = f.label :room_type
            = f.semantic_dropdown :room_type,
              ['Single',
              'Single+ (Delta Eta and Delta Xi Only)',
              'Double',
              'Quad',
              'Triple',
              'Cold Dorm',
              'Fall Only',
              'Spring Only',
              'Not Living In House']
        - elsif wrp? && f.object.property_id.to_s == '96'
          .field
            = f.label :room_type
            = f.semantic_dropdown :room_type, ['Single', 'Double']

    .ui.very.padded.basic.vertical.segment.member-fields
      %h3.ui.header
        Member Information

      = f.fields_for :member, f.object.member do |mf|
        .equal.width.fields
          .required.field
            = mf.label :first_name, 'Legal First Name'
            = mf.text_field :first_name

          - if dphie?
            .field
              = mf.label :middle_name
              = mf.text_field :middle_name

          .required.field
            = mf.label :last_name, 'Legal Last Name'
            = mf.text_field :last_name

          .field
            = mf.label :nickname,
              trisigma? ? 'Nickname' : 'Informal First Name'
            = mf.text_field :nickname

        .equal.width.required.fields
          .field
            = mf.label :email do
              - if dphie?
                University Email
              - elsif sae?
                Personal Email
              - else
                Email
            = mf.email_field :email

          - if sae?
            .field
              = f.label :university_email
              = f.email_field :university_email

          .field
            = mf.label :phone
            = mf.text_field :phone

          - unless trisigma?
            .field
              = mf.label :student_id_number, 'Student ID Number'
              = mf.text_field :student_id_number

        .equal.width.required.fields
          .field
            = mf.label :date_of_birth, 'Date of Birth'
            = mf.date_field :date_of_birth

          .field
            = mf.label :social_security_number
            = mf.password_field :social_security_number, autocomplete: :off

          - if wrp?
            .field
              = mf.label :drivers_license_number
              = mf.text_field :drivers_license_number

          - if sae?
            .field
              = f.label :year_in_school
              = f.semantic_dropdown :year_in_school,
                [['Select', nil],
                ['Freshman', 'freshman'],
                ['Sophomore', 'sophomore'],
                ['Junior', 'junior'],
                ['Senior', 'senior']]

        - if dphie?
          .required.field
            = f.label :housing_staff_text_or_call,
              'Is it okay for the Housing Staff to text or call you if needed?'
            = f.semantic_dropdown :housing_staff_text_or_call,
              [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
              {},
              data: { options: { placeholder: 'Select' } }

        - if trisigma?
          .two.required.fields
            .field
              = f.label :anticipated_graduation_date
              = f.semantic_dropdown :anticipated_graduation_date,
                [['Select', nil],
                'Fall 2023', 'Spring 2024', 'Fall 2024', 'Spring 2025', 'Fall 2025', 'Spring 2026', 'Fall 2026', 'Spring 2027', 'Fall 2027', 'Spring 2028'],
                {},
                class: 'ui search selection dropdown',
                data: { options: { placeholder: 'Select' } }

            .field
              = f.label :major
              = f.text_field :major

        - if chio_beta_beta? || chio_mu?
          .three.required.fields
            .field
              = f.label :anticipated_graduation_date
              = f.semantic_dropdown :anticipated_graduation_date,
                [['Select', nil],
                'Fall 2023', 'Spring 2024', 'Fall 2024', 'Spring 2025', 'Fall 2025', 'Spring 2026', 'Fall 2026', 'Spring 2027', 'Fall 2027', 'Spring 2028'],
                {},
                class: 'ui search selection dropdown',
                data: { options: { placeholder: 'Select' } }

            .field
              = f.label :anticipated_study_abroad
              = f.semantic_dropdown :anticipated_study_abroad,
                [['Select', nil],
                'No', 'Fall 2023', 'Spring 2024', 'Fall 2024', 'Spring 2025', 'Fall 2025', 'Spring 2026', 'Fall 2026', 'Spring 2027'],
                {},
                class: 'ui search selection dropdown',
                data: { options: { placeholder: 'Select' } }

            .field
              = f.label :pledge_class
              = f.semantic_dropdown :pledge_class,
                [['Select', nil],
                'Fall 2018', 'Spring 2019', 'Fall 2019', 'Spring 2020', 'Fall 2020', 'Spring 2021', 'Fall 2021', 'Spring 2022', 'Fall 2022', 'Spring 2023', 'Fall 2023', 'Spring 2024'],
                {},
                class: 'ui search selection dropdown',
                data: { options: { placeholder: 'Select' } }

        - if barrister?
          .required.field
            = f.label :university, 'What university will you be attending?'
            = f.text_field :university

    - if wrp?
      -# Parking
      .ui.very.padded.basic.vertical.segment
        %h3.ui.header
          Parking

        .field
          = f.label :requested_parking, 'Are you interested in parking? (Please note, not all locations have available parking)'
          = f.semantic_dropdown :requested_parking,
            [['No', 'no'], ['Yes', 'yes']],
            {},
            id: :request_parking

        .vehicle-fields
          = f.fields_for :vehicle do |vf|
            .three.required.fields
              .field
                = vf.label :make
                = vf.text_field :make

              .field
                = vf.label :model
                = vf.text_field :model

              .field
                = vf.label :year
                = vf.number_field :year

            .three.required.fields
              .field
                = vf.label :color
                = vf.text_field :color

              .field
                = vf.label :license_plate_state
                = vf.text_field :license_plate_state

              .field
                = vf.label :license_plate_number
                = vf.text_field :license_plate_number

    - if needs_guarantor?
      .ui.very.padded.basic.vertical.segment.guarantor-fields
        %h3.ui.header
          Guarantor, Parent, or Guardian Information

        = f.fields_for :guarantor, f.object.guarantor do |gf|
          .two.required.fields
            .field
              = gf.label :first_name, 'Legal First Name'
              = gf.text_field :first_name

            .field
              = gf.label :last_name, 'Legal Last Name'
              = gf.text_field :last_name

          .two.required.fields
            .field
              = gf.label :email
              = gf.email_field :email

            .field
              = gf.label :phone
              = gf.text_field :phone

          - if wrp? || dphie?
            .two.required.fields
              .field
                = gf.label :social_security_number
                = gf.password_field :social_security_number, autocomplete: :off

              - unless dphie?
                .field
                  = gf.label :drivers_license_number
                  = gf.text_field :drivers_license_number

    .ui.very.padded.basic.vertical.segment
      %h3.ui.header
        - if wrp?
          Permanent / Forwarding Address
          = surround '(', ')' do
            %i not
            the chapter house address
        - elsif dphie?
          Home Mailing Address
          = surround '(', ')' do
            %i not
            the chapter house address
        - else
          Home Address

      - @default_address = f.object.address
      = render partial: 'shared/address_fields',
        locals: { f: f }

    - if dphie?
      -# Primary Emergency Contact
      .ui.very.padded.basic.vertical.segment
        %h3.ui.header
          Primary Emergency Contact

        = f.fields_for :emergency_contact do |ecf|
          .three.fields
            .required.field
              = ecf.label :first_name
              = ecf.text_field :first_name

            .field
              = ecf.label :middle_name
              = ecf.text_field :middle_name

            .required.field
              = ecf.label :last_name
              = ecf.text_field :last_name

          .three.fields
            .field
              = ecf.label :nickname, 'Informal First Name'
              = ecf.text_field :nickname

            .required.field
              = ecf.label :phone
              = ecf.text_field :phone

            .required.field
              = ecf.label :relation, 'Relation To Member'
              = ecf.text_field :relation

          .required.field
            = ecf.label :permission_to_contact,
              'Do we have permission to contact and share your health / emergency information with your emergency contact?'
            = ecf.semantic_dropdown :permission_to_contact,
              [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
              {},
              data: { options: { placeholder: 'Select' } }

    - if dphie? || chio_beta_beta?
      -# Medical Information
      .ui.very.padded.basic.vertical.segment
        %h3.ui.header
          Medical Information

        %p
          This information will remain confidential and strictly used in an
          emergency situation where first aid is required. If you are not
          comfortable providing this information, you may choose to skip this
          section. This information will not impact in any way your ability to
          live in the house.

        = f.fields_for :dphie_medical_information do |dmi|
          .field
            = dmi.label :food_allergies, 'Allergies, if any, to FOOD'
            = dmi.text_area :food_allergies, rows: 2

          .field
            = dmi.label :non_food_allergies, 'Allergies, if any, to NON-FOOD'
            = dmi.text_area :non_food_allergies, rows: 2

          .field
            = dmi.label :special_health_conditions,
              'Special Health Conditions (asthma, diabetes, etc.)'
            = dmi.text_area :special_health_conditions, rows: 2

          .field
            = dmi.label :special_medications_or_treatments,
              'Special medications or treatments (insulin, inhaler, etc.)'
            = dmi.text_area :special_medications_or_treatments, rows: 2

    - if dphie?
      -# Payment Plan
      .ui.very.padded.basic.vertical.segment
        %h3.ui.header
          Payment Plan / Financial Aid

        .required.field
          = f.label :dphie_payment_plan,
            'Will you be using the Delta Phi Epsilon payment plan program? (information below)'
          = f.semantic_dropdown :dphie_payment_plan,
            [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
            {},
            data: { options: { placeholder: 'Select' } }

        %p
          %i
            Payment plans require the payment of a $50 payment plan
            administrative fee each semester.

        %table.ui.very.basic.stackable.table
          %tbody
            %tr
              %td Fall Semester Payment Due Dates:
              %td Spring Semester Payment Due Dates:
            %tr
              %td June 1, 2024
              %td October 2, 2024
            %tr
              %td July 1, 2024
              %td November 2, 2024
            %tr
              %td August 1, 2024 or before move date, whichever one comes first.
              %td December 2, 2024 or before move date, whichever one comes first.

        .required.field
          = f.label :financial_aid,
            'Will you be using financial aid?'
          = f.semantic_dropdown :financial_aid,
            [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
            {},
            data: { options: { placeholder: 'Select' } }

        .required.field
          = f.label :payment_plan_529,
            'Will you be using a 529 payment plan?'
          = f.semantic_dropdown :payment_plan_529,
            [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
            {},
            data: { options: { placeholder: 'Select' } }

        .required.field
          = f.label :florida_prepaid_account,
            'Florida Prepaid Account (University of Florida Students Only)'
          = f.semantic_dropdown :florida_prepaid_account,
            [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
            {},
            data: { options: { placeholder: 'Select' } }

    - if wrp? && f.object.property_id.to_s == '96'
      .ui.very.padded.basic.vertical.segment
        %h3.ui.header
          Payment Preference

        %p
          %i
            There is an additional fee for selecting the payment plan option.

        .required.field
          = f.label :payment_preference, 'Payment Preference'
          = f.semantic_dropdown :payment_preference, ['Pay in Full', 'Payment Plan']

    - if barrister?
      .ui.basic.vertical.segment
        %h3.ui.header
          Room Preference Information

        %p
          There are no guarantees of room preference. Please select that you
          understand that you may have a roommate.

        %ul
          %li
            Singles are available only at a higher rental rate and are
            dependent on extremely limited availability. Seniority for Singles
            will be established by GPA / Credit Hours
          %li
            Doubles are the default at all properties.
          %li
            Triple rooms are only available at Illinois State, Miami and
            Missouri.

        .grouped.fields
          .field
            .ui.checkbox
              = f.check_box :barrister_terms, checked: false
              = f.label :barrister_terms, 'I Understand'

    .ui.basic.vertical.segment
      .grouped.fields
        .field
          .ui.checkbox
            = f.check_box :agree_to_sms, checked: true
            = f.label :agree_to_sms,
              t('views.tenant.agree_to_sms',
              customer_name: Customer.current.name)

        - if wrp? || dphie?
          - name = wrp? ? :wrp_terms : :dphie_terms
          .field
            .ui.checkbox
              = f.check_box name, checked: false
              = f.label name, 'I understand that if I don’t execute my lease in a timely manner prior to move in, it may delay my ability to move into the facility. I agree that the information entered here is accurate and true, and I understand the penalties that may be associated with entering false information.'

    .ui.basic.vertical.segment
      .ui.error.message

      .field
        = f.submit 'Continue', class: 'ui primary submit button'

:javascript
  $('.ui.checkbox').checkbox();

  var dropdown = $('#request_parking');

  var vehicleFields = $('.vehicle-fields');

  var toggleVehicle = function() {
    var value = dropdown.dropdown('get value');

    if (value === 'yes') {
      vehicleFields.slideDown(100);
    } else {
      vehicleFields.slideUp(100);
    }
  }

  vehicleFields.hide();

  setTimeout(function() {
    $('#request_parking').dropdown('setting', 'onChange', function () {
      toggleVehicle();
    });

    toggleVehicle();
  }, 10);
