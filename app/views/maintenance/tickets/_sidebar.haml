.action-sidebar
  .ui.fluid.dropdown{ data: { maintenance_ticket_target: 'serviceArea' } }
    %span.text Service Area
    %i.cog.icon
    .menu
      .ui.icon.search.input
        %i.search.icon
        %input{ placeholder: 'Search...', type: 'text' }
      .divider
      .ui.scrolling.menu
        .item

  = maintenance_ticket.service_area_links

  .ui.divider

  = render partial: 'maintenance/tickets/sidebar/owner',
    locals: { maintenance_ticket: maintenance_ticket }

  = render partial: 'maintenance/tickets/sidebar/tenant',
    locals: { maintenance_ticket: maintenance_ticket }

  .ui.fluid.dropdown{ data: { maintenance_ticket_target: 'tags' } }
    %span.text Tags
    %i.cog.icon
    .menu
      .ui.icon.search.input
        %i.search.icon
        %input{ placeholder: 'Search Tags...', type: 'text' }
      .divider
      .ui.scrolling.menu
        - applied_tags = maintenance_ticket.tags.pluck(:tag)
        - TagsQuery.new.search.for_maintenance_tickets.each do |tag|
          .item{ data: { value: tag.tag } }
            %i.icon{ class: applied_tags.include?(tag.tag) ? 'check' : nil }
            = tag.tag

        - if Feature.enabled?(:tag_management, Customer.current) && current_property_manager.role.administrator?
          = link_to 'Manage Tags...', settings_tags_path, class: 'item'
        - else
          = link_to 'Manage Tags...', maintenance_tags_path, class: 'item'

  - if maintenance_ticket.tags.none?
    None
  - else
    - maintenance_ticket.tags.each do |tag|
      = link_to tag.tag,
        maintenance_tickets_path(filters: { tag: tag.id }),
        class: 'ui small tag label'

  .ui.divider

  .ui.fluid.dropdown{ data: { maintenance_ticket_target: 'urgency' } }
    %span.text Urgency
    %i.cog.icon
    .menu
      .item Critical
      .item Normal
      .item Minor

  = maintenance_ticket.urgency.titleize

  .ui.divider

  .ui.fluid.dropdown{ data: { maintenance_ticket_target: 'employee',
    assignment_id: maintenance_ticket.assignment&.id } }
    %span.text Assigned Employee
    %i.cog.icon
    .menu
      .ui.icon.search.input
        %i.search.icon
        %input{ placeholder: 'Search Employees...', type: 'text' }
      .divider
      .ui.scrolling.menu
        - maintenance_ticket.assignable_employees.each do |employee|
          .item{ data: { value: employee.id } }
            %i.icon{ class: maintenance_ticket.assigned_user == employee ? 'check' : nil }
            = employee.name

  - if maintenance_ticket.assigned_user
    = employee_label(maintenance_ticket.assigned_user)
  - else
    None -

    = link_to 'Assign Yourself',
      maintenance_ticket_path(maintenance_ticket),
      method: :patch,
      remote: true,
      data: { params: { maintenance_ticket: { assignment_attributes: { user_id: current_property_manager.id } } }.to_param }
    \-
    = link_to 'Schedule', '#', id: 'schedule-button'

  .ui.divider

  = render Maintenance::WorkOrders::Sidebar::AssignedVendorsComponent.new(work_order: maintenance_ticket)

  .ui.divider

  = render Maintenance::WorkOrders::Sidebar::EstimatesComponent.new(work_order: maintenance_ticket)

  .ui.divider

  = render Maintenance::WorkOrders::Sidebar::ExpensesComponent.new(work_order: maintenance_ticket)

  = render Maintenance::WorkOrders::Sidebar::BillingRateComponent.new(work_order: maintenance_ticket)

  = render Maintenance::WorkOrders::Sidebar::MaterialsMarkupComponent.new(work_order: maintenance_ticket)

  = render Maintenance::WorkOrders::Sidebar::CustomDataComponent.new(work_order: maintenance_ticket)

:javascript
  if (!document.documentElement.hasAttribute('data-turbolinks-preview')) {
    if ($('#tenant-balance').length || $('#entity-balance').length) {
      $.get("/maintenance/tickets/#{@maintenance_ticket.id}/sidebar_balances");
    }
  }
