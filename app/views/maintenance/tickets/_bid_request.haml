- vendors = VendorsQuery.new.search.with_email.order(name: :asc)

.ui.tiny.modal#bid-request
  %i.close.icon
  .header
    Request Bid

  .content
    = form_with model: maintenance_ticket.bid_requests.build,
      url: maintenance_ticket_bid_requests_path(maintenance_ticket),
      id: :bid_request_form,
      scope: :bid_request,
      local: false,
      remote: true,
      class: 'ui form' do |f|

      .required.field
        = f.label :vendor_ids, 'Vendor(s)'
        = f.semantic_dropdown :vendor_ids,
          vendors.pluck(:name, :id),
          {},
          multiple: true,
          class: 'ui search selection dropdown',
          data: { options: { placeholder: 'Select' }, multiple: true }

      .field
        = f.label :message
        = f.text_area :message,
          rows: 3
      
      .field
        = f.uppy_dashboard_field :attachments, multiple: true, accept: 'image/*', use_s3: false, use_active_storage: true
      
      .field
        .ui.checkbox
          = f.check_box :include_attachments
          = f.label :include_attachments,
            'Include all attachments on the work order'

      .ui.error.message

  .actions
    %button.ui.cancel.button
      Cancel
    %button.ui.primary.button{ type: 'submit',
      form: 'bid_request_form',
      data: { disable: true } }
      %i.envelope.outline.alternate.icon
      Request

:javascript
  $('#bid-request-button').click(function (event) {
    event.preventDefault();
    $('#bid-request').modal({ ...modalDefaults, autofocus: false }).modal('show');
  });
