- if !@electronic_signature.countersigner? && Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
  - if @electronic_signature.recipient == @electronic_signature.document.primary_tenant
    = render partial: 'sae_mailers/request_signature_member'
  - else
    = render partial: 'sae_mailers/request_signature_guardian'

- elsif Customer.current_subdomain.start_with?('chio-mu')
  = render partial: 'electronic_signatures_mailer/chio_mu_request'

- else
  Hello #{@electronic_signature.recipient.first_name},

  - if @electronic_signature.document.is_a?(Lease)
    - if @electronic_signature.countersigner?
      #{@requested_by_name} has requested you
      review and countersign a lease agreement.

      = link_to 'Click here to review and sign the Lease Agreement.',
        electronic_signature_url(@electronic_signature,
        subdomain: Customer.current_subdomain)

    - else
      %p
        Congratulations on your new lease!
        - if Customer.current_subdomain.start_with?('dphie')
          Housing
        - else
          = @requested_by_name
        has sent you a lease agreement to review and sign for your new home at
        #{@property.name}.

      = link_to 'Click here to review and sign the Lease Agreement.',
        electronic_signature_url(@electronic_signature,
        subdomain: Customer.current_subdomain)

      %p
        After you sign the Lease Agreement, the property manager will countersign
        and all parties will receive a final copy via email.

      - if Customer.current_subdomain == 'brown'
        %p
          Lease must be signed within 5 days of receipt.

  - elsif @electronic_signature.document.is_a?(Lease::Addendum)
    - if @electronic_signature.countersigner?
      #{@requested_by_name} has requested you
      review and countersign a lease addendum.

      = link_to 'Click here to review and sign the lease addendum.',
        electronic_signature_url(@electronic_signature,
        subdomain: Customer.current_subdomain)

    - else
      %p
        #{@requested_by_name} has sent you a lease
        addendum to review and sign.

      = link_to 'Click here to review and sign the lease addendum.',
        electronic_signature_url(@electronic_signature,
        subdomain: Customer.current_subdomain)

      %p
        After you sign the addendum, the property manager will countersign and
        all parties will receive a final copy via email.

  - else
    - if @electronic_signature.countersigner?
      #{@requested_by_name} has requested you
      review and countersign an agreement.

      = link_to 'Click here to review and sign the agreement.',
        electronic_signature_url(@electronic_signature,
        subdomain: Customer.current_subdomain)

    - else
      %p
        #{@requested_by_name} has sent an agreement
        for you to review and sign.

      = link_to 'Click here to review and sign the agreement.',
        electronic_signature_url(@electronic_signature,
        subdomain: Customer.current_subdomain)

      %p
        After you sign the agreement, the property manager will countersign and
        all parties will receive a final copy via email.

  %p
    Have a nice day!
