- lease = @lease_membership&.lease&.decorate

.metrics
  %h2.ui.header
    Current Balance

  .ui.padded.stackable.grid
    .ui.six.wide.column
      = link_to @balance.format,
        tenants_rent_invoices_path,
        class: 'balance-box'
    .ui.ten.wide.column
      .ui.large.list
        .item
          .header
            Electronic Payments
          - if available_payment_methods.any?
            = link_to 'View Payment Methods', tenants_account_path
          - else
            You do not have a payment method setup.
            %br
            = link_to 'Add Payment Method',
              new_tenants_account_payment_method_path

        = render Tenants::Dashboard::PaymentPlansItemComponent.new(tenant: current_tenant)

        - unless Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
          .item
            .header
              Scheduled Payments

            - if @scheduled_payments.any?
              - @scheduled_payments.each do |scheduled_payment|
                = link_to scheduled_payment.description,
                  [:tenants, :account, scheduled_payment]
                - if scheduled_payment.failed?
                  failed.
                  = link_to 'Click here',
                    [:tenants, :account, scheduled_payment]
                  to retry or remove your scheduled payment.
                - else
                  will pay
                  - if scheduled_payment.pay_balance?
                    your balance
                  - else
                    = scheduled_payment.amount.format
                  on #{scheduled_payment.next_payment_date.strftime('%B %-d, %Y')}.

                %br

            - elsif available_payment_methods.any?
              You do not have any scheduled payments.

              - unless Customer.current_subdomain.start_with?('chio-mu', 'chiohc-msu')
                %br
                = link_to 'Add Scheduled Payment',
                  new_tenants_account_scheduled_payment_path

            - else
              You must have a funding source prepared to use scheduled payments.

          - if lease
            .item
              .header
                - if Customer.current.home_owners_association? || california?
                  Agreement Details
                - else
                  Lease Details

              - if lease.month_to_month?
                Month to Month
              - else
                = lease.start_date.strftime('%B %-d, %Y')
                to
                = lease.end_date.strftime('%B %-d, %Y')

              %p
                = lease.tenant_portal_description
