%h4.ui.header
  Quick Links

:ruby
  lease = current_tenant.current_lease || current_tenant.leases.last
.action-boxes
  = link_to new_tenants_payment_path,
    class: 'box' do
    .image
      %i.credit.card.outline.icon
    %h4.ui.header
      Payment
    .description
      Make A Payment

  - if lease
    = link_to new_tenants_maintenance_ticket_path,
      class: 'box' do
      .image
        %i.wrench.icon
      %h4.ui.header
        Request Maintenance
      .description
        Create a Maintenance Ticket

  = link_to new_tenants_message_path,
    class: 'box' do
    .image
      %i.envelope.outline.icon
    %h4.ui.header
      Message
    .description
      Send A Message

  - if lease
    = link_to tenants_agreement_path(lease, type: 'leases'),
      class: 'box' do
      .image
        %i.file.text.outline.icon
      %h4.ui.header
        - if Customer.current.home_owners_association?
          Agreement
        - elsif california?
          Member Agreement
        - else
          Lease Agreement
      .description
        View Your
        - if Customer.current.home_owners_association?
          Agreement
        - elsif california?
          Member Agreement
        - else
          Lease Agreement
