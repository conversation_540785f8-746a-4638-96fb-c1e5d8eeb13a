%h2.ui.header
  Guarantor or Guardian Information
= form_with model: @guarantor, url: create_guarantor_profile_tenants_member_onboarding_path(onboarding),
  method: :post,
  class: class_names('ui form', 'error' => @guarantor.errors.any?),
  id: 'member_onboarding_guarantor_profile',
  scope: :guarantor,
  data: { local: false, remote: true } do |f|
  - guarantor_step = onboarding_wizard.steps[:guarantor_profile]
  .ui.error.message
    = render partial: 'shared/ujs/error_list', locals: { errors: (@guarantor.errors.full_messages.presence || flash[:errors] || []) }
  - guarantor_metadata = (f.object.metadata || f.object.build_metadata)
  .ui.basic.vertical.segment
    %h3.ui.header
      Guarantor/Guardian Information

    - if @required_information.optional?
      .required.field
        .ui.checkbox
          = f.check_box :skip_guarantor, checked: guarantor_step.skip_guarantor
          = f.label :skip_guarantor, 'Skip Guarantor/Guardian Profile?'

    .equal.width.fields
      .required.field
        = f.label :first_name, 'Legal First Name'
        = f.text_field :first_name, autocomplete: :off

      .required.field
        = f.label :last_name, 'Legal Last Name'
        = f.text_field :last_name, autocomplete: :off

    .equal.width.fields
      .required.field
        = f.label :email
        .ui.action.input
          = f.email_field :email, autocomplete: :off

      .required.field
        = f.label :phone
        = f.text_field :phone, autocomplete: :off


    - if @required_information.gathering_collections_information?
      .equal.width.fields
        .required.eight.wide.field
          = f.label :date_of_birth, 'Date of Birth'
          = f.semantic_date_field :date_of_birth

        = f.fields_for :taxpayer_identification, (f.object.taxpayer_identification || f.object.build_taxpayer_identification) do |t|
          .required.eight.wide.field
            = t.hidden_field :id, disabled: t.object.id.present?
            = t.label :tin, 'Social Security Number'
            = t.hidden_field :tin_type, value: 'ssn'
            - ssn_placeholder = nil
            - if t.object.tin.present?
              - ssn_placeholder = t.object.starred_tin(4)
              = t.hidden_field :encrypted_tin
              = t.hidden_field :encrypted_tin_iv
            - else
              - ssn_placeholder = 'Social Security Number'
            = t.password_field :tin, value: nil, placeholder: ssn_placeholder



    = f.fields_for :metadata, guarantor_metadata do |maf_u|
      = maf_u.hidden_field :id, disabled: maf_u.object.id.present?
      = maf_u.fields_for :data, guarantor_metadata.data || {} do |maf|
        - if @required_information.gathering_drivers_license_number?
          .required.eight.wide.field
            = maf.label :drivers_license_number
            = maf.text_field :drivers_license_number, value: guarantor_metadata.data['drivers_license_number']

  - if @required_information.gathering_address?
    .ui.basic.vertical.segment
      %h3.ui.header
        Forwarding Address
      - @default_address = f.object.forwarding_address || f.object.build_forwarding_address
      = render partial: 'shared/address_fields', locals: { f: f, address_field_name: :forwarding_address }

:javascript
  $('.ui.checkbox').checkbox();
