%h2.ui.header
  Member Information
= form_with model: @member, url: create_member_profile_tenants_member_onboarding_path(onboarding),
  method: :post,
  class: 'ui form',
  id: 'member_onboarding_member_profile',
  scope: :member,
  data: { local: false, remote: true } do |f|
  - member_metadata = (f.object.metadata || f.object.build_metadata)
  .ui.error.message
  .ui.basic.vertical.segment
    %h3.ui.header
      Member Information

    .fields
      .required.eight.wide.field.disabled
        = f.label :first_name, 'Legal First Name'
        = f.text_field :first_name, autocomplete: :off, disabled: true

      .required.eight.wide.field.disabled
        = f.label :last_name, 'Legal Last Name'
        = f.text_field :last_name, autocomplete: :off, disabled: true
      - if @required_information.gathering_nickname?
        = f.fields_for :metadata, member_metadata do |maf_u|
          = maf_u.fields_for :data, member_metadata.data || {} do |maf|
            .eight.wide.field
              = maf.label :nickname, 'Nickname'
              = maf.text_field :nickname, value: member_metadata.data['nickname']

    .equal.width.fields
      .required.field.disabled
        = f.label :email
        = f.email_field :email, autocomplete: :off, disabled: true

      .required.field
        = f.label :phone
        = f.text_field :phone, value: f.object.phone&.phony_formatted, autocomplete: :off


    - if @required_information.gathering_collections_information?
      .equal.width.fields
        .required.field
          = f.label :date_of_birth, 'Date of Birth'
          = f.semantic_date_field :date_of_birth

        = f.fields_for :taxpayer_identification, (f.object.taxpayer_identification || f.object.build_taxpayer_identification) do |t|
          .required.field
            - if t.object.id.present?
              = t.hidden_field :id, value: t.object.id
            = t.label :tin, 'Social Security Number'
            = t.hidden_field :tin_type, value: 'ssn'
            = t.password_field :tin, value: t.object.tin, placeholder: 'Social Security Number'


    = f.fields_for :metadata, member_metadata do |maf_u|
      = maf_u.fields_for :data, member_metadata.data || {} do |maf|
        - if @required_information.gathering_student_id?
          .required.eight.wide.field
            = maf.label :student_id_number, 'Student ID Number'
            = maf.text_field :student_id_number, value: member_metadata.data['student_id_number']
        - if @required_information.gathering_school_year?
          .required.eight.wide.field
            = maf.label :year_in_school, 'School Year'
            = maf.semantic_dropdown :year_in_school,
                [['Select', nil],
                ['Freshman', 'freshman'],
                ['Sophomore', 'sophomore'],
                ['Junior', 'junior'],
                ['Senior', 'senior']],
                { selected: member_metadata.data['year_in_school'] }
        - if @required_information.gathering_drivers_license_number?
          .required.eight.wide.field
            = maf.label :drivers_license_number
            = maf.text_field :drivers_license_number, value: member_metadata.data['drivers_license_number']

  - if @required_information.gathering_address?
    .ui.basic.vertical.segment
      %h3.ui.header
        Forwarding Address
      - forwarding_address = f.object.forwarding_address || f.object.build_forwarding_address
      = render partial: 'shared/address_fields',
        locals: { f: f, address_field_name: :forwarding_address, default_address: forwarding_address }

  .ui.basic.vertical.segment
    - if @required_information.gathering_additional_questions?
      %h3.ui.header{style: 'margin-bottom:2rem;'}
        Additional Information
      = f.fields_for :metadata, member_metadata do |maf_u|
        = maf_u.hidden_field :id, disabled: maf_u.object.id.present?
        = maf_u.fields_for :data, member_metadata.data || {} do |maf|
          .ui.grid
            - (onboarding.information_collection&.additional_questions || []).each do |af|
              = render partial: "tenants/member_onboardings/additional_questions/#{af['type']}", locals: { maf: maf, af: af, required_information: @required_information, member_metadata: member_metadata }
    .ui.checkbox{style: 'margin-top:2rem;'}
      = f.check_box :agree_to_sms, checked: f.object.agreed_to_sms_at.present?
      = f.label :agree_to_sms, t('views.tenant.agree_to_sms', customer_name: Customer.current.name)

:javascript
  $('.ui.checkbox').checkbox();
  $('.ui.dropdown').dropdown();
