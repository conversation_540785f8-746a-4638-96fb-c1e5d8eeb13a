:ruby
  paid, unpaid = @invoices.partition(&:paid?)
  properties = unpaid.map(&:seller).uniq.map do |seller|
    [seller, unpaid.filter { |un| un.seller == seller }]
  end

.ui.container
  - if properties.size == 1 && @balance.positive? && (@balance < Monetize.parse('$2,500') || Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]))
    = link_to 'Make a Payment',
      new_tenants_payment_path,
      class: 'right floated ui button'

  %h1.ui.header
    - if Customer.current.greek_housing? || Customer.current.university? || Customer.current.home_owners_association?
      = page_title 'Charges'
    - else
      = page_title 'Rent and Charges'
    .sub.header
      Current Balance of #{@balance.format}

  %h3.ui.dividing.header
    Unpaid
  .ui.middle.aligned.divided.selection.list
    - if unpaid.any? && properties.size > 1
      - properties.each do |property, invoices|
        .invoices-header
          %h4.ui.header= property.name
          = link_to "Pay #{property.name} Balance...",
            new_tenants_payment_path(property_id: property.id),
            class: 'ui button'
        - invoices.each do |invoice|
          = render partial: 'shared/rent_invoices/row',
            locals: { invoice: invoice, path: tenants_rent_invoice_path(invoice) }
    - elsif unpaid.any?
      - unpaid.each do |invoice|
        = render partial: 'shared/rent_invoices/row',
          locals: { invoice: invoice, path: tenants_rent_invoice_path(invoice) }
    - else
      %i None

  %h3.ui.dividing.header
    Paid
  .ui.middle.aligned.divided.selection.list
    - if paid.any?
      - paid.each do |invoice|
        = render partial: 'shared/rent_invoices/row',
          locals: { invoice: invoice, path: tenants_rent_invoice_path(invoice) }
    - else
      %i None
