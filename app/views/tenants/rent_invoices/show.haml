:ruby
  property_id = @invoice.seller_id if @invoice.seller.is_a?(Property)

  breadcrumb_text = if Customer.current.greek_housing? || Customer.current.university? || Customer.current.home_owners_association?
                      'Charges'
                    else
                      'Rent and Charges'
                    end

.ui.container
  .ui.two.column.grid
    .column
      .ui.breadcrumb
        = link_to breadcrumb_text,
          tenants_rent_invoices_path,
          class: 'section'
        %i.right.angle.icon.divider
        %h1.ui.header
          = page_title @invoice.to_s
          .sub.header
            - if @invoice.paid?
              Paid
            - else
              Unpaid
    .column
      = print_button

      - unless @invoice.paid?
        - if @invoice.payment_plans_available?
          = link_to 'Setup Payment Plan',
            new_tenants_payment_plan_path(invoice_id: @invoice.id),
            class: class_names('right floated ui button',
            disabled: @invoice.payment_plans.any?)

        = link_to 'Pay Now...',
          new_tenants_rent_invoice_payment_path(@invoice,
          property_id: property_id),
          class: 'right floated ui button'

  - if @active_payment_plan
    .ui.info.message
      .header Payment Plan
      %p
        This bill is associated with an
        = succeed '.' do
          = link_to 'active payment plan',
            tenants_payment_plan_path(@active_payment_plan)

  = render partial: 'shared/invoice', locals: { invoice: @invoice }
