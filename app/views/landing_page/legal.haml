:ruby
  links = {
    'Terms of Use' => terms_path,
    'Privacy Policy' => privacy_path,
    'Deposit Account End User Agreement' => legal_unit_deposit_account_end_user_agreement_path,
    'Deposit Account Disclosures' => legal_unit_deposit_account_disclosures_path,
    'Debit Cardholder Agreement' => legal_unit_debit_cardholder_agreement_path,
    'Electronic Disclosure Consent' => legal_unit_electronic_disclosure_consent_path
  }

.ui.basic.padded.segment{ style: 'flex-grow: 1;' }
  .ui.text.container
    %h1.ui.big.heavy.dashed.header
      = meta_title 'Legal'

    %br

    %ul.ui.list
      - links.each do |title, path|
        %li.item
          = link_to title, path, target: '_blank'
