.ui.vertical.segment
  %h4.ui.header Payment Methods

  = render ElectronicPayments::PaymentMethodsListComponent.new(contact: tenant,
    remove_path_proc: ->(method) { tenant_payment_method_path(tenant, id: method.to_sgid.to_s) })

  %br

  = link_to new_tenant_payment_method_path(tenant),
    class: 'ui basic compact button' do
    %i.plus.icon
    Add Payment Method

.ui.padded.vertical.segment
  %h4.ui.header
    Scheduled Payments

  .ui.selection.list
    = render partial: 'shared/scheduled_payment',
      collection: ScheduledPaymentsQuery.for_tenant(tenant).upcoming

  = link_to new_tenant_scheduled_payment_path(tenant),
    class: 'ui basic compact button' do
    %i.plus.icon
    Add Scheduled Payment

- if Feature.enabled?(:payment_plans, Customer.current)
  .ui.padded.vertical.segment
    %h4.ui.header
      Payment Plans

    - plans = PaymentPlansQuery.new.search.by_tenant(tenant).with_current_or_upcoming_installments

    - if plans.none?
      %span.ui.grey.text
        %i None
    - else
      .ui.selection.list
        = render partial: 'shared/payment_plan', collection: plans
