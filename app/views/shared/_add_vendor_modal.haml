= render ModalComponent.new id: :vendor_form_modal,
  title: "Add a Vendor",
  options: { submit: { text: 'Create Vendor' },
  cancel: { text: 'Cancel' }, form: :vendor_form, detachable: false } do

  .actions
    = form_with model: Vendor.new,
      url: vendors_path,
      method: :post,
      local: false,
      remote: true,
      class: 'ui form',
      html: { 'data-type' => :json },
      data: { action: ['ajax:error->tunisia--bank-accounts--card-activities--transaction#addVendorError',
      'ajax:success->tunisia--bank-accounts--card-activities--transaction#addVendor',
      'ajax:success->modal#close'].join(' ')},
      id: :vendor_form do |f|

      .ui.error.message

      .two.required.fields
        .field
          = f.label :name
          = f.text_field :name

        .field
          = f.label :kind, 'Type'
          = f.semantic_dropdown :kind,
            Vendor.kinds.keys.sort.map { |k| [k.humanize, k] }

      .two.fields
        .field
          = f.label :business_type, 'Entity Type'
          = f.semantic_dropdown :business_type,
            Taxpayer.business_type_dropdown_values,
            { selected: f.object&.business_type },
            class: 'ui search selection dropdown',
            data: { options: { placeholder: 'Select' } }

        .field
          = f.fields_for :taxpayer_identification do |t|
            = render partial: 'shared/taxpayer_fields', locals: { f: t }

      .field
        = f.label :tags
        = f.tags_field

      .field
        = f.label :notes
        = f.text_area :notes, rows: 2
