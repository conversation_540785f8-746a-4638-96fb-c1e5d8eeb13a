:javascript
  $('#select-file').click(function () {
    $('#file-input').click();
  });

  var handleFiles = function (files) {
    var file = files[0];

    $('#file-dropzone').addClass('disabled');
    $('.segment#progress').slideDown(100);

    $('.ui.list#log').append('Uploading ' + file.name);

    var formData = null;

    var optionsForm = document.querySelector('form#data_import_options');

    if (optionsForm) {
      formData = new FormData(optionsForm);
    } else {
      formData = new FormData();
    }

    formData.append('upload', file);

    $.ajax({
      url: window.location.pathname,
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success: function(data) {
        console.log('success', data);
      },
    });
  };

  $('#file-input').change(function (event) {
    var input = $('#file-input')[0];
    handleFiles(input.files);
  });

  var dropArea = document.getElementById('file-dropzone');

  var preventDefaults = function (e) {
    e.preventDefault();
    e.stopPropagation();
  };

  var hoverOn = function () {
    dropArea.classList.add('hovering');
  };

  var hoverOff = function () {
    dropArea.classList.remove('hovering');
  };

  ;['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, preventDefaults, false)
  });

  ;['dragenter', 'dragover'].forEach(eventName => {
    dropArea.addEventListener(eventName, hoverOn, false)
  });

  ;['dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, hoverOff, false)
  });

  var handleDrop = function (event) {
    var dataTransfer = event.dataTransfer;
    var files = dataTransfer.files;
    handleFiles(files);
  };

  dropArea.addEventListener('drop', handleDrop, false);

  var latest_message = null;
  var latest_percent = 0;
  var latest_status = null;

  if (!window.actionCableConsumer) {
    window.actionCableConsumer = ActionCable.createConsumer();

    window.actionCableConsumer.subscriptions.create(
      { channel: 'DataImportsChannel' },
      {
        received(data) {
          if (data.data_import) {
            updated_at = Date.parse(data.data_import.updated_at);
            percent = parseInt(data.data_import.percent, 10);
            status = data.data_import.status;

            if (latest_message && updated_at >= latest_message) {
              var log = data.data_import.log;

              if (percent > latest_percent) {
                $('.ui.progress').progress('set percent', percent);
              }

              if (status == 'succeeded') {
                $('#archive_data_import').show();
              }

              html = '';

              log.split("\n").forEach(function (line) {
                html += "<div class='item'>" + line + '</div>';
              });

              $('#log').html(html);

              if (status !== latest_status && status == 'failed') {
                $('.ui.progress').removeClass('teal').addClass('red');
                $('#file-dropzone').slideDown(100);
                $('#file-dropzone').removeClass('disabled');
                $('#archive_data_import').show();
              }
            }

            latest_message = Math.max(latest_message, updated_at);
            latest_percent = Math.max(latest_percent, percent);
            latest_status = status;
          }
        }
      }
    );
  }

:css
  .segment {
    transition: box-shadow .2s;
  }

  .segment.hovering {
    box-shadow: rgba(34, 36, 38, 0.2) 0px 2px 25px 0px inset !important;
  }
