.ui.info.message{ style: 'margin-top: 0;' }
  .header
    = t('activerecord.models.inspection/report')
    Templates
  %p
    #{t('activerecord.models.inspection/report').pluralize} can be automatically generated and assigned to
    #{I18n.t('tenant_term').downcase.pluralize} during move-in and move-out.

- %w[in out].each do |direction|
  - key = "move_#{direction}_inspection_template_id"

  .field
    = f.label key,
      "Move-#{direction.capitalize} #{t('activerecord.models.inspection/report')} Template"
    = f.semantic_dropdown key,
      [['None', nil]] + Inspection::Template.pluck(:name, :id),
      { selected: f.object.public_send(key) },
      data: { options: { placeholder: 'None' } }
