.ui.printable.paper
  - if guest_card.archived?
    .ui.warning.message
      .header
        Archived
      %p
        This guest card was archived on
        #{guest_card.archived_at.to_fs(:human_datetime)}.

  %h2.ui.header
    Guest Card
    .sub.header
      Submitted #{guest_card.created_at.to_fs(:human_datetime)}

  .ui.two.column.stackable.padded.grid
    .column
      %h4.ui.dividing.header
        Contact Information
      .ui.tiny.reversed.horizontal.left.aligned.statistics
        .statistic
          .label Name
          .value
            = link_to guest_card.name,
              guest_card.tenant.url
        .statistic
          .label Phone
          .value= phone_link(guest_card.phone) || 'Unspecified'
        .statistic
          .label Email
          .value
            = mail_to guest_card.email
    .column
      %h4.ui.dividing.header
        Interested In
      .ui.tiny.reversed.horizontal.left.aligned.statistics
        .statistic
          .label Portfolio
          .value
            - if guest_card.portfolio
              = link_to guest_card.portfolio.name,
                portfolio_path(guest_card.portfolio)
            - else
              Unspecified
        .statistic
          .label Property
          .value
            - if guest_card.property
              = link_to guest_card.property.name,
                property_path(guest_card.property)
            - else
              Unspecified
        .statistic
          .label Floorplan
          .value= guest_card.floorplan&.name || 'Unspecified'
        .statistic
          .label Unit
          .value
            - if guest_card.unit
              = link_to guest_card.unit.name, unit_path(guest_card.unit)
            - else
              Unspecified

    .sixteen.wide.column
      %h4.ui.dividing.header
        Additional Details
      .ui.list
        .item
          .content
            .header
              Agent
            .description
              - if guest_card.internal_source.present?
                = link_to guest_card.internal_source.name,
                  organization_employee_path(guest_card.internal_source)
              - else
                None

        .item
          .content
            .header
              Preferred Contact Method
            .description
              = guest_card.preferred_contact_method&.titleize || 'Any'

        = render partial: 'shared/guest_card/data',
          locals: { data: guest_card.data.except('first_name',
          'last_name', 'email', 'phone', 'floorplan',
          'property').merge('Desired Move-In Date' => guest_card.move_in_date,
          'Desired Move-Out Date' => guest_card.move_out_date,
          'External Source' => guest_card.external_source&.source) }

      - if Customer.current_subdomain == 'gebrael'
        %h4.ui.dividing.header
          Attachments
        .ui.list
          .item
            .content
              .header Drivers License
            .description
              - if guest_card.drivers_license_copy.present?
                = link_to guest_card.drivers_license_copy.filename,
                  guest_card.drivers_license_copy.expiring_url,
                  target: '_blank'
              - else
                None
          .item
            .content
              .header Recent Paystubs
            .description
              - if guest_card.paystubs.present?
                = link_to guest_card.paystubs.filename,
                  guest_card.paystubs.expiring_url,
                  target: '_blank'
              - else
                None

    - if guest_card.tours.any? || guest_card.requested_tour?
      .sixteen.wide.column
        %h4.ui.dividing.header
          Tour
        - if guest_card.tours.any?
          = render guest_card.tours
        - else
          #tour-request
            - if guest_card.requested_tour?
              %p
                #{guest_card.first_name} requested a tour at one of the following
                times (click to select):
              .ui.selection.list#tour-times
                - guest_card.requested_tour_times.each do |time|
                  %a.item
                    = time.to_fs(:human_datetime_long)

              :ruby
                tour = current_property_manager.tours.build(
                  guest_card: guest_card,
                  property: guest_card.property,
                  lead: guest_card.tenant
                )

              = render partial: 'leasing/tours/form',
                locals: { tour: tour, local: false }

:javascript
  $('#tour-times .item').click(function () {
    $('#tour_time').closest('.ui.calendar').calendar('set date', $(this).html());
  });
