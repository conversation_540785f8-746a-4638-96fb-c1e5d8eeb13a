:ruby
  portfolio = nil
  entity = nil
  owners = nil
  property = nil
  tenant = nil
  vendor = nil

  property = invoice.buyer if invoice.buyer.is_a?(Property)
  property ||= invoice.seller if invoice.seller.is_a?(Property)

  entity = invoice.buyer if invoice.buyer.is_a?(Company) && invoice.buyer.customer_managed?
  entity ||= invoice.seller if invoice.seller.is_a?(Company) && invoice.seller.customer_managed?

  tenant = invoice.buyer if invoice.buyer.is_a?(Tenant)
  tenant ||= invoice.seller if invoice.seller.is_a?(Tenant)

  membership = invoice.journal_entries.map(&:lease_membership).compact.uniq.first
  lease = membership.lease if membership

  vendor = invoice.buyer if invoice.buyer.is_a?(Vendor)
  vendor ||= invoice.seller if invoice.seller.is_a?(Vendor)

  entity ||= property&.company
  owners ||= entity&.owners
  portfolio ||= entity&.portfolio

  forwarded_to = invoice.forwarded_to.includes(:buyer)
  forwarded_from = invoice.forwarded_from

  ledger = invoice.ledger&.decorate

  associated_work_orders =
    MaintenanceTicketsQuery
    .new.search
    .associated_with_invoice(invoice)

  can_add_recurring = true if can_add_recurring.nil?

- if portfolio
  - s.with_item title: 'Portfolio' do
    = link_to portfolio.name, portfolio_path(portfolio)

- if entity
  - s.with_item title: 'Entity' do
    = link_to entity.name, entity.url

- if owners.present?
  - s.with_item title: 'Owners' do
    .ui.list
      - owners.each do |owner|
        = link_to owner.name, owner_path(owner), class: 'item'

- if property
  - s.with_item title: 'Property' do
    = link_to property.name, property_path(property)

- if tenant
  - s.with_item title: I18n.t('tenant_term') do
    = link_to tenant.name, tenant.url

- if lease
  - s.with_item title: I18n.t('lease_term') do
    = link_to "#{I18n.t('lease_term')} #{lease.id}", leasing_lease_path(lease)

- if vendor
  - s.with_item title: 'Vendor' do
    = link_to vendor.name, vendor_path(vendor)

- if ledger
  - s.with_item title: 'Ledger' do
    = link_to ledger.sidebar_description, ledger.report_path

- if forwarded_to.any?
  - s.with_item title: 'Forwarded To' do
    .ui.list
      - forwarded_to.each do |invoice|
        = link_to invoice.url, class: 'item' do
          = invoice.invoice_number
          &nbsp;
          = invoice.amount.format
          &nbsp;
          = invoice.buyer.name.truncate(24)

- if forwarded_from
  - s.with_item title: 'Forwarded From' do
    = link_to forwarded_from.url do
      = forwarded_from.invoice_number
      &nbsp;
      = forwarded_from.amount.format
      &nbsp;
      = forwarded_from.seller.name.truncate(24)

- if invoice.payment_batch
  - s.with_item title: 'Payables Batch' do
    = link_to invoice.payment_batch.name,
      accounting_payables_batch_path(invoice.payment_batch)

- if associated_work_orders.any?
  - s.with_item title: 'Maintenance Tickets' do
    .ui.list
      - associated_work_orders.each do |linked_maintenance_ticket|
        = link_to linked_maintenance_ticket.subject,
          linked_maintenance_ticket.url,
          class: 'item'

- if invoice.linked_projects.any?
  - s.with_item title: 'Projects' do
    .ui.list
      - invoice.linked_projects.each do |linked_project|
        = link_to linked_project.name, linked_project.url, class: 'item'

- if Feature.enabled?(:recurring_invoices, Customer.current)
  - require_permission can_add_recurring do
    - s.with_item title: 'Recurring Schedule' do
      - if invoice.recurring_schedule
        %div{ style: 'padding-bottom: 1rem;' }
          %span.ui.grey.text
            = invoice.recurring_schedule.description

        = link_to 'View',
          '#',
          data: { modal: :recurring_schedule_modal }

        &emsp;

        = link_to 'Cancel',
          accounting_invoice_recurring_schedule_path(invoice),
          method: :delete,
          data: { confirm: 'Remove Schedule?' }
      - else
        = link_to 'Add Schedule',
          '#',
          data: { modal: :recurring_schedule_modal }

      = render RecurringSchedule::FormModalComponent.new(recurrable: invoice)

- if Feature.enabled?(:payment_plans, Customer.current)
  - if invoice.buyer.is_a?(Tenant)
    - s.with_item title: 'Payment Plan' do
      - if invoice.payment_plans.any?
        .ui.list
          - invoice.payment_plans.each do |payment_plan|
            .item
              = link_to payment_plan.decorate.sidebar_description,
              tenant_payment_plan_path(invoice.buyer, payment_plan)
      - elsif invoice.payment_plan_eligible?
        None
      - else
        = link_to 'Ineligible',
          edit_accounting_receivables_invoice_path(invoice)
