:ruby
  alignment = cell[:justify]
  sort = cell[:sort]
  value = cell[:value]
  type = cell[:type]
  style = cell[:style]
  color = cell[:color]
  colspan = cell[:colspan]

  if type == '%'
    sort ||= value
    if value
      value = "#{(value * 100).round(2)}%"
    else
      value = '-'
    end
    alignment ||= 'right'
  end

  if value.is_a?(Date)
    sort ||= value.to_datetime.to_i
    value = value.strftime(Reports::V3::DATE_FORMAT)
  elsif value.is_a?(Time)
    sort ||= value.to_i
    value = value.strftime(Reports::V3::DATETIME_FORMAT)
  elsif value.is_a?(Money)
    sort ||= value.cents
    color ||= 'red' if value.negative?
    alignment ||= 'right'
    value = value.accounting_format
  elsif value.is_a?(Numeric)
    sort ||= value
    value = number_with_delimiter(value)
    alignment ||= 'right'
  end

  alignment ||= 'left'

  display = value.to_s

  # Truncate and display tooltip if necessary
  title = nil
  truncate_value = cell[:truncate]
  unless truncate_value == false
    truncate_length = cell[:truncate] || Reports::V3::MAX_DISPLAY_LENGTH
    if display.length > truncate_length
      title = display
      display = display.truncate(truncate_length)
    end
  end

  if cell[:depth]
    display = ('&emsp;' * cell[:depth]) + display
  end

  style ||= "color: #{color};" if color

  html_tag = footer ? 'th' : 'td'

  show_link = allow_links && cell[:link].present?

= content_tag html_tag,
  class: "#{alignment} aligned",
  title: title,
  style: style,
  colspan: colspan,
  'data-sort-value' => sort do

  - if show_link
    = link_to cell[:link], onclick: cell[:onclick] do
      = simple_format(display)
  - elsif cell[:type].in?(['b', 'b$'])
    %b= simple_format(display)
  - elsif cell[:type].in?(['h3'])
    %h3= simple_format(display)
  - elsif cell[:type].in?(['h4'])
    %h4= simple_format(display)
  - else
    = simple_format(display)
