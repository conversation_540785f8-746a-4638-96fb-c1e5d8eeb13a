.action-bar
  .ui.container
    %h2.ui.header
      Rent Schedule Report

    .ui.form
      .three.fields{ style: 'margin: 0' }
        .field{ style: 'padding: 0' }
          = react_component 'TurbolinksDateSelector', queryKey: 'start_date', placeholder: 'Start'
        .field{ style: 'padding: 0' }
          = react_component 'TurbolinksDateSelector', queryKey: 'end_date', placeholder: 'End'
        .field
          = react_component 'TurbolinksPortfolioSelector'

    .action.buttons
      = print_button
      %span{ style: 'width: 10px' }
      = link_to params.permit(:start_date,
        :end_date, :portfolio).merge(format: :xlsx),
        class: 'ui right floated green excel button' do
        %i.file.excel.outline.icon
        Export

.action-container
  .ui.wide.container
    .printable.paper{ style: 'overflow: scroll; margin: 2em; min-height: auto; width: auto; padding: 2em; flex: 1 1 auto' }
      %h3.ui.header
        Rent Schedule
        .sub.header
          = @subheader
      %table.single.line.ui.very.basic.small.compact.selectable.table
        %thead
          %tr
            %th Property
            %th Unit
            %th Rent
            %th Tenant
            %th Banner
            %th Tags
            %th Code
            %th Designation
            %th Term
            %th Start Date
            %th End Date
            %th Rent
            %th Rent/Term
            %th SEM I
            %th SEM II
        %tbody
          - last_property = nil
          - last_unit = nil
          - last_lease = nil
          - @rows.each do |row|
            %tr
              %td
                - if last_property != row.property
                  = link_to "#{row.property_name}", property_path(row.property)
              %td= link_to "#{row.unit_name}", unit_path(row.unit)
              %td
                - if last_unit != row.unit || row.lease != last_lease
                  - if row.lease
                    = link_to "#{row.floorplan_price}", leasing_lease_path(row.lease)
                  - else
                    = row.floorplan_price
              %td= row.tenant ? (link_to "#{row.tenant_name}", tenant_path(row.tenant)) : 'Currently Vacant'
              %td= row.banner
              %td= row.tags
              %td= row.code
              %td= row.designation
              %td= number_with_precision(row.term, precision: 1, strip_insignificant_zeros: true)
              %td= row.formatted_start_date
              %td= row.formatted_end_date
              %td= row.rent_price
              %td= row.rent_for_term
              %td= row.sem_1
              %td= row.sem_2
            - last_property = row.property
            - last_unit = row.unit
            - last_lease = row.lease
