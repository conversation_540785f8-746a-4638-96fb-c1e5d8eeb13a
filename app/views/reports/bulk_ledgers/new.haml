.action-bar
  .ui.container
    %h1.ui.header
      Letters

.action-container{ style: 'margin-top: 1em;' }
  .ui.container
    = render partial: 'flash_notices'

    .ui.narrow.container
      = form_with model: @form,
        url: reports_bulk_ledgers_path,
        scope: :bulk_ledgers_form,
        class: 'ui form',
        data: { controller: 'background--task-form' },
        local: false,
        remote: true do |f|

        .disabled.required.field
          = f.label :template
          = f.semantic_dropdown :template,
            [['Ledger Statement', 'ledger_statement']],
            {},
            class: 'ui search selection dropdown'

        .disabled.required.field
          = f.label :delivery_method
          = f.semantic_dropdown :delivery_method,
            [['Print', 'print']],
            {},
            class: 'ui search selection dropdown'

        .field
          = f.label :envelope_format
          = f.semantic_dropdown :envelope_format,
            [['None', nil], ['Double Window - CE15S', 'ce15s']],
            {},
            class: 'ui search selection dropdown',
            data: { options: { placeholder: 'None' } }

        .two.required.fields
          .field
            = f.label :property_id
            = f.semantic_dropdown :property_id,
              current_property_manager.properties.unarchived.pluck(:name, :id),
              {},
              class: 'ui search selection dropdown'

          .field
            = f.label :lease_status
            = f.semantic_dropdown :lease_status,
              Reports::BulkLedgersForm.lease_status_dropdown_options,
              {},
              class: 'ui search selection dropdown'

        .two.required.fields
          .field
            = f.label :activity_start_date
            = f.semantic_date_field :activity_start_date

          .field
            = f.label :activity_end_date
            = f.semantic_date_field :activity_end_date

        .ui.error.message

        .field
          = f.submit 'Submit', class: 'ui primary submit button'
