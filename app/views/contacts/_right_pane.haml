- if contact.is_a?(Owner)
  = render Contact::Panels::OwnerPortalComponent.new(contact)

  = render Contact::Panels::EntityListComponent.new(contact)

  = render Contact::Panels::PropertyListComponent.new(contact)

- if contact.is_a?(Vendor)
  = render Contact::Panels::AddressComponent.new(contact, edit_permission: :update_portfolio_vendors?)

  - require_permission :manage_deposit_accounts_for_portfolio_vendors? do
    = render Contact::Panels::ElectronicDepositAccountComponent.new(contact)

  = render Contact::Panels::TaxpayerIdentificationComponent.new(contact, edit_permission: :update_portfolio_vendors?)

  = render Contact::Panels::InsuranceInformationComponent.new(contact)

- if contact.is_a?(Tenant)
  .panel
    .header
      %i.angle.down.icon
      #{I18n.t('tenant_term')} Portal

  .panel
    .actions
      %a.action
        %i.small.fitted.plus.icon
        Lease
    .header
      %i.angle.down.icon
      Agreements
      %span.count
        1 Active

  .panel
    .actions
      %a.action
        %i.small.fitted.plus.icon
        Ticket
    .header
      %i.angle.down.icon
      Maintenance Tickets
      %span.count
        0 Open

  .panel
    .actions
      %a.action
        %i.small.fitted.calendar.alternate.outline.icon
        Schedule Payment
    .header
      %i.angle.down.icon
      Scheduled Payments
      %span.count
        None

- if contact.respond_to?(:attachments)
  = render Contact::Panels::RecentDocumentsComponent.new(contact)
