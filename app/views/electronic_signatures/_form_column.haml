- leasing_agent = electronic_signature.requested_by || Customer.current.representative

- if electronic_signature.signed?
  .ui.success.message
    .header Completed
    %p
      You have already signed this document. You will receive a copy via
      email when all parties have signed it.
    %p= render partial: 'electronic_signatures/contact_leasing_agent', locals: { leasing_agent: }

- elsif electronic_signature.expired?
  .ui.info.message
    .header Expired
    %p
      This document has expired and can no longer be signed.
    %p= render partial: 'electronic_signatures/contact_leasing_agent', locals: { leasing_agent: }

- else
  :ruby
    url = if electronic_signature.is_a?(ElectronicSignatures::InlineForm)
            electronic_signature.post_path
          end

  = form_with url: url,
    id: 'electronic_signature_form',
    model: electronic_signature,
    scope: :electronic_signature,
    local: false,
    remote: true,
    html: { class: 'ui form' } do |f|
    - if defined?(@success_redirect_key)
      = f.hidden_field :success_redirect_key, value: @success_redirect_key

    - if Customer.current_subdomain.start_with?('mph-sandbox', 'marketplacehomes')
      = render partial: 'electronic_signatures/marketplace_additional',
        locals: { f: f, electronic_signature: electronic_signature }
    - elsif Customer.current_subdomain.start_with?('wrp')
      = render partial: 'electronic_signatures/wrp_additional',
        locals: { f: f, electronic_signature: electronic_signature }
    - elsif Customer.current_subdomain.start_with?('dphie')
      = render partial: 'electronic_signatures/dphie_additional',
        locals: { f: f, electronic_signature: electronic_signature }

    .ui.info.message
      .header Electronic Signature
      %p
        This document may be executed in one or more counterparts, each of
        which shall constitute an original and all of which when taken
        together shall constitute one and the same instrument. An executed
        facsimile or .pdf of this document may be relied upon as having,
        and shall be deemed to have, the same force and effect as an
        original.

      %p= render partial: 'electronic_signatures/contact_leasing_agent', locals: { leasing_agent: }

      - if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]) && electronic_signature.is_a?(ElectronicSignatures::InlineForm)
        .header
          Sublease Release Requests
        %p
          Members may request to be released from all or part of their Sublease
          obligation. Typically, members make these requests to accommodate for
          a study-abroad or internship term. Sublease early termination
          requests are not guaranteed and subject to the Chapter or Emerging
          Chapter adhering to the terms of their Master Lease, including
          minimum capacity requirements. Chapter / Emerging Chapter officers
          and Members are neither permitted nor have the authority to grant
          sublease early release requests; only Greek Housing Management may
          grant sublease early release requests.

        .header
          Acknowledgement
        %p
          %i
            By signing below, you hereby acknowledge that you agree to and
            understand all terms of the Membership Agreement, including
            provisions regarding the live-in requirement and sublease early
            release requests. Furthermore, you agree to and understand that the
            Chapter or Emerging Chapter will be required to take disciplinary
            action, up to and including rescinding your bid or expelling you
            from the Chapter or Emerging Chapter, should you ever fail to
            fulfill the terms outlined in the Membership Agreement.

    - if defined?(@include_damage_waiver) && @include_damage_waiver
      = render partial: 'damage_waiver_banner'

    .ui.very.basic.secondary.segment
      %p
        Please
        = link_to 'download a copy',
          electronic_signature.hosted_document_path,
          target: '_blank',
          download: true
        of the unexecuted document for your records and offline review.

    - if electronic_signature.document.is_a?(Lease)
      - property_name = electronic_signature.document.property.name

      - unless electronic_signature.countersigner
        - if electronic_signature.document.property.lead_paint_disclosure?
          .ui.very.basic.secondary.segment
            %p
              As part of this process you should also review
              = link_to 'this booklet',
                'https://revela-public.s3.amazonaws.com/lead-based-paint-information.pdf',
                target: '_blank',
                download: true
              regarding properties build prior to 1978.

        - if electronic_signature.document.property.lead_paint_disclosure?
          .ui.required.field
            .ui.checkbox
              = f.check_box :agree_lead,
                data: { validate: 'agree_lead' }

              = f.label :agree_lead,
                'I have downloaded and reviewed the booklet.'

      - if Customer.current_subdomain == 'ccp'
        .ui.required.field
          .ui.checkbox
            = f.check_box :agree_rules,
              data: { validate: 'agree_rules' }
            = f.label :agree_rules,
              'I have read and agree to Rules & Regulations.'

        .ui.required.field
          .ui.checkbox
            = f.check_box :agree_waiver,
              data: { validate: 'agree_waiver' }
            = f.label :agree_waiver,
              'I have read and agree to Waiver and Release of Liability.'

        - if electronic_signature.document.unexecuted_lease_document.template_options['template_ids']&.include?('113')
          .ui.required.field
            .ui.checkbox
              = f.check_box :agree_parking,
                data: { validate: 'agree_parking' }
              = f.label :agree_parking,
                "I have read and agree to #{property_name} Parking Lease."

        .ui.required.field
          .ui.checkbox
            = f.check_box :agree_lease,
              data: { validate: 'agree_lease' }
            = f.label :agree_lease,
              "I have read and agree to #{property_name} Residential Lease."

      - if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
        = render partial: 'electronic_signatures/sae_agrees', locals: { f: f,
          electronic_signature: electronic_signature }

    .ui.required.field
      = f.label :full_name
      = f.text_field :full_name,
        placeholder: 'Please type your full name here.'

    .ui.required.field
      = f.label :signature
      .signature
        %canvas{ height: 120, width: 480 }
        %button.ui.basic.mini.compact.icon.button{ type: 'button' }
          %i.eraser.icon
        = f.hidden_field :signature,
          class: 'image',
          id: 'signature',
          value: nil.presence

    .ui.small.very.basic.secondary.segment
      %p
        By clicking &lsquo;Submit Signature&rsquo;, I agree that the
        signature and initials will be the electronic represenatation of my
        signature and initials for all purposes when I (or my agent) use
        them on documents, including legally binding contracts &mdash; just
        the same as a pen-and-paper signature or initial. You also agree to
        our
        = link_to 'Terms of Service',
          ApplicationMailer::TERMS_URL,
          target: '_blank'
        and
        = succeed '.' do
          = link_to 'Privacy Policy',
            ApplicationMailer::PRIVACY_URL,
            target: '_blank'

    .ui.required.field
      .ui.checkbox
        = f.check_box :agree,
          data: { validate: 'agree' }

        = f.label :agree,
          'I agree to use electronic records and signatures.'

    - if defined?(@include_damage_waiver) && @include_damage_waiver
      = render 'damage_waiver_acknowledgement', f: f

    .ui.error.message

    .clearfix
      = f.submit 'Submit Signature',
        class: 'ui blue button',
        data: { disable_with: 'Please Wait...' }
