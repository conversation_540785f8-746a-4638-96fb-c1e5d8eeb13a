- content_for(:content) do
  .action-bar
    .ui.container
      %h1.ui.header
        Manage Tags

      .action.buttons
        = link_to '#', class: 'ui button', data: { modal: :add_tag_modal } do
          %i.plus.icon
          Add Tag

        = export_button

  .action-container
    .ui.container{ style: 'padding-top: 1rem;' }
      = render partial: 'application/flash_notices'

      .ui.orange.pointing.secondary.menu
        - tabs.each do |tab|
          - count = TagsQuery.new.search.for_type(tab.taggable_type).count
          - active = @tab.filter == tab.filter

          = link_to settings_tags_path(tab),
            class: class_names('item', active: active) do
            = tab.title
            .ui.mini.orange.label{ class: class_names(basic: !active) }
              = count

      = yield

= render template: 'layouts/application'

= render partial: 'settings/tags/add_tag_modal'

= render partial: 'settings/tags/edit_tag_modal'
