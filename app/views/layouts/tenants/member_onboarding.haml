- content_for(:content) do
  %main.ui.fluid.container{ style: 'padding: 1rem; flex: 1 0 auto;' }
    = link_to destroy_tenant_session_path, class: 'ui right floated button', method: :delete do
      %i.sign.out.icon
      Logout
    - unless onboarding_wizard.submitted?
      = link_to clear_tenants_member_onboarding_index_path, class: 'ui right floated button', method: :delete do
        %i.trash.icon
        Clear Onboarding Session

    %h1.ui.header{ style: 'margin-top: 0;' }
      Member Onboarding

    .ui.divider

    .ui.stackable.very.padded.grid
      %br/
      .four.wide.column
        .ui.vertical.orange.secondary.pointing.menu
          - if try(:onboarding_wizard)
            - onboarding_wizard.steps.values.each do |step|
              %a.item{ class: class_names(active: step.active?, disabled: !step.visitable?),
                href: step.show_path }
                - if step.completed?
                  %i.green.check.mark.icon
                = step.name

      .column{ class: class_names('ten wide' => !action_name.include?('agreement'), 'twelve wide' => action_name.include?('agreement')) }
        .ui.container
          = render "application/flash_notices"
        = yield

  %footer
    .ui.divider{ style: 'margin: 0;' }

    .ui.container{ style: 'padding: 1rem;' }
      .clearfix
        - if onboarding_wizard.previous_step&.visitable?
          = link_to onboarding_wizard.previous_step.show_path, class: 'ui basic button' do
            Back

        - if onboarding_wizard.current_step&.form_id
          %button.right.floated.ui.primary.button{form: onboarding_wizard.current_step.form_id,
            type: :submit, data: { disable_with: 'Next...' }}
            Next
        - elsif onboarding_wizard.current_step&.id == :completion_summary
          = link_to 'Done', onboarding_wizard.current_step.after_completion_path,
            class: 'right floated ui primary button'
        - elsif onboarding_wizard.next_step&.visitable?
          = link_to 'Next', onboarding_wizard.next_step.show_path, class: 'right floated ui primary button'

    = render partial: 'layouts/shared/footer'

= render template: 'layouts/simple'
