- content_for(:content) do
  .action-bar
    .ui.container
      %h1.ui.header
        Agreements

      .action.buttons
        - require_permission :export_operations_agreements? do
          = export_button
        - require_permission :create_operations_agreements? do
          - unless current_property_manager.role.name.in?(['House Director', 'Chapter Advisor / Officer', 'House Corporation Board Member', 'City of Detroit']) && CustomerSpecific::Behavior.disable_based_on_role_name?
            - if @agreement_type
              = link_to "New #{@agreement_type.name.singularize}",
                new_leasing_agreement_path(params[:type]),
                class: 'ui button'
            - elsif params[:type] == 'loans'
              = link_to 'New Loan',
                new_lending_loan_path,
                class: 'ui button'
            - else
              = link_to "New #{I18n.t('lease_term')}",
                new_leasing_lease_path,
                class: 'ui button'

  .action-container
    .ui.container{ style: 'padding-top: 1rem;' }
      = render partial: 'application/flash_notices'

      .ui.orange.pointing.secondary.menu
        - if display_leases?
          = link_to leasing_agreements_path(type: 'leases'),
            class: class_names({ active: params[:type] == 'leases' }, 'item') do
            = I18n.t('lease_term').pluralize

        - if display_loans?
          = link_to leasing_agreements_path(type: 'loans'),
            class: class_names({ active: params[:type] == 'loans' }, 'item') do
            Loans

        - Agreements::AgreementType.all.each do |type|
          = link_to "/leasing/agreements/#{type.slug}",
            class: class_names({ active: params[:type] == type.slug }, 'item') do
            = type.name.pluralize

        - require_permission :manage_types_for_operations_agreements? do
          = link_to leasing_agreement_types_path, class: 'right item' do
            Manage

      .ui.active.tab
        = yield

= render template: 'layouts/application'
