- content_for(:content) do
  .action-bar
    .ui.container
      %h1.ui.header
        Collections

      .action.buttons
        - if controller_name.in?(%w[late active_notice expired_notice evicting])
          = export_button

  .action-container{ style: 'overflow-y: scroll;' }
    .ui.container{ style: 'margin-top: 1em;' }
      = render 'flash_notices'

      .ui.orange.pointing.secondary.menu
        :ruby
          path = operations_collections_late_index_path
          active = current_page?(path)
          count = CollectionsQuery.new.search.late.length
        = link_to path,
          class: class_names('item', active: active) do
          Late
          .ui.mini.orange.label{ class: class_names(basic: !active) }
            = count

        :ruby
          path = operations_collections_active_notice_index_path
          active = current_page?(path)
          count = CollectionsQuery.new.search.active_notice.length
        = link_to path,
          class: class_names('item', active: current_page?(path)) do
          Notice Sent
          .ui.mini.orange.label{ class: class_names(basic: !active) }
            = count

        :ruby
          path = operations_collections_expired_notice_index_path
          active = current_page?(path)
          count = CollectionsQuery.new.search.expired_notice.length
        = link_to path,
          class: class_names('item', active: current_page?(path)) do
          Notice Expired
          .ui.mini.orange.label{ class: class_names(basic: !active) }
            = count

        :ruby
          path = operations_collections_evicting_index_path
          active = current_page?(path)
          count = CollectionsQuery.new.search.evicting.length
        = link_to path,
          class: class_names('item', active: current_page?(path)) do
          Evicting
          .ui.mini.orange.label{ class: class_names(basic: !active) }
            = count

        .right.menu
          - path = operations_collections_history_index_path
          - active = current_page?(path)
          = link_to path,
            class: class_names('item', active: current_page?(path)) do
            %i.history.icon
            History

      .ui.active.tab{ 'data-tab': 'late' }
        = yield

= render template: 'layouts/application'
