class MemberOnboardingsMailer < ApplicationMailer
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def notify_assignment(assignment:)
    # For brand
    @portfolio = assignment.configuration.portfolio

    customer = Customer.current
    tenant = assignment.tenant

    @tenant_first_name = tenant.first_name
    @onboarding_name = assignment.configuration.name
    @organization_name = customer.name
    @dashboard_url = tenants_dashboard_url(subdomain: customer.subdomain)

    mail to: email_address_with_name(tenant.email, tenant.name),
         subject: "Onboarding Invitation: #{@onboarding_name}"
  end

  def remind_assignment(assignment:)
    # For brand
    @portfolio = assignment.configuration.portfolio

    customer = Customer.current
    tenant = assignment.tenant

    @tenant_first_name = tenant.first_name
    onboarding_name = assignment.configuration.name
    @dashboard_url = tenants_dashboard_url(subdomain: customer.subdomain)

    mail to: email_address_with_name(tenant.email, tenant.name),
         subject: "Onboarding Reminder: Complete #{onboarding_name}"
  end
end
