require 'color'

module ApplicationHelper
  include Serialization

  def menu_link(name = nil, link = nil, html_options = nil, &block)
    classes = ['item']
    classes << 'active' if active?(name)

    if block
      link_to(link, { class: classes }, &block)
    else
      link_to(name, link, { class: classes }, &block)
    end
  end

  def render_namespace_template
    if tenant_namespace?
      render 'tenant'
    else
      render 'management'
    end
  end

  def weekday_time(time)
    time.strftime('%A, %B %e, %Y at %l:%M %p')
  end

  def day_time(time)
    time.strftime('%B %e, %Y at %l:%M %p')
  end

  def weekday(time)
    time.strftime('%A, %B %e, %Y')
  end

  def day(time)
    time.strftime("%B #{time.day.ordinalize}, %Y")
  end

  def percentage(float)
    (float * 100.0).to_i.to_s + '%'
  end

  def color_label(color_name, content)
    color = Color::CSS[color_name]

    style = if color
              if color.brightness > 0.5
                "background-color: #{color.html}; color: #000;"
              else
                "background-color: #{color.html}; color: #FFF;"
              end
            end

    content_tag(:div, class: 'ui mini label', style: style) { content }
  end

  def allow_basic_tags(text)
    sanitize(text, tags: %w[br a], attributes: %w[href])
  end

  def unread_notifications?
    NotificationsQuery.for_user(current_property_manager).visible.any?
  end

  def new_whats_new_entries?
    latest_published = WhatsNewEntry.published.maximum(:published_at)

    return false unless latest_published

    last_visit = current_property_manager.last_whats_new_visit

    return true unless last_visit

    latest_published > last_visit
  end

  def last_n(string, number)
    string&.to_s&.gsub(/.(?=.{#{number}})/, '*')
  end

  def subdomain_logo
    Customer.current.logo_url
  end

  # Renders a delete button with an icon and a confirmation message.
  # @param link [String] the path to DELETE to
  def delete_link(path)
    options = {
      method: :delete,
      data: { confirm: 'Are you sure?' },
      class: 'right floated ui button'
    }

    link_to path, options do
      content_tag('i', nil, class: 'remove icon') + 'Delete'
    end
  end

  def account_select_options(accounts)
    [['None', '']] + accounts.map do |account|
      [account.display_name, account.id]
    end
  end

  def options_for_enum(enum)
    enum.keys.map { |k| [k.titleize, k] }
  end

  def print_button(class_name: 'right floated ui button')
    content_tag(:a, class: class_name, onclick: 'window.print();') do
      content_tag(:i, nil, class: 'print icon') + 'Print'
    end
  end

  def pdf_button(class_name: 'ui button', id: nil, extra_permit: [])
    link_to filtered_url_as_format(:pdf, extra_permit: extra_permit),
            class: class_name,
            id: id,
            target: '_blank',
            rel: 'noopener' do
      content_tag(:i, nil, class: 'pdf file outline icon') + 'Download'
    end
  end

  def export_button(class_name: 'ui button', id: nil, extra_permit: [])
    link_to filtered_url_as_format(:xlsx, extra_permit: extra_permit),
            download: true,
            class: class_name,
            id: id do
      content_tag(:i, nil, class: 'excel file outline icon') + 'Export'
    end
  end

  def background_download_button(link_options = {}, url_options = {}, &)
    link_to(
      filtered_url_as_format(:js, **url_options),
      class: 'ui button',
      data: { background_download: true },
      **link_options,
      &
    )
  end

  def background_pdf_button(link_options = {}, url_options = {})
    background_download_button(
      link_options,
      { download_format: :pdf, **url_options }
    ) do
      content_tag(:i, nil, class: 'pdf file outline icon') + 'Download'
    end
  end

  def background_xlsx_button(link_options = {}, url_options = {})
    background_download_button(
      link_options,
      { download_format: :xlsx, **url_options }
    ) do
      content_tag(:i, nil, class: 'excel file outline icon') + 'Export'
    end
  end

  def meta_title(title)
    content_for(:title, title)
    title
  end

  def meta_description(description)
    content_for(:meta_description, description)
    description
  end

  def mail_link(email)
    return nil if email.blank?

    mail_to email, email, target: '_blank'
  end

  def phone_link(number)
    return nil if number.blank?

    link_to number.phony_formatted(normalize: :US),
            "tel:#{number.phony_normalized}"
  end

  def format_seconds(seconds)
    Time.at(seconds).utc.strftime('%H:%M')
  end

  def filtered_url_as_format(format, extra_permit: [], **extra)
    url_for(
      format: format,
      params: params.permit(
        *extra_permit,
        filters: {},
        sort: {}
      ),
      **extra
    ).gsub(
      %r{[a-z0-9\-/_]+(?=\.)}, request.path
    )
  end

  # Displays a loading paper that gets replaced by pages rendered from +url+ by
  # the InlinePdf stimulus controller.
  def inline_pdf(url)
    tag.div(data: { controller: 'inline-pdf', url: url }) do
      tag.div(class: 'pdf paper') do
        tag.div(class: 'ui placeholder') do
          tag.div(class: 'image header') do
            tag.div(class: 'line') + tag.div(class: 'line')
          end
        end
      end
    end
  end

  def leasing_leads_path
    if !Rails.env.test? && Feature.enabled?(:leasing_pipeline_v2, Customer.current)
      leasing_pipeline_leads_path
    else
      super
    end
  end

  def deferred_render(...)
    render DeferredRenderComponent.new(...)
  end

  private

  def tenant_namespace?
    params[:controller].split('/').first == 'tenants'
  end

  def active?(name)
    params[:controller].include? name.downcase
  end
end
