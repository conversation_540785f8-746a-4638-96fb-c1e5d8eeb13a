class InvoicesQuery
  def initialize(relation = Invoice.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    include Queries::MaxPagination

    def by_user(user, side: :both)
      return apply_side_scope(side) if user.top_level?

      companies_and_properties = [user.companies, user.properties].flatten

      case side
      when :both
        where(seller: companies_and_properties)
          .or(where(buyer: companies_and_properties))
      when :payables
        where(buyer: companies_and_properties)
      when :receivables
        where(seller: companies_and_properties)
      else
        fail 'Invalid accounting side'
      end
    end

    def payables_by_user(user)
      by_user(user, side: :payables)
    end

    def receivables_by_user(user)
      by_user(user, side: :receivables)
    end

    def by_owner(owner)
      where(buyer: owner.companies + owner.properties)
    end

    def by_tenant(tenant)
      where(buyer: tenant)
    end

    def by_lease_membership(membership)
      joins(:journal_entries)
        .where(plutus_entries: { lease_membership_id: membership.id })
    end

    def by_lease(lease)
      ids = lease.lease_membership_ids

      joins(:journal_entries)
        .where(plutus_entries: { lease_membership_id: ids })
    end

    def payable
      where(buyer_type: %w[Property Company])
    end

    def receivable
      where(seller_type: %w[Property Company])
    end

    def batched
      where.not(payment_batch_id: nil)
    end

    def unbatched
      where(payment_batch_id: nil)
    end

    def filter_with(filters)
      return self if filters.blank?

      filter_text(filters[:search])
        .filter_status(filters[:status])
        .filter_approval_status(filters[:approval_status])
        .filter_invoices_batch(filters[:invoices_batch])
        .filter_buyer(filters[:buyer_sgid])
        .filter_seller(filters[:seller_sgid])
        .filter_start_date(filters[:start_date])
        .filter_end_date(filters[:end_date])
        .filter_portfolio_id(filters[:portfolio_id])
    end

    def filter_status(status)
      case status
      when 'paid' then paid
      when 'unpaid' then unpaid
      when 'due' then due
      when 'overdue' then overdue
      when 'waived' then waived
      when 'open' then open
      when 'closed' then closed
      else self
      end
    end

    def filter_approval_status(status)
      case status
      when 'approved' then approved
      when 'unapproved' then unapproved
      when /needs_approval_by_(\d+)/
        user = PropertyManager.find(Regexp.last_match(1))
        where(id: Approvals::Query.invoice_payment_needs_approval_by(user))
      when /approved_by_(\d+)/
        user = PropertyManager.find(Regexp.last_match(1))
        where(id: Approvals::Query.invoice_payment_previously_approved_by(user))
      else self
      end
    end

    def filter_invoices_batch(batch_id)
      return self if batch_id.blank?

      case batch_id
      when 'none' then unbatched
      when 'any' then batched
      else where(payment_batch_id: batch_id)
      end
    end

    def filter_buyer(buyer)
      return self if buyer.blank?

      model = GlobalID::Locator.locate_signed(buyer)
      if model
        if model.is_a?(Company)
          where(buyer: model).or(where(buyer: model.properties))
        else
          where(buyer: model)
        end
      else
        type, id = buyer.split('-')
        where('invoices.buyer_type = ? AND invoices.buyer_id = ?', type, id)
      end
    end

    def filter_seller(seller)
      return self if seller.blank?

      model = GlobalID::Locator.locate_signed(seller)
      if model
        where(seller: model)
      else
        type, id = seller.split('-')
        where('invoices.seller_type = ? AND invoices.seller_id = ?', type, id)
      end
    end

    def filter_start_date(date)
      return self if date.blank?

      where(physical_date: date..)
    end

    def filter_end_date(date)
      return self if date.blank?

      where(physical_date: ..date)
    end

    def filter_portfolio_id(portfolio_id)
      return self if portfolio_id.blank?

      portfolio = Portfolio.find(portfolio_id)

      where(buyer: portfolio.companies).or(
        where(buyer: portfolio.properties)
      ).or(
        where(seller: portfolio.companies)
      ).or(
        where(seller: portfolio.properties)
      )
    end

    def filter_text(text)
      return self if text.blank?

      query = joins(:line_items)

      query.where('invoices.invoice_number ILIKE ?', "%#{text}%").or(
        query.merge(
          LineItem.where('line_items.description ILIKE ?', "%#{text}%")
        )
      ).or(
        query.where('invoices.description ILIKE ?', "%#{text}%")
      ).or(
        query.where('invoices.note ILIKE ?', "%#{text}%")
      )
    end

    def order_with(params)
      return self if params.blank? || params[:column].blank?

      direction = params[:direction] == 'descending' ? 'DESC' : 'ASC'

      case params[:column]
      when 'physical_date', 'invoice_number', 'description', 'amount_cents'
        reorder(params[:column] => direction)
      # TODO: This breaks the paid scope
      # TODO: Finish sorting by balance
      # when 'balance_cents'
      #   reorder(
      #     Arel.sql(
      #       "
      #         (
      #           CASE
      #             WHEN invoices.waived_at IS NOT NULL THEN 0
      #             ELSE invoices.amount_cents - invoice_total_payments.payment
      #           END
      #         ) #{direction}
      #       "
      #      )
      #    )
      else
        self
      end
    end

    def due
      open.where(due_date: Time.zone.today..)
    end

    def overdue
      open.where(due_date: ...Time.zone.today)
    end

    def open(date = nil)
      return unpaid.where(waived_at: nil) unless date

      unpaid(date)
        .where('invoices.post_date <= ?', date) # TODO: move to unpaid scope
        .where(
          'invoices.waived_at > ? OR invoices.waived_at IS NULL',
          date.end_of_day
        )
    end

    def closed(date = nil)
      pids = InvoicesQuery.new.search.paid(date).pluck(:id)
      wids = InvoicesQuery.new.search.waived(date).pluck(:id)
      where(id: pids + wids)
    end

    def waived(date = nil)
      if date
        where('invoices.waived_at <= ?', date)
      else
        where.not(waived_at: nil)
      end
    end

    def paid(date = nil)
      with_relevant_invoice_payments(date)
        .group(:id)
        .having(%(
          sum(invoice_payments.amount_cents)
          >=
          min(invoices.amount_cents)
        )) # TODO: avoid useless min
    end

    def unpaid(date = nil)
      with_relevant_invoice_payments(date)
        .group(:id)
        .having(%(
          (sum(invoice_payments.amount_cents)
          <
          min(invoices.amount_cents)
          OR
          count(invoice_payments) = 0)
        )) # TODO: avoid useless min
    end

    def approved
      where.not(invoice_payment_approved_at: nil)
    end

    def unapproved
      where(invoice_payment_approved_at: nil)
    end

    def appliable_for(payment)
      ledger = payment.ledger

      # TODO: Possibly make this use properly payable_invoices or
      # receivable_invoices.
      subquery = if payment.payer.is_a?(Property) && payment.payee.is_a?(Tenant)
                   # Refund payables
                   where(seller: payment.payee, buyer: payment.payer).open
                 else
                   case ledger
                   when Accounting::Ledger::Tenant,
                     Accounting::Ledger::LeaseChain,
                     Accounting::Ledger::SimpleAgreement
                     merge(ledger.payable_invoices).open
                   else
                     where(seller: payment.payee, buyer: payment.payer).open
                   end
                 end

      where(id: subquery.select(:id))
    end

    def with_priority
      line_items_with_priority = LineItemsQuery.new.search.with_priority

      highest_priority = \
        LineItem
        .from(line_items_with_priority, :line_items)
        .select('invoice_id, MAX(priority) AS priority')
        .group(:invoice_id)

      joins("
        JOIN LATERAL (#{highest_priority.to_sql}) subquery
        ON subquery.invoice_id = invoices.id
      ").select('invoices.*').select('subquery.priority')
    end

    def with_one_line_item
      joins(:line_items)
        .group('invoices.id')
        .having('COUNT(line_items.id) = 1')
    end

    def apply_order
      with_priority.order(
        post_date: :asc,
        priority: :desc,
        created_at: :asc
      )
    end

    def without_payment_plan(date = Time.zone.today)
      active_or_upcoming_plans =
        PaymentPlansQuery.new.search.with_current_or_upcoming_installments(date)

      active_or_upcoming_memberships =
        PaymentPlan::InvoiceMembership
        .joins(:payment_plan)
        .merge(active_or_upcoming_plans)

      where.not(id: active_or_upcoming_memberships.select(:invoice_id))
    end

    private

    def apply_side_scope(side)
      case side
      when :both
        self
      when :payables
        payable
      when :receivables
        receivable
      else
        fail 'Invalid accounting side'
      end
    end

    def with_relevant_invoice_payments(date)
      if date
        quoted_date = ActiveRecord::Base.connection.quote(date)
        quoted_time = ActiveRecord::Base.connection.quote(date.end_of_day)

        joins(%(
          LEFT OUTER JOIN (
            SELECT
              invoice_payments.*
            FROM
              invoice_payments
            JOIN
              payments
            ON (invoice_payments.payment_id = payments.id)
            AND payments.date <= #{quoted_date}
            AND (
              payments.reversed_at IS NULL
              OR
              payments.reversed_at > #{quoted_time}
            )
          ) invoice_payments
          ON (
            invoice_payments.invoice_id = invoices.id
            AND (
              invoice_payments.reversed_at IS NULL
              OR
              invoice_payments.reversed_at > #{quoted_date}
            )
          )
        ))
      else
        joins(%(
          LEFT OUTER JOIN invoice_payments
          ON (invoice_payments.invoice_id = invoices.id
          AND invoice_payments.reversed_at IS NULL)
        ))
      end
    end
  end

  class PaidScope
    def self.call(date = nil)
      InvoicesQuery.new.search.paid(date)
    end
  end

  class UnpaidScope
    def self.call(date = nil)
      InvoicesQuery.new.search.unpaid(date)
    end
  end

  class OpenScope
    def self.call(date = nil)
      InvoicesQuery.new.search.open(date)
    end
  end

  class ApprovedScope
    def self.call
      InvoicesQuery.new.search.approved
    end
  end
end
