class Taxes::Candidate1099Q<PERSON>y
  def initialize(relation = Taxes::Candidate1099.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    include Queries::MaxPagination

    ALLOWED_ISSUES = Taxes::Candidate1099::ALLOWED_ISSUES.keys.freeze
    ISSUE_FILTERS = Taxes::Candidate1099::ISSUE_FILTERS.keys.freeze

    def for_modal
      includes(
        :irs_filing,
        payer: %i[representative address taxpayer_identification],
        payee: %i[primary_contact address taxpayer_identification]
      )
    end

    def for_table
      includes(:payee, :payer)
    end

    def find(id)
      where(id: id).first!
    end

    def with_statuses?(*statuses)
      where.not(status: statuses).none?
    end

    def with_matching_years?
      true unless first
      where.not(year: first.year).none?
    end

    def filter_issues(*issues)
      return self unless issues.any?

      issue_scopes = Array.wrap(issues).compact.map(&:to_sym)
      sanitized_scopes = issue_scopes & ISSUE_FILTERS

      sanitized_scopes
        .each_with_index
        .reduce(nil) do |memo, (issue_scope, idx)|
        if idx.positive?
          memo.or(public_send(issue_scope))
        else
          public_send(issue_scope)
        end
      end
    end

    def filter_values(value_filters = {})
      where value_filters.compact_blank
    end

    def filter_search(query)
      return self if query.blank?

      s = "%#{query}%"

      where('payer_name ILIKE ?', s).or(where('payee_name ILIKE ?', s))
    end

    def no_issues
      ALLOWED_ISSUES
        .each_with_index
        .reduce(nil) do |memo, (issue_scope, idx)|
        if idx.positive?
          memo.where.not(id: public_send(issue_scope))
        else
          where.not(id: public_send(issue_scope))
        end
      end
    end

    def missing_tin
      where(taxpayer_identification_id: nil)
    end

    def missing_address
      where(address_id: nil)
    end

    def missing_contact
      where(contact_id: nil)
    end

    def under600
      where('payments_total_cents + adjustment_cents < 60000')
    end
  end
end
