class VendorsQuery
  def initialize(relation = Vendor.unarchived)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  def self.for_user(user)
    new.search.by_user(user)
  end

  module Scopes
    include TagFiltering

    def by_user(user)
      self
    end

    def by_company(company)
      company.vendors
    end

    def with_email
      joins(:vendor_contacts)
        .merge(VendorContact.where.not(email: [nil, '']))
        .distinct
    end

    def filter_email(email)
      return self unless email

      joins(:vendor_contacts)
        .where('vendor_contacts.email ILIKE ?', "%#{email}%")
    end

    def filter_name(name)
      return self unless name

      where('name ILIKE ?', "%#{name}%")
    end

    def filter_kind(kind)
      return self if kind.blank?

      where(kind: kind)
    end

    def filter_text(text)
      return self if text.blank?

      query = left_joins(:vendor_contacts)

      ids = query.where('vendors.name ILIKE ?', "%#{text}%").or(
        query.where("vendor_contacts.first_name || ' ' || vendor_contacts.last_name <PERSON>I<PERSON> ?", "%#{text}%")
      ).or(
        query.where('vendor_contacts.email ILIKE ?', "%#{text}%")
      ).or(
        query.where('vendor_contacts.phone ILIKE ?', "%#{text}%")
      ).pluck(:id)

      where(id: ids) # Avoids having to use distinct
    end

    def order_with(params, default_params: { column: 'name' })
      sort = params || default_params

      return self if sort.blank? || sort[:column].blank?

      direction = sort[:direction] == 'descending' ? 'DESC' : 'ASC'

      case sort[:column]
      when 'name'
        order(sort[:column] => direction)
      when 'kind'
        ints = Vendor.kinds.sort.map(&:second)
        ints.reverse! if direction == 'DESC'
        order(Arel.sql(ints.map { |i| "vendors.kind != #{i}" }.join(', ')))
      else
        self
      end
    end
  end
end
