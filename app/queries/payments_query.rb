class PaymentsQuery
  def initialize(relation = Payment.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  def self.for_user(user)
    new.search.by_user(user)
  end

  def self.for_tenant(tenant)
    new.search.by_tenant(tenant)
  end

  module Scopes
    def by_user(user)
      return self if user.top_level?

      query = where(payer: [user.companies, user.properties].flatten)
              .or(where(payee: [user.companies, user.properties].flatten))

      if user.role.name == 'Chapter Advisor / Officer'
        query = query.where(payer_type: 'Tenant').or(query.where(payee_type: 'Tenant'))
      end

      query
    end

    def by_tenant(tenant)
      where(payer: tenant)
    end

    def by_property(property)
      joins(:journal_entries).merge(Plutus::Entry.where(property: property))
    end

    # Filters the relation by the polymorphic {Payment#payer} attribute.
    # @param payer For example, "Tenant-9"
    def filter_payer(payer)
      return self unless payer

      type, id = payer.split('-')

      if type == 'Company'
        payers = [Company.find(id), Property.where(company_id: id)].flatten
        where(payer: payers)
      else
        where(payer_type: type, payer_id: id)
      end
    end

    # Filters the relation by the polymorphic {Payment#payee} attribute.
    # @param payee For example, "Vendor-9"
    def filter_payee(payee)
      return self unless payee

      type, id = payee.split('-')

      if type == 'Company'
        payees = [Company.find(id), Property.where(company_id: id)].flatten
        where(payee: payees)
      else
        where(payee_type: type, payee_id: id)
      end
    end

    def filter_search(search)
      return self if search.blank?

      where('payments.description ILIKE ?', "%#{search}%")
        .or(where('payments.check_number ILIKE ?', "%#{search}%"))
    end

    def filter_payer_sgid(sgid)
      return self if sgid.blank?

      payer = GlobalID::Locator.locate_signed(sgid)

      if payer.is_a?(Company)
        where(payer: [payer, payer.properties].flatten)
      else
        where(payer: payer)
      end
    end

    def filter_payee_sgid(sgid)
      return self if sgid.blank?

      payee = GlobalID::Locator.locate_signed(sgid)

      if payee.is_a?(Company)
        where(payee: [payee, payee.properties].flatten)
      else
        where(payee: payee)
      end
    end

    def filter_kind(kind)
      return self if kind.blank?

      where(kind: kind)
    end

    def filter_status(status)
      return self unless status

      where(status: Payment.statuses[status])
    end

    def filter_after(date)
      return self unless date

      where('payments.date >= ?', date)
    end

    def filter_before(date)
      return self unless date

      where('payments.date <= ?', date)
    end

    def filter_portfolio_id(portfolio_id)
      return self if portfolio_id.blank?

      portfolio = Portfolio.find(portfolio_id)

      where(payer: portfolio.companies).or(
        where(payer: portfolio.properties)
      ).or(
        where(payee: portfolio.companies)
      ).or(
        where(payee: portfolio.properties)
      )
    end

    def payable
      where(payer_type: %w[Property Company])
    end

    def receivable
      where(payee_type: %w[Property Company])
    end

    def with_credit(date = nil)
      query = self

      # Filter reversed payments
      query = if date
                query.where(
                  'payments.date <= ?', date
                ).where(
                  'payments.reversed_at IS NULL OR payments.reversed_at > ?',
                  date.end_of_day
                )
              else
                query.where(reversed_at: nil)
              end

      # Join invoice payments
      query = if date
                quoted_date = ActiveRecord::Base.connection.quote(date)

                query.joins("
                  LEFT OUTER JOIN (
                    SELECT
                      invoice_payments.*
                    FROM
                      invoice_payments
                    WHERE (
                      invoice_payments.reversed_at IS NULL OR
                      invoice_payments.reversed_at > #{quoted_date}
                    )
                    AND invoice_payments.date <= #{quoted_date}
                  ) invoice_payments
                  ON invoice_payments.payment_id = payments.id
                ")
              else
                query.joins('
                  LEFT OUTER JOIN
                    invoice_payments
                  ON (
                    invoice_payments.payment_id = payments.id
                    AND
                    invoice_payments.reversed_at IS NULL
                  )
                ')
              end

      # Filter applied payments
      query
        .group(:id)
        .having(%(
          (sum(invoice_payments.amount_cents)
          <
          payments.amount_cents
          OR
          count(invoice_payments) = 0)
        ))
    end

    def filter_with(params)
      return self if params.blank?

      filter_search(params[:search])
        .filter_kind(params[:kind])
        .filter_payer_sgid(params[:payer_sgid])
        .filter_payee_sgid(params[:payee_sgid])
        .filter_after(params[:start_date])
        .filter_before(params[:end_date])
        .filter_status(params[:status])
        .filter_portfolio_id(params[:portfolio_id])
    end

    def order_with(params)
      sort = params || { column: 'date', direction: 'descending' }

      return self if sort.blank? || sort[:column].blank?

      direction = sort[:direction] == 'descending' ? 'DESC' : 'ASC'

      case sort[:column]
      when 'date', 'description', 'kind', 'status'
        reorder(sort[:column] => direction)
      when 'amount'
        reorder(amount_cents: direction)
      else
        self
      end
    end
  end
end
