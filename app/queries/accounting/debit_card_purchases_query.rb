class Accounting::DebitCardPurchasesQuery
  def initialize(relation = Accounting::DebitCardPurchase.all, archived: false)
    @relation = relation
                .includes(:vendor)
                .extending(Scopes)
                .not_processed

    @relation = @relation.unarchived unless archived
  end

  def search
    @relation
  end

  def self.for_bank_account(bank_account, archived: false)
    new(archived:).search.by_bank_account(bank_account)
  end

  module Scopes
    include Queries::MaxPagination

    def by_bank_account(bank_account)
      where(bank_account_id: bank_account)
    end

    def filter_with(filters)
      return self if filters.blank?

      filter_search(filters[:search])
        .filter_start_date(filters[:start_date])
        .filter_end_date(filters[:end_date])
        .filter_status(filters[:status])
        .filter_paid_by(filters[:paid_by])
    end

    def filter_search(search)
      return self if search.blank?

      where('merchant_name ILIKE :search OR description ILIKE :search OR paid_by ILIKE :search',
            search: "%#{search}%")
        .or(filter_vendor_name(search))
    end

    def filter_start_date(date)
      return self if date.blank?

      where(transaction_at: Time.zone.parse(date).beginning_of_day..)
    end

    def filter_end_date(date)
      return self if date.blank?

      where(transaction_at: ..Time.zone.parse(date).end_of_day)
    end

    def filter_status(status)
      return self if status.blank?

      return filter_archived if status == 'archived'

      where(status: status)
    end

    def filter_vendor_name(vendor_name)
      return self if vendor_name.blank?

      where(vendor: VendorsQuery.new.search.filter_name(vendor_name))
    end

    def filter_paid_by(paid_by)
      return self if paid_by.blank?

      where('LOWER(paid_by) = ?', paid_by.downcase)
    end

    def filter_archived
      where(archived_at: 90.days.ago..)
    end
  end
end
