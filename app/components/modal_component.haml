- content_for :loader_content do
  .ui.centered.active.inline.loader.component

.ui.modal{ class: size, id: id, data: data }
  %i.black.close.icon
  .header
    = title

  .content{ class: class_names(scrolling: scrolling?) }
    - if content_loaded?
      = content
    - else
      = content_for :loader_content

  - unless cancel_hidden? && submit_hidden?
    .actions
      - unless cancel_hidden?
        .ui.basic.cancel.button
          = cancel_text

      - unless submit_hidden?
        - if form_id
          %button.ui.primary.submit.button{ type: 'submit',
          disabled: submit_disabled?,
          form: form_id,
          data: { disable: true } }
            - if submit_icon
              %i.icon{ class: submit_icon }
            = submit_text
        - elsif submit_button?
          = submit_button
        - else
          .ui.primary.submit.button{ class: class_names(disabled: submit_disabled?) }
            - if submit_icon
              %i.icon{ class: submit_icon }
            = submit_text

:javascript
  var setupModalClickListeners = function() {
    $('a[data-modal="#{id}"]').click(function (event) {
      event.preventDefault();

      setTimeout(function () {
        $(event.target).removeClass('active selected');
      }, 0);

      var options = Object.assign({}, window.modalDefaults);

      options.autofocus = #{autofocus};
      options.detachable = #{detachable};

      if (#{is_a?(Modal::BulkActionComponent)}) {
        options.onShow = function () {
          $(this)
            .find('.content')
            .html("#{j content_for(:loader_content)}");

          $(this)
            .find('.actions .submit')
            .attr('disabled', true)
            .addClass('disabled');

          var selection = $('div[data-controller*="multi-selection-index"]')[0];
          var index = selection['multi-selection-index'];
          var ids = index.selectedRowIds();
          var overselection = index.isOverselection();
          var search = window.location.search;
          var query = qs.parse(search, { ignoreQueryPrefix: true }) || {};

          query['selection'] = {
            overselection: overselection,
            ids: ids,
          };


            let url;
            if ("#{@options[:modal_path]}".length > 0) {
              url = "#{@options[:modal_path]}?" + qs.stringify(query);
            } else {
              url = window.location.pathname + "/#{@options[:controller_action]}.js?" + qs.stringify(query);
            }

          $.get(url);
        }
      }

      if (#{loads_path}) {
        $('##{id} .content').html('<div class="ui very basic vertical segment"><div class="ui active centered loader"></div></div>');

        $('##{id}').modal(options).modal('show');

        $.get(event.target.href, html => {
          $('##{id} .content').html(html);
        });
      } else {
        $('##{id}').modal(options).modal('show');
      }
    });
  };

  setTimeout(setupModalClickListeners, 0);

  $('##{id}')[0].setupModalClickListeners = setupModalClickListeners;
