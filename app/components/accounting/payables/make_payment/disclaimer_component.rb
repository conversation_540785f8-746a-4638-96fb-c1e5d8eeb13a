class Accounting::Payables::MakePayment::DisclaimerComponent < ApplicationComponent
  include Accounting::Payables::MakePayment::Shared

  def initialize(payment:)
    super()

    @payment = payment
  end

  def render?
    return false unless moving_funds?

    return false if withdrawal_bank_account.blank?

    return false if payee.blank?

    return false if ach? && deposit_bank_account.blank? # Shouldn't be possible from the form.

    true
  end

  private

  def moving_funds? = amount.positive? && (ach? || mailing_check?)

  def payment_terms_path = helpers.terms_path

  def from_description
    ach_description(withdrawal_bank_account)
  end

  def to_description
    if ach?
      ach_description(deposit_bank_account)
    elsif mailing_check?
      payee.name
    else
      fail
    end
  end

  def ach_description(account)
    "#{account.owner.name}'s #{acount_description(account)}"
  end

  def account_description(account)
    [
      account.account_type.titleize.downcase,
      'account ending in',
      account.last_four
    ].join(' ')
  end
end
