%h5.ui.top.attached.header{ style: 'background-color: #f9fafb;' }
  .right.content{ style: 'float: right; font-weight: normal;' }
    = link_to 'Edit', '#', data: { modal: :mailing_address_modal }

  .content
    - if address_blank?
      Missing Mailing Address
      %i.red.exclamation.triangle.icon

    - elsif address_verified?
      Verified Mailing Address
      %i.green.check.icon

    - elsif address_unverified?
      - if address_verification_available?
        Unverified Mailing Address
        %i.yellow.exclamation.triangle.icon

      - else
        Mailing Address

    - else
      - fail

  - unless address_blank?
    .sub.header
      - if printing_check?
        Your check will use this recipient mailing address.

      - elsif mailing_check?
        A check will be automatically printed and mailed to this address.

      - else
        - fail

.ui.bottom.attached.segment
  - if address_blank?
    There is no address on file for #{payee.name}. An address is required to deliver check payments.

  - else
    .ui.list
      .item= address.addressable.name
      .item= address.street_address
      .item= address.city_state_zip

    - if show_address_verification_button?
      = link_to address_verification_path, class: 'ui small button' do
        %i.magic.icon
        Verify Address
