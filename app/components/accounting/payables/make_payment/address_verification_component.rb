class Accounting::Payables::MakePayment::AddressVerificationComponent < ApplicationComponent
  include Accounting::Payables::MakePayment::Shared

  def initialize(payment:)
    super()

    @payment = payment
  end

  def render? = payee && mailing_address_required?

  private

  delegate :blank?, to: :address, prefix: true, allow_nil: true

  def address = payee&.address_for_mailed_checks

  # TODO: Delegate :verified?, :unverified? to :address, prefix: true, allow_nil: true
  def address_verified? = false
  def address_unverified? = !address_verified?

  def mailing_address_required? = printing_check? || mailing_check?

  def address_verification_available? = false # TODO

  def address_verification_path = '#'

  def show_address_verification_button? = address_unverified? && address_verification_available?
end
