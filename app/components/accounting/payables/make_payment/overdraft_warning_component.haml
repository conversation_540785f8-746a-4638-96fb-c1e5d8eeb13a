-# We want this to have the same style as .ui.error.message,
  but it comes with extra behavior like being hidden by default
  and being overridden by ujs errors.
.ui.red.message{ style: 'color: #9f3a38; background-color: #fff6f6; box-shadow: 0 0 0 1px #e0b4b4 inset,0 0 0 0 transparent;' }
  .header
    %i.red.exclamation.triangle.icon
    Overdraft Warning

  %p
    This #{amount.format} payment may exceed your account&rsquo;s estimated available balance of #{estimated_available_balance.format} by #{estimated_overdraft_amount.format}.

    If you wish to proceed, please type a confirmation below.

  .required.field
    = f.label :overdraft_acknowledgement
    = f.text_field :overdraft_acknowledgement,
      placeholder: "e.g. Recently deposited sufficient funds - #{helpers.current_property_manager.name}"
