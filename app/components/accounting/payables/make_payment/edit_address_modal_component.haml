= render ModalComponent.new id: :mailing_address_modal,
  title: 'Edit Mailing Address',
  options: { autofocus: false,
  detachable: false,
  form: :mailing_address_form,
  submit: { text: 'Save' } } do

  .ui.embedded.info.message
    This will update the #{address_description} on the
    = succeed '.' do
      = link_to "#{profile_description} for #{name}", profile_path

  = form_with id: :mailing_address_form,
    scope: :address,
    model:,
    url:,
    method:,
    local: false,
    remote: true,
    class: 'ui form' do |f|

    = f.hidden_field :kind

    .required.field
      = f.label :country
      = f.semantic_dropdown :country, country_options

    .two.fields
      .required.field
        = f.label :line_one, 'Street Address Line One'
        = f.text_field :line_one

      .field
        = f.label :line_two, 'Street Address Line Two'
        = f.text_field :line_two

    .three.fields
      .required.field
        = f.label :city
        = f.text_field :city

      .required.field
        = f.label :region, 'State or Province'
        = f.text_field :region

      .required.field
        = f.label :postal_code, 'Zip Code'
        = f.text_field :postal_code

    .ui.bottom.embedded.error.message
