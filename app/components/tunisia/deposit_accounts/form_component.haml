:ruby
  e_sign_agreement = link_to('E-Sign Agreement', legal_unit_electronic_disclosure_consent_path, target: '_blank',)
  end_user_agreement = link_to('Deposit Account End User Agreement', legal_unit_deposit_account_end_user_agreement_path, target: '_blank',)
  debit_card_agreement = link_to('Debit Card Agreement', legal_unit_debit_cardholder_agreement_path, target: '_blank',)
  revela_termas_and_services = link_to('Revela Terms of Service', terms_path, target: '_blank',)
  revela_privacy_policy = link_to('Revela Privacy Policy', privacy_path, target: '_blank',)

= form_with id: :bank_account_form,
  model: [:organization, :tunisia, bank_account],
  local: false,
  class: 'ui form' do |f|

  .two.required.fields
    .field
      = f.label :name
      = f.text_field :name,
        placeholder: 'e.g. Operating Account, Payroll, etc.'

    .field{ class: class_names(disabled: owner_disabled?) }
      = f.label :owner_id, 'Owner'
      = f.semantic_dropdown :owner_id,
        owner_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' },
        accounting__bank_account_form_target: 'ownerDropdown' }

  .three.required.fields
    .field
      = f.label :account_type
      = f.semantic_dropdown :account_type,
        account_type_options,
        {},
        class: 'ui search selection dropdown'

    .field
      = f.label :designation
      = f.semantic_dropdown :designation,
        designation_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

    .field{ class: class_names(disabled: ledger_account_disabled?) }
      = f.label :ledger_account_id, 'Ledger Account'
      = f.semantic_dropdown :ledger_account_id,
        ledger_account_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

  .three.two.fields
    .field
      = f.label :account_number
      = f.text_field :account_number,
        disabled: true,
        placeholder: 'Provided by Unit'

    .field
      = f.label :routing_number
      = f.text_field :routing_number,
        disabled: true,
        placeholder: 'Provided by Unit'

    .field
      = f.label :beginning_check_number
      = f.number_field :beginning_check_number,
        disabled: true,
        placeholder: BankAccount::DEFAULT_BEGINNING_CHECK_NUMBER

  .required.field
    .ui.checkbox
      = f.check_box :agreement_acknowledgement
      = f.label :agreement_acknowledgement do
        By creating a bank account, you agree to the 
        #{e_sign_agreement}, 
        #{end_user_agreement}, 
        #{debit_card_agreement}, 
        #{revela_termas_and_services}, and 
        #{revela_privacy_policy}

  = f.hidden_field :idempotency_key, value: idempotency_key

  .ui.error.message

  .clearfix
    = f.submit 'Save', class: 'ui primary submit button'

:javascript
  $('.ui.checkbox').checkbox();
