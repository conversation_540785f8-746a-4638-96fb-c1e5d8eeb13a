= form_with model: model,
  url: url,
  local: false,
  scope: :payment_plan_preset,
  data: { controller: 'payment-plans--presets--form' },
  class: 'ui form' do |f|

  - if model.persisted?
    .ui.info.message
      %p
        Note that editing a payment plan preset will not impact existing
        payment plans created from this preset.

  .ui.very.basic.vertical.segment
    .two.required.fields
      .field
        = f.label :name
        = f.text_field :name,
          placeholder: 'e.g. 90 Day Payment Plan'

      .field
        = f.label :type
        = f.semantic_dropdown :type,
          [['Duration', 'PaymentPlan::Preset::Duration'],
          ['Explicit Installments', 'PaymentPlan::Preset::Explicit'],
          ['Date Range', 'PaymentPlan::Preset::Range']]

  %fieldset.ui.very.basic.vertical.segment{ **fieldset_options('duration') }
    %h4.ui.header
      Installment Settings

    .required.field
      = f.label :duration_days, 'Duration (Days)'
      = f.number_field :duration_days, placeholder: 'e.g. 90', min: 2, max: 365

    .two.required.fields
      .field
        = f.label :minimum_installment_count
        = f.number_field :minimum_installment_count, min: PaymentPlan::MINIMUM_INSTALLMENT_COUNT, max: PaymentPlan::MAXIMUM_INSTALLMENT_COUNT

      .field
        = f.label :maximum_installment_count
        = f.number_field :maximum_installment_count, min: PaymentPlan::MINIMUM_INSTALLMENT_COUNT, max: PaymentPlan::MAXIMUM_INSTALLMENT_COUNT

  %fieldset.ui.very.basic.vertical.segment{ **fieldset_options('explicit') }
    %h4.ui.header
      Installment Schedule

    #installments{ data: { payment_plans__presets__form_target: 'installmentsFields' } }
      = f.fields_for :installments do |installment_f|
        = render partial: 'installment_fields', locals: { f: installment_f }

    = link_to_add_association f, :installments,
      partial: 'installment_fields',
      class: 'ui small basic button',
      data: { association_insertion_node: '#installments',
      association_insertion_method: 'append',
      payment_plans__presets__form_target: 'addInstallmentButton' } do
      %i.plus.icon
      Add Installment

  %fieldset.ui.very.basic.vertical.segment{ **fieldset_options('range') }
    %h4.ui.header
      Installment Settings

    .two.required.fields
      .field
        = f.label :range_start_date, 'Start Date'
        = f.semantic_date_field :range_start_date

      .field
        = f.label :range_end_date, 'End Date'
        = f.semantic_date_field :range_end_date

    .two.required.fields
      .field
        = f.label :minimum_installment_count
        = f.number_field :minimum_installment_count, min: PaymentPlan::MINIMUM_INSTALLMENT_COUNT, max: PaymentPlan::MAXIMUM_INSTALLMENT_COUNT

      .field
        = f.label :maximum_installment_count
        = f.number_field :maximum_installment_count, min: PaymentPlan::MINIMUM_INSTALLMENT_COUNT, max: PaymentPlan::MAXIMUM_INSTALLMENT_COUNT

  .ui.very.basic.vertical.segment
    %h4.ui.header
      Enrollment Availability

    .field
      .ui.checkbox
        = f.check_box :portal_visible
        = f.label :portal_visible, "Available in #{I18n.t('tenant_term')} Portal"

    .two.fields
      .field
        = f.label :date_available_start, 'Start Date'
        = f.semantic_date_field :date_available_start

      .field
        = f.label :date_available_end, 'End Date'
        = f.semantic_date_field :date_available_end

    - if true
      .two.fields
        .field
          = f.label :property_whitelist_ids, 'Property Allow List'
          = f.semantic_dropdown :property_whitelist_ids,
            property_list_options,
            {},
            multiple: true,
            data: { options: { multiple: true,
            placeholder: 'Leave blank to allow all properties' } },
            class: 'ui multiple search selection dropdown'

        .field
          = f.label :property_blacklist_ids, 'Property Deny List'
          = f.semantic_dropdown :property_blacklist_ids,
            property_list_options,
            {},
            multiple: true,
            data: { options: { multiple: true,
            placeholder: 'Leave blank to allow all properties' } },
            class: 'ui multiple search selection dropdown'

  .ui.very.basic.vertical.segment
    .ui.error.message

    = f.submit submit_text, class: 'ui primary submit button'

:javascript
  $('.ui.checkbox').checkbox();
