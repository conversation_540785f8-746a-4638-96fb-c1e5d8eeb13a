.filter.box#filter-box{ 'data-initialized' => 'false',
  data: { controller: 'reports--filter-box' } }
  .ui.accordion.on{ data: { reports__filter_box_target: 'accordion' } }
    .title{ style: 'display: none;' }
    .content.active
      .filter.controls.visible
        .ui.container
          = content

  .ui.container{ style: 'position: relative;' }
    %button.ui.compact.tiny.bottom.attached.filter.basic.button.on{ data: { action: 'click->reports--filter-box#toggle',
    reports__filter_box_target: 'button' } }
      %i.ui.chevron.down.icon
      Filters
