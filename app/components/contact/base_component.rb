class Contact::BaseComponent < ApplicationComponent
  def initialize(contact)
    super()
    @contact = contact
  end

  private

  attr_reader :contact

  def path_name
    if contact.is_a?(PropertyManager)
      'Employees'
    else
      contact.class.name.downcase.pluralize
    end
  end

  def index_path_method
    if contact.is_a?(PropertyManager)
      :organization_employees_path
    else
      "#{path_name}_path"
    end
  end

  def edit_contact_path
    [:edit, contact]
  end

  def documents
    @documents ||= begin
      klass = if contact.is_a?(Tenant)
                Portfolio::TenantDocuments
              else
                Portfolio::Documents
              end

      documents = klass.new(contact).documents.map do |d|
        OpenStruct.new(**d)
      end

      documents = documents.sort_by { |a| a[:created_at]&.to_i || 0 }.reverse

      documents
    end
  end

  def manage_insurances_permission
    if contact.is_a?(<PERSON>end<PERSON>)
      helpers.current_property_manager.can.manage_insurance_for_portfolio_vendors?
    else
      true
    end
  end

  def manage_documents_permission
    if contact.is_a?(Vendor)
      helpers.current_property_manager.can.manage_documents_for_portfolio_vendors?
    else
      true
    end
  end
end
