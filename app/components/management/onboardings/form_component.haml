= form_with model: member_onboarding_configuration,
  url: url,
  local: false,
  id: 'onboarding-form',
  method: form_method,
  class: 'ui form',
  data: scoped_properties_stimulus_data do |f|
  .ui.error.message

  .required.field
    = f.label :name, 'Onboarding Form Name'
    = f.text_field :name, class: 'ui input'

  .equal.width.fields
    .required.field
      = f.label :portfolio_id, 'Portfolio'
      = f.semantic_dropdown :portfolio_id,
                            portfolio_options,
                            { prompt: 'Select portfolio' },
                            data: portfolio_dropdown_data,
                            class: 'ui dropdown search',
                            disabled: disable_dropdowns?

    - if Feature.enabled?(:onboarding_enhancements, Customer.current)
      .field{ data: scoped_properties_stimulus.data { target('propertyDropdownWrapper') } }
        = render 'management/onboardings/property_dropdown', f:, property_options:, disabled: disable_dropdowns?

  .field
    .ui.grid
      .one.column.row
        .two.wide.column
          .ui.flex.margined
            = f.label :message, 'Message'
            %i.ui.icon.info.circle{
              data: {
                content: 'Add a message that will be presented to members before they begin onboarding',
                variation: 'very wide'
              },
              style: 'color: gray;'
            }
        .wide.column
          = f.text_area :message, class: 'ui input'

:javascript
  $('.info.circle.icon').popup();
