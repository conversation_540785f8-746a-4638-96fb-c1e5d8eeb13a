class Management::Onboardings::FormComponent < ApplicationComponent
  def initialize(user:, token:, member_onboarding_configuration:)
    super()
    @user = user
    @token = token
    @member_onboarding_configuration = member_onboarding_configuration
  end

  private

  attr_reader :user, :token, :member_onboarding_configuration

  def portfolio_options
    PortfoliosQuery.new.search
                   .by_any_user_properties(helpers.current_property_manager)
                   .pluck(:name, :id)
  end

  def property_options
    portfolio = member_onboarding_configuration.portfolio
    return [] if portfolio.nil?

    PropertiesQuery.for_user(user)
                   .by_portfolio(portfolio)
                   .pluck(:name, :id)
  end

  def url
    initial_setup? ? onboardings_path : onboarding_path(token)
  end

  def form_method
    initial_setup? ? :post : :patch
  end

  def disable_dropdowns?
    !initial_setup?
  end

  def initial_setup?
    token.nil?
  end

  def scoped_properties_stimulus
    @scoped_properties_stimulus ||=
      Shigeki.new('onboarding--scoped-properties', view_context: self)
  end

  def scoped_properties_stimulus_data
    scoped_properties_stimulus.data do
      controller.value(
        name: 'selectable_properties_path',
        content: selectable_properties_onboardings_path
      )
    end
  end

  def portfolio_dropdown_data
    return {} unless Feature.enabled?(:onboarding_enhancements, Customer.current)

    scoped_properties_stimulus.data { action_change('updateProperties').target('portfolio') }
  end
end
