class Maintenance::WorkOrders::AssignVendorModalComponent < ApplicationComponent
  def initialize(work_order:)
    super()
    @work_order = work_order
  end

  private

  attr_reader :work_order

  def url
    maintenance_ticket_vendor_assignments_path(work_order)
  end

  def vendor_status_stimulus
    @vendor_status_stimulus ||=
      Shigeki.new('maintenance-ticket--vendor-status', view_context: self)
  end

  def vendor_dropdown_data
    vendor_status_stimulus.data do
      action_change('updateVendorStatus')
        .other({ options: { placeholder: 'Select' } })
    end
  end

  def vendor_options
    [['Select', nil]] + VendorsQuery.new.search.with_email.pluck(:name, :id)
  end

  def include_tenant_disabled?
    tenant.nil?
  end

  def include_tenant_label_text
    details = tenant&.name || "No #{I18n.t('tenant_term')}"

    "Include #{I18n.t('tenant_term')} Information (#{details})"
  end

  def include_owner_visible?
    Customer.current.single_family_management?
  end

  def include_owner_disabled?
    owner.nil?
  end

  def include_owner_label_text
    details = owner&.name || 'No Owner'

    "Include Owner Information (#{details})"
  end

  def proof_required_visible?
    vendor_assignment_proof_required
  end

  def proof_required_default
    vendor_assignment_proof_required
  end

  delegate :configuration, :owner, :tenant, to: :work_order
  delegate :vendor_assignment_proof_required, to: :configuration
end
