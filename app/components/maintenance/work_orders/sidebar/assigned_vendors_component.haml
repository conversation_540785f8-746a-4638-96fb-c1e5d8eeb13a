= render ActionSidebar::ItemComponent.new(title: 'Assigned Vendors') do |item|
  - item.with_hint do
    = hint

  - if assignments.any?
    .ui.link.list
      - assignments.each do |assignment|
        .item
          = link_to assignment.vendor.name, vendor_path(assignment.vendor)
          &nbsp;
          = link_to '(remove)',
            maintenance_ticket_vendor_assignment_path(@work_order,
            assignment.id),
            remote: true,
            method: :delete,
            data: { confirm: "Unassign #{assignment.vendor.name}?" }

  - item.with_action_link do
    = link_to 'Assign Vendor', nil, data: { modal: :vendor_assignment_modal }

= render Maintenance::WorkOrders::AssignVendorModalComponent.new(work_order: @work_order)
