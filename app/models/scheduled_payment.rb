##
# Stores information regarding when {Payment}s are to be created.
class ScheduledPayment < ApplicationRecord
  include ScheduledPayment::PreventedByEvictions

  # @!attribute recurring?
  #   @return [<PERSON>olean] if this scheduled payment should happen monthly,
  #     on the day of {#date}.
  #
  # @!attribute date
  #   @return [Date] the date when the first {Payment} should be created.
  #     Recurring payments will be created on this day of the month.
  #
  # @!attribute last_ran_at
  #   @return [DateTime] the last time this schedule created a {Payment}. Used
  #     to prevent creating duplicates and to run payments after downtime.
  #
  # @!attribute pay_balance?
  #   @return [<PERSON><PERSON>an] if the entire balance should be paid as opposed to a
  #     fixed amount.

  enum :status, {
    idle: 0,
    processing: 1,
    failed: 2
  }

  belongs_to :destination, polymorphic: true
  belongs_to :source, polymorphic: true
  belongs_to :payment_method, polymorphic: true, optional: true
  belongs_to :lease_membership, inverse_of: :scheduled_payments, optional: true
  belongs_to :simple_agreement, class_name: 'Agreements::SimpleAgreement',
                                inverse_of: :scheduled_payments, optional: true
  belongs_to :property, inverse_of: :scheduled_payments, optional: false

  has_many :payments, dependent: :nullify
  has_one :payment_plan_installment, class_name: 'PaymentPlan::Installment',
                                     dependent: :restrict_with_error

  validates :date, presence: true
  validates :payment_method, presence: true, if: :next_payment_date
  validates :amount, numericality: { greater_than: 0 }, unless: :pay_balance?
  validate :ensure_accounting_context

  scope :recurring, -> { where(recurring: true) }
  scope :one_time, -> { where(recurring: false) }
  scope :upcoming, -> { recurring.or(one_time.where(last_ran_at: nil)) }

  # @!attribute amount
  #   @return [Money] the amount to pay, if this is not {#pay_balance?}
  monetize :amount_cents

  audited only: %i[date recurring pay_balance amount_cents failure_reason]

  # @return [Date] when the next payment is scheduled for. If it is recurring,
  #   this date is one month following the last scheduled date of payment
  def next_payment_date
    return nil unless date

    return [date, Time.zone.today].max unless last_run_date

    return nil unless recurring?

    # Forward date #date to skip months, despite last ran at
    return [date, Time.zone.today].max if last_run_date.before?(date)

    months = ((last_run_date - date) / 31).round + 1

    [date + months.months, Time.zone.today].max
  end

  def last_run_date
    last_ran_at&.to_date
  end

  def accounting_context
    if lease_membership
      lease_membership.accounting_context
    else
      simple_agreement.accounting_context
    end
  end

  def ledger
    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]) &&
       (agreement = source.simple_agreements.last)
      return agreement.ledger
    end

    lease&.ledger || source.ledger
  end

  delegate :payment_plan, to: :payment_plan_installment, allow_nil: true
  delegate :lease, to: :lease_membership, allow_nil: true

  private

  def ensure_accounting_context
    return if lease_membership || simple_agreement

    errors.add(:base, 'Must have a lease or agreement')
  end
end
