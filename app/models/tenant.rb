class Tenant < ApplicationRecord
  include Avatar
  include BankAccountOwner
  include Collections::Contact
  include Tenant::Searchable # Contact overrides #as_index_json, so this needs to be first
  include Contact
  include DeviseModifications
  include ElectronicSignature::Recipient
  include FormattedPhones
  include HasAge
  include HasAttachments
  include HasLedger
  include HasMetadata
  include HasPaymentMethods
  include HasScheduledPayments
  include Invoicing
  include Insurance::PolicyHolder
  include Inspection::Report::ActivityActor
  include Maintenance::TicketEventAuthor
  include NameStripping
  include MemberOnboardable
  include PasswordComplexity
  include PerformsInspections
  include Taggable
  include Taxpayer
  include Telephony::ProxySessionParticipant
  include Tenant::CustomData
  include User::HasLoginFingerprints
  include ValidEmailFormat

  enum :kind, {
    contact: 5,
    lead: 0,
    applicant: 1,
    screened: 2,
    screening: 3,
    resident: 4
  }

  enum :temperature, { hot: 4, interested: 3, contacted: 2, not_interested: 1 }

  belongs_to :leasing_agent, class_name: 'PropertyManager'

  # Lead Property
  belongs_to :lead_property, class_name: 'Property'

  has_many :lease_memberships, dependent: :destroy
  has_many :leases, through: :lease_memberships
  has_many :lease_chains, -> { distinct },
           through: :leases, source: :chain,
           class_name: 'Lease::Chain'
  has_many :units, through: :leases
  has_many :properties, through: :units
  has_many :maintenance_tickets, dependent: :nullify
  has_many :guest_cards, dependent: :destroy
  has_many :lease_application_memberships, dependent: :destroy
  has_many :lease_applications, through: :lease_application_memberships
  has_many :tours, inverse_of: :lead, foreign_key: 'lead_id',
                   dependent: :destroy
  has_many :unit_reservations, class_name: 'Unit::Reservation',
                               dependent: :destroy
  has_many :utility_transfers, class_name: 'Utilities::Transfer',
                               dependent: :destroy

  # Tenant specified as a lead on a {LeaseApplication}
  has_many :lead_lease_applications, class_name: 'LeaseApplication',
                                     inverse_of: :lead, foreign_key: 'lead_id',
                                     dependent: :destroy

  has_many :background_checks, through: :lease_applications
  has_many :broadcasts_recipients, as: :recipient, dependent: :destroy
  has_many :broadcasts, through: :broadcasts_recipients
  has_one :forwarding_address, as: :addressable,
                               class_name: 'Address',
                               inverse_of: :addressable,
                               dependent: :destroy
  has_many :maintenance_surveys, dependent: :destroy

  has_many :simple_agreement_memberships,
           class_name: 'Agreements::SimpleAgreement::Membership',
           dependent: :destroy

  has_many :simple_agreements, through: :simple_agreement_memberships

  # rubocop:disable Rails/HasManyOrHasOneDependent
  has_one :status_view,
          class_name: 'Tenant::Status',
          inverse_of: :tenant
  # rubocop:enable Rails/HasManyOrHasOneDependent

  validates :first_name, presence: true
  validates :last_name, presence: true
  validates :email, uniqueness: true, allow_blank: true
  validates :external_id, uniqueness: true, allow_blank: true
  validates :password, length: { minimum: 8 }, allow_nil: true

  strip_attributes only: :external_id

  # Include default devise modules. Others available are:
  # :lockable, :timeoutable and :omniauthable
  devise :database_authenticatable, :registerable, :recoverable, :rememberable,
         :trackable, :confirmable
  skip_callback :create, :before, :generate_confirmation_token

  before_validation :clear_blank_email

  accepts_nested_attributes_for :forwarding_address, reject_if: \
    proc { |attributes| attributes['line_one'].blank? }

  def agree_to_sms=(value)
    return if agreed_to_sms_at.present?

    self.agreed_to_sms_at = Time.zone.now if ActiveModel::Type::Boolean.new.cast(value)
  end

  def name
    "#{first_name} #{last_name}"
  end

  def active_lease_memberships
    lease_memberships
      .joins(:lease)
      .merge(Lease.active)
  end

  def current_lease_membership
    active_lease_memberships.first
  end

  def current_lease
    current_lease_membership&.lease
  end

  def current_unit
    current_lease&.unit
  end

  def current_property
    current_unit&.property
  end

  def to_s
    name
  end

  def url
    if resident?
      "/manage/tenants/#{id}"
    else
      "/leasing/leads/#{id}"
    end
  end

  # Only ever send welcome email explicitly
  def send_confirmation_notification?
    false
  end

  # TODO: db level
  def company
    current_property&.company || Company.first
  end

  def address
    current_unit&.address ||
      forwarding_address ||
      leases.last&.address ||
      lease_application_memberships.last&.applicant&.address
  end

  # Prefer forwarding address when available
  def billing_address
    forwarding_address || address
  end

  def contact_type
    kind.humanize
  end

  # Here to match Vendor, Company behavior during payment processing
  def primary_contact
    self
  end

  def update_agent(property_manager)
    leasing_agent.present? || update(leasing_agent: property_manager)
  end

  delegate :service_area, to: :current_unit, allow_nil: true
  delegate :status, to: :status_view

  private

  def clear_blank_email
    self.email = nil if email.blank?
  end
end
