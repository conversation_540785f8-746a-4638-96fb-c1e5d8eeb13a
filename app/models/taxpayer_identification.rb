##
# A Taxpayer Identification Number (TIN).
# This is either an EIN, SSN, or ITIN.
# Attached to +Vendor+s or +Owner+s for 1099-MISC filing.
class TaxpayerIdentification < ApplicationRecord
  TIN_VALIDATION_REGEX = /\A[0-9]{9}\Z/

  belongs_to :taxpayer, polymorphic: true

  attr_encrypted :tin, key: ENV['TIN_KEY'].first(32)

  enum :tin_type, { ein: 0, ssn: 1, itin: 2 }

  before_validation :cleanse_tin

  validates :taxpayer, presence: true
  validates :tin_type, presence: true
  validates :tin, presence: true, format: { with: TIN_VALIDATION_REGEX }

  def formatted_tin
    case tin_type
    when 'ein'
      tin.gsub(/(\d{2})(\d{7})/, '\1-\2')
    else
      tin.gsub(/(\d{3})(\d{2})(\d{4})/, '\1-\2-\3')
    end
  end

  def starred_tin(expose = 0)
    return formatted_tin.gsub(/\D/, '*') unless expose > 0

    formatted_tin[0..((-1 * expose) - 1)].gsub(/\d/, '*') +
      formatted_tin[(-1 * expose)..]
  end

  private

  def cleanse_tin
    return if tin.blank?

    self.tin = tin.gsub(/[^\d]/, '')
  end
end
