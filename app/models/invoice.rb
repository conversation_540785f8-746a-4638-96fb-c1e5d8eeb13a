class Invoice < ApplicationRecord
  include Approvals::Approvable
  include CommercialDocument
  include HasInvoicePayments
  include Invoice::Batchable
  include Invoice::Forwardable
  include Invoice::HasAttachments
  include Invoice::HasAssociatedContacts
  include Invoice::HasJournalEntries
  include Invoice::HasNotificationPreference
  include Invoice::HasPaymentPlans
  include Invoice::HasRevelaInvoiceNumber
  include Invoice::ImpactsCashBasis
  include Invoice::MaintenanceBillingResult
  include Invoice::Recurrable
  include Invoice::UniqueInvoiceNumbers
  include Invoice::Searchable
  include Invoice::ApiV2Resource # Specs fail if this isn't last

  has_one :invoice_total_payment

  belongs_to :buyer, polymorphic: true
  belongs_to :seller, polymorphic: true
  belongs_to :invoice_processing_email, class_name: 'InvoiceProcessing::Email',
                                        optional: true

  has_one :billing, dependent: :destroy
  has_many :line_items, dependent: :destroy,
                        after_add: :update_amount, after_remove: :update_amount
  has_many :invoice_payments, dependent: :destroy,
                              inverse_of: :invoice
  has_many :payments, through: :invoice_payments

  has_many :linkages, dependent: :destroy
  has_many :linked_maintenance_tickets,
           through: :linkages, source: :maintenance_ticket
  has_many :linked_projects,
           through: :linkages, source: :project
  has_one :invoice_balance, class_name: 'Accounting::InvoiceBalance'
  has_one :accounting_debit_card_purchase, class_name: 'Accounting::DebitCardPurchase',
                                           dependent: :restrict_with_error

  accepts_nested_attributes_for :line_items, allow_destroy: true

  validates :buyer, presence: true
  validates :buyer, exclusion: {
    in: ->(invoice) { [invoice.seller] },
    message: 'must be different than seller'
  }, if: :seller
  validates :buyer_type, inclusion: { in: Accounting::TRANSACTION_ENTITIES }
  validates :seller, presence: true
  validates :seller_type, inclusion: { in: Accounting::TRANSACTION_ENTITIES }
  validates :description, presence: true
  validates :post_date, presence: true
  validates :physical_date, presence: true
  validates :amount, numericality: { greater_than: 0 }
  validate :at_least_one_line_item
  validate :due_date_after_invoice_date
  validate :valid_buyer_seller_pair?

  before_validation :default_physical_date, :update_amount

  after_touch :update_amount_after_touch

  alias_attribute :date, :post_date

  monetize :amount_cents

  audited except: %i[payment_batch_order recurring_schedule_id]
  has_associated_audits

  include HasAuditedInvoicePayments

  scope :paid, InvoicesQuery::PaidScope
  scope :unpaid, InvoicesQuery::UnpaidScope
  scope :open, InvoicesQuery::OpenScope
  scope :approved, InvoicesQuery::ApprovedScope

  amoeba do
    include_association :line_items
    nullify :forwarded_from_id
    nullify :invoice_number
    nullify :notification_sent_at
    nullify :payment_batch_id
    set payment_batch_order: 0
    nullify :waived_at
    customize ->(_original, duplicate) { duplicate.send(:set_default_approval) }
  end

  PERMISSABLE_ATTRIBUTES = [
    :post_date, :due_date, :physical_date,
    :description, :invoice_number, :note, :payment_plan_eligible,
    :buyer_lease_membership_id, :buyer_unit_id,
    :seller_lease_membership_id, :seller_unit_id,
    :loan_id,
    line_items_attributes: LineItem::PERMISSABLE_ATTRIBUTES,
    shrine_attachments_attributes: {}
  ].freeze

  def balance(date = nil)
    return Money.zero if waived? && (date.nil? || date.to_date >= waived_at)

    amount - total_paid(date)
  end

  def paid?(date = nil)
    !waived?(date) && balance(date) <= 0
  end

  def unpaid?(date = nil)
    !paid?(date)
  end

  def open?(date = nil)
    !waived?(date) && unpaid?(date)
  end

  def closed?(date = nil)
    !open?(date)
  end

  def total_paid(date = nil)
    if date.nil? && persisted?
      Money.new(invoice_total_payment&.payment)
    else
      date ||= Time.zone.now
      payments_by_date = invoice_payments
                         .reject { |ip| ip.reversed? && ip.reversed_at <= date }
                         .select { |ip| ip.date <= date }
      Money.sum(payments_by_date.map(&:amount))
    end
  end

  # TODO: Same as overdue?
  # TODO: Do Time.zone.today > due_date
  def late?
    Time.zone.now >= due_date && unpaid?(due_date)
  end

  def to_s
    description
  end

  def invoice_number
    super.presence || "R#{id}"
  end

  # TODO: Remove these eventually
  def url
    if payable?
      "/accounting/payables/invoices/#{id}"
    else
      "/accounting/receivables/invoices/#{id}"
    end
  end

  def receivable?
    ['Company', 'Property'].include?(seller_type)
  end

  def payable?
    ['Company', 'Property'].include?(buyer_type)
  end

  def property
    if buyer.is_a? Property
      buyer
    elsif seller.is_a? Property
      seller
    end
  end

  def waived?(date = nil)
    return false if waived_at.nil?

    if date.present?
      waived_at < date.end_of_day
    else
      true
    end
  end

  def buyer_sgid=(sgid)
    self.buyer = GlobalID::Locator.locate_signed(sgid)
  end

  def seller_sgid=(sgid)
    self.seller = GlobalID::Locator.locate_signed(sgid)
  end

  def buyer_changed?
    buyer_id_changed? || buyer_type_changed?
  end

  def seller_changed?
    seller_id_changed? || seller_type_changed?
  end

  def buyer_was
    return nil unless (id = buyer_id_was)

    buyer_type_was.safe_constantize&.find_by(id: id)
  end

  def seller_was
    return nil unless (id = seller_id_was)

    seller_type_was.safe_constantize&.find_by(id: id)
  end

  private

  def at_least_one_line_item
    return if line_items.any?

    errors.add(:invoice, 'must contain at least one line item')
  end

  def due_date_after_invoice_date
    return if physical_date.blank? || due_date.blank?

    return unless physical_date > due_date

    errors.add(:due_date, 'cannot be before invoice date')
  end

  # Assuming buyer and seller are set (validated elsewhere), an {Invoice} must
  # be associated to at least an entity or property; e.g. a {Vendor} cannot
  # invoice a {Tenant}.
  def valid_buyer_seller_pair?
    return true unless buyer && seller

    return if valid_target_pair?(buyer, seller)

    errors.add(:base, 'At least Buyer or Seller must be a Company or Property')
  end

  def default_physical_date
    self.physical_date ||= post_date
  end

  def update_amount(_line_item = nil)
    self.amount = line_items_total
  end

  def line_items_total
    Money.sum(line_items.reject(&:marked_for_destruction?).map(&:amount))
  end

  # This is called becuase line_items belong_to invoice touch: true, so this
  # updates the amount if line items are created, destroyed, or updated outside
  # the context of the line_items relation.
  def update_amount_after_touch
    line_items.reload
    save!
  end
end
