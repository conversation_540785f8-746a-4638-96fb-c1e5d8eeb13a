class InvoiceAttachment < Attachment
  has_attached_file :upload, storage: :s3,
                             bucket: ENV.fetch('S3_INVOICES_BUCKET', nil),
                             s3_region: 'us-east-1',
                             s3_server_side_encryption: 'AES256',
                             s3_protocol: 'https',
                             path: '/:hash/:filename',
                             validate_media_type: false,
                             hash_secret: \
                             Rails.application.secrets.secret_key_base

  has_many :invoice_processing_associated_attachments,
           class_name: 'InvoiceProcessing::AssociatedAttachment',
           inverse_of: :attachment, foreign_key: :attachment_id,
           dependent: :destroy

  do_not_validate_attachment_file_type :upload

  def self.options
    { storage: :s3, bucket: ENV.fetch('S3_INVOICES_BUCKET', nil) }
  end

  def direct_upload_url
    upload.url
  end
end
