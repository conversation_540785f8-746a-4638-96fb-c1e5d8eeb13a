class MemberOnboarding::Configuration < ApplicationRecord
  include Archivable
  extend MemberOnboarding::Configuration::AutoAssignableForProperties
  include MemberOnboarding::PropertyOnboardable

  set_callback :archive, :before, :remove_assignments_and_defaults

  attr_accessor :skip_module_selection_validation

  has_one :information_collection, class_name: 'MemberOnboarding::InformationCollection',
                                   inverse_of: :configuration,
                                   dependent: :destroy

  has_one :lease_agreement, class_name: 'MemberOnboarding::LeaseAgreement',
                            inverse_of: :configuration,
                            dependent: :destroy

  has_one :charge, class_name: 'MemberOnboarding::Charge',
                   inverse_of: :configuration,
                   dependent: :destroy

  has_one :guarantor, class_name: 'MemberOnboarding::Guarantor',
                      inverse_of: :configuration,
                      dependent: :destroy

  has_one :membership_agreement, class_name: 'MemberOnboarding::MembershipAgreement',
                                 inverse_of: :configuration,
                                 dependent: :destroy

  has_one :risk_release, class_name: 'MemberOnboarding::RiskRelease',
                         inverse_of: :configuration,
                         dependent: :destroy

  belongs_to :portfolio, optional: false

  has_many :member_assignments, class_name: 'MemberOnboarding::Assignment',
                                inverse_of: :configuration,
                                dependent: :destroy

  # TODO: remove when onboarding_enhancements is completed
  has_many :member_completions, class_name: 'MemberOnboarding::Completion',
                                inverse_of: :configuration,
                                dependent: :destroy

  validates :name, presence: true, uniqueness: true
  validate :at_least_one_module_selected, on: :create, unless: :skip_module_selection_validation
  validates_associated :information_collection, :lease_agreement,
                       :charge, :guarantor, :membership_agreement, :risk_release, allow_nil: true

  def module_ids
    # This follows the order the modules show up in module selection
    [
      information_collection,
      guarantor,
      membership_agreement,
      lease_agreement,
      charge,
      risk_release
    ].compact.map { |module_component| module_component.class.name.demodulize.underscore }
  end

  def at_least_one_module_selected
    return unless module_ids.empty?

    errors.add(:base, 'At least one module must be selected')
  end

  def remove_assignments_and_defaults
    member_assignments.destroy_all
    property_memberships.destroy_all
    auto_assign_property_memberships.each do |auto_assign_property_membership|
      auto_assign_property_membership.update!(auto_assign: false)
    end
  end
end
