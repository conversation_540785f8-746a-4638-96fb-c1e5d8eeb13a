##
# Binds a group of {Tenant}s via {LeaseMembership}s to a {Unit} for a period of
# time.
class Lease < ApplicationRecord
  include Agreements::Agreement
  include DateOrdering
  include ElectronicSignable
  include HasChargeSchedule
  include HasLedger
  include HasMetadata
  include Lease::Archivable
  include Lease::ComputedValues
  include Lease::HasAddenda
  include Lease::HasInsurancePolicies
  include Lease::HasMembers
  include Lease::HasMoveOuts
  include Lease::HasNextRentDate
  include Lease::Renewable
  include Lease::TerminationReasons
  include HasAssociatedPaymentPlans

  # @!attribute [rw] kind
  #   @return [Enumerable<String>] the kind of lease.
  # @!method fixed?
  #   Returns true if this is a fixed lease. Fixed leases have a defined start
  #   and end date.
  # @!method month_to_month?
  #   Returns true if this is a month to month lease. These kinds of leases do
  #   not have a defined end date.
  # @!method rollover?
  #   Returns true if this is a rollover lease. These kinds of leases are fixed
  #   but become month_to_month after their end date.
  enum :kind, {
    fixed: 0,
    month_to_month: 1,
    rollover: 2
  }

  # rubocop:disable Rails/HasManyOrHasOneDependent
  has_one :chain_membership, class_name: 'Lease::Chain::Membership'
  # rubocop:enable Rails/HasManyOrHasOneDependent
  has_one :chain, through: :chain_membership, class_name: 'Lease::Chain'
  has_one :api_v2_aging_delinquency,
          through: :chain,
          source: :aging_delinquency_materialized,
          class_name: 'Lease::Chain::AgingDelinquency::Materialized'

  include Lease::Evictable # Needs chain defined

  belongs_to :unit

  has_many :payment_plans, through: :lease_memberships
  has_many :maintenance_tickets, dependent: :nullify
  has_many :parking_reservations, dependent: :destroy
  has_one :parking_allocation, dependent: :destroy,
                               class_name: 'Parking::Allocation'
  has_many :lease_pet_memberships, dependent: :destroy
  has_many :pets, through: :lease_pet_memberships

  # rubocop:disable Rails/HasManyOrHasOneDependent
  has_one :computed_amounts, class_name: 'Lease::ComputedAmounts'
  # rubocop:enable Rails/HasManyOrHasOneDependent

  validates :kind, presence: true
  validates :end_date, presence: true
  validates :start_date, presence: true
  validates :unit, presence: true
  validates :grace_period_days, numericality: {
    only_integer: true, greater_than_or_equal_to: 0, allow_nil: true
  }
  validate :must_have_reasonable_start_date
  validate :must_have_reasonable_end_date

  accepts_nested_attributes_for :charge_schedule_entries, allow_destroy: true
  accepts_nested_attributes_for :parking_allocation, reject_if: :all_blank

  delegate :name, to: :primary_tenant, allow_nil: true
  delegate :property, :floorplan, to: :unit
  delegate :company, :configuration, to: :property
  delegate :portfolio, to: :company

  scope :active, lambda { |date = Time.zone.today|
    unarchived.where(start_date: ..date, end_date: date..)
  }

  amoeba do
    nullify :executed_at
    include_association :charge_schedule
    include_association :lease_memberships
  end

  audited
  has_associated_audits

  def active?(date = Time.zone.today)
    unarchived? && start_date <= date && end_date >= date
  end

  def active_insurance_policy?
    insurance_policies.active.exists?
  end

  # @return [Money] the total amount of recurring charge_schedule_entries on
  # this lease.
  def amount
    Money.sum(charge_schedule.entries.select(&:recurring?).map(&:amount))
  end

  def to_s
    "#{unit.name} from #{start_date.to_fs(:short_date)} to #{end_date.to_fs(:short_date)}"
  end

  def invoices
    InvoicesQuery.new.search.by_lease(self)
  end

  def document_template
    templates = Document.template_options(
      template_type: :lease, property_id: property.id
    ).or(
      Document.template_options(
        template_type: :lease,
        portfolio_id: portfolio.id
      )
    )

    templates = templates.order(created_at: :desc)

    templates.find_by('template_options @> ?', { tenant_count: tenants.count }.to_json) ||
      templates.first ||
      Document.template_options(template_type: :lease).order(created_at: :desc).first
  end

  def unexecuted_lease_copy
    Document.template_options(
      template_type: :unexecuted_lease, lease_id: id
    ).order(created_at: :desc).first
  end

  def unexecuted_lease_document
    Document.template_options(
      template_type: :unexecuted_lease_template, lease_id: id
    ).order(created_at: :desc).first
  end

  def partially_signed_document
    Document.template_options(
      template_type: :partially_signed_lease, lease_id: id
    ).order(created_at: :desc).first
  end

  def executed_lease_document
    Document.template_options(
      template_type: :executed_lease, lease_id: id
    ).order(created_at: :desc).first
  end

  def signature_file_name(executed)
    [
      property.name,
      unit.name,
      tenants.map(&:name).join(' '),
      executed ? 'executed' : 'unexecuted'
    ].map(&:parameterize).join('-').underscore + '.pdf'
  end

  def after_document_generation
    return unless Customer.current_subdomain == 'brown'

    users = PropertyManager.find([4, 12])

    link = Rails.application.routes.url_helpers.leasing_lease_url(
      self, subdomain: Customer.current_subdomain
    )

    title = 'A lease document is ready for your review'

    description = unit.qualified_name

    users.each do |user|
      Notification.create!(
        kind: :review_lease_document,
        user: user,
        resource: self,
        link: link,
        title: title,
        description: description
      )
    end
  end

  def after_execution
    Lease::Execute.call(lease: self)
  end

  def url
    routes.leasing_lease_path(self)
  end

  def own_and_associated_audits
    Audited::CustomAudit.where(auditable: self).or(
      Audited::CustomAudit.where(associated: self)
    ).or(
      Audited::CustomAudit.where(associated: charge_schedule)
    )
  end

  delegate :ledger, to: :chain

  delegate :balance, :held_security_deposit, :charged_security_deposit,
           :aging_delinquency, to: :ledger

  delegate :address, to: :unit

  private

  def must_have_reasonable_start_date
    return if start_date&.after?(Date.new(1969, 12, 31))

    errors.add(:start_date, 'must be after 1969')
  end

  def must_have_reasonable_end_date
    return if end_date&.before?(Date.new(2100, 1, 1))

    errors.add(:end_date, 'must be before 2100')
  end
end
