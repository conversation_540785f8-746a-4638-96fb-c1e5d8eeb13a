class ApplicationRecord < ActiveRecord::Base
  self.abstract_class = true

  connects_to database: { writing: :primary, reading: :primary_replica }

  include ::Serialization

  include Rails.application.routes.url_helpers

  def self.searchable_by(*fields)
    include Searchable::Index
    self.index_fields = fields

    settings index: { number_of_shards: 1 } do
      mappings dynamic: false do
        fields.each do |field|
          indexes field, analyzer: 'standard', type: 'text'
        end

        indexes :tenant, type: 'keyword'
        indexes :property_ids, type: 'keyword'
        indexes :portfolio_ids, type: 'keyword'
        indexes :company_ids, type: 'keyword'
        indexes :non_scoped, type: 'keyword'
      end
    end
  end

  def self.all_from_customers(customers = Customer.all)
    if name.in?(Apartment.excluded_models)
      fail "#{name} is on the public schema"
    end

    customers.map do |customer|
      from(
        %(
          (
            SELECT #{table_name}.*, #{customer.id} AS union_customer_id
            FROM "#{customer.subdomain}".#{table_name}
          ) #{table_name}
        )
      )
    end.reduce(:union_all)
  end

  ##
  # Name to use for display when showing audit info.
  #
  # Override to customize.
  def auditable_name
    if respond_to?(:name)
      name
    elsif respond_to?(:description)
      description&.truncate(30) || ''
    else
      "#{self.class.name.humanize.downcase} #{id}"
    end
  end

  def self.has_attached_file(*attachment_names, **options)
    uses_s3 = Rails.env.production? ||
              (ENV['LOCAL_S3']&.to_s == 'true' && !ENV['CI'])

    if uses_s3
      super
    else
      super(*attachment_names, storage: :filesystem)
    end
  end

  protected

  def routes
    Rails.application.routes.url_helpers
  end
end
