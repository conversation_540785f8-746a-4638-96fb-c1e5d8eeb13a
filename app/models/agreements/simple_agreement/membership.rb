class Agreements::SimpleAgreement::Membership < ApplicationRecord
  belongs_to :simple_agreement,
             class_name: 'Agreements::SimpleAgreement',
             inverse_of: :memberships,
             optional: false

  belongs_to :tenant,
             inverse_of: :simple_agreement_memberships,
             optional: false

  has_one :risk_release_enrollments,
          class_name: 'RiskRelease::Enrollment',
          inverse_of: :simple_agreement_membership,
          foreign_key: :simple_agreement_membership_id,
          dependent: :destroy

  after_create_commit :onboarding_auto_assign

  enum :role, {
    primary: 0,
    guardian: 1
  }

  validates :role, presence: true

  delegate :property, :company, :configuration, :ledger, :accounting_context,
           to: :simple_agreement

  def onboarding_auto_assign
    return if guardian? || !Feature.enabled?(:onboarding_setup, Customer.current)

    MemberOnboarding::AutoAssignJob.perform_later(self)
  end
end
