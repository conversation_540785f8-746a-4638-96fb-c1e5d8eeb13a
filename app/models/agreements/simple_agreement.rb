class Agreements::SimpleAgreement < ApplicationRecord
  include AccountingContext
  include Agreements::Agreement
  include Archivable
  include DateOrdering
  include ElectronicSignable
  include HasAssociatedPaymentPlans
  include HasChargeSchedule
  include HasLedger
  include HasMetadata
  include Taggable

  belongs_to :agreement_type, optional: false, inverse_of: :simple_agreements

  has_many :memberships,
           class_name: 'Agreements::SimpleAgreement::Membership',
           dependent: :destroy,
           inverse_of: :simple_agreement
  has_many :tenants, through: :memberships

  # rubocop:disable Rails/HasManyOrHasOneDependent
  has_one :primary_membership, -> { primary },
          class_name: 'Agreements::SimpleAgreement::Membership',
          inverse_of: :simple_agreement
  # rubocop:enable Rails/HasManyOrHasOneDependent
  has_one :primary_tenant, through: :primary_membership, source: :tenant

  belongs_to :company, optional: false, inverse_of: :simple_agreements
  belongs_to :property, optional: true, inverse_of: :simple_agreements

  has_many :scheduled_payments, inverse_of: :simple_agreement,
                                dependent: :restrict_with_error
  has_many :payment_plans, inverse_of: :simple_agreement,
                           dependent: :restrict_with_error

  validate :one_primary_member

  scope :current, lambda { |date = Time.zone.today|
    unarchived.merge(
      where(start_date: [..date, nil])
    ).merge(
      where(end_date: [date.., nil])
    )
  }

  def unexecuted_lease_document
    Document.where(parent: self).template_options(
      template_type: :unexecuted_simple_agreement_template
    ).order(created_at: :desc).first || agreement_type.document_template
  end

  def unexecuted_lease_copy
    Document.where(parent: self).template_options(
      template_type: :unexecuted_simple_agreement
    ).order(created_at: :desc).first
  end

  def partially_signed_document
    Document.template_options(
      template_type: :partially_signed_simple_agreement, lease_id: id
    ).order(created_at: :desc).first
  end

  def executed_lease_document
    Document.where(parent: self).template_options(
      template_type: :executed_simple_agreement
    ).order(created_at: :desc).first
  end

  def signature_file_name(executed)
    components = [
      property&.name,
      primary_tenant.name,
      agreement_type.name,
      executed ? 'executed' : 'unexecuted'
    ]

    "#{components.map(&:parameterize).join('-').underscore}.pdf"
  end

  def amount
    Money.zero
  end

  def primary_tenant
    memberships.find(&:primary?)&.tenant
  end

  def url
    routes.leasing_agreement_path(self, type: agreement_type.slug)
  end

  def lease_memberships
    []
  end

  def parking_allocation
    nil
  end

  def auditable_name
    'Agreement'
  end

  delegate :name, to: :primary_tenant
  delegate :configuration, to: :company

  private

  def one_primary_member
    return if memberships.one?(&:primary?)

    errors.add(:base, 'must have exactly one primary member')
  end
end
