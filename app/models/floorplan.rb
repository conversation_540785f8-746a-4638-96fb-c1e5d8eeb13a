class Floorplan < ApplicationRecord
  PERMISSABLE_ATTRIBUTES = %i[
    name square_feet bedrooms bathrooms price image
  ].freeze

  default_scope { order(name: :asc, square_feet: :asc) }

  include HasAvailability
  include HasOccupancy

  belongs_to :property, inverse_of: :floorplans
  has_one :listing, dependent: :destroy
  has_many :units, dependent: :restrict_with_error
  has_many :available_units, -> { Unit.unarchived.vacant }, class_name: 'Unit'
  has_many :available_soon_units,
           -> { UnitsQuery.new.search.unarchived.with_date_available },
           class_name: 'Unit'
  has_many :lease_applications, dependent: :restrict_with_error

  validates :name, presence: true
  validates :property, presence: true
  validates :square_feet, presence: true, numericality: {
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: 1_000_000
  }
  validates :bedrooms, presence: true, numericality: {
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: 1000
  }
  validates :bathrooms, presence: true, numericality: {
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: 1000
  }

  monetize :price_cents

  has_attached_file :image, storage: :s3,
                            bucket: ENV.fetch('S3_UPLOADS_BUCKET', nil),
                            s3_region: 'us-east-1',
                            s3_server_side_encryption: 'AES256',
                            s3_protocol: 'https',
                            path: '/:xroot/:hash/:filename',
                            hash_secret: \
                            Rails.application.secrets.secret_key_base

  validates_attachment_content_type :image, content_type: %r{\Aimage/.*\z}

  Paperclip.interpolates :xroot do |attachment, _style|
    [
      ('development_uploads' if Rails.env.development?),
      ('test_uploads' if Rails.env.test?),
      Customer.current.subdomain,
      'floorplan',
      attachment.instance.id
    ].compact.join('/')
  end

  def to_s
    name
  end

  def date_available
    @date_available ||= available_soon_units.filter_map(&:date_available).min
  end

  def price_per_square_foot
    return Money.zero if square_feet.zero?

    price / square_feet
  end
end
