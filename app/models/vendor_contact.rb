class VendorContact < ApplicationRecord
  include FormattedPhones
  include NameStripping
  include Notifications::Notifiable

  has_one :user_profile, class_name: 'User::Profile', as: :profile,
                         dependent: :destroy

  belongs_to :vendor

  validates :first_name, presence: true
  validates :email, format: Devise.email_regexp, if: -> { email.present? }

  def name
    [first_name, last_name].join(' ')
  end
end
