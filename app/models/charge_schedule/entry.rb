##
# Represents a recurring charge for a lease, e.g. the monthly rent amount or a
# pet fee. Individual dues for a +LeaseMembership+ are described by
# +allocations+.
class ChargeSchedule::Entry < ApplicationRecord
  include DateOrdering

  belongs_to :charge_schedule,
             inverse_of: :entries,
             optional: false
  belongs_to :account,
             class_name: 'Plutus::Account',
             inverse_of: :charge_schedule_entries,
             optional: false
  belongs_to :charge_preset, optional: true,
                             inverse_of: :charge_schedule_entries
  has_many :allocations,
           inverse_of: :entry,
           class_name: 'ChargeSchedule::Entry::Allocation',
           dependent: :destroy

  accepts_nested_attributes_for :allocations

  validates :charge_schedule, presence: true
  validates :account, presence: true
  validates :name, presence: true
  validates :amount_cents, presence: true
  validates :inherit_term, inclusion: { in: [true, false] }
  validates :start_date, presence: true
  validates :end_date, presence: true
  validate :exact_charge_amount_distribution

  # ChargeSchedule should only be blank during model specs testing associations.
  before_validation :set_default_dates, if: :charge_schedule

  scope :recurring, -> { where(recurring: true) }
  scope :one_time, -> { where(recurring: false) }
  scope :active, lambda { |as_of = Time.zone.today|
    where('? BETWEEN charge_schedule_entries.start_date AND ' \
          'charge_schedule_entries.end_date', as_of)
  }

  delegate :chargeable, to: :charge_schedule

  monetize :amount_cents

  amoeba do
    include_association :allocations
  end

  audited associated_with: :charge_schedule
  has_associated_audits

  def annual_amount
    Money.sum(allocations.map(&:annual_amount))
  end

  def term_amount
    Money.sum(allocations.map(&:term_amount))
  end

  def lease
    chargeable if chargeable.is_a?(Lease)
  end

  def start_date=(new_date)
    super(date_clamped_within_chargeable(new_date))
  end

  def end_date=(new_date)
    super(date_clamped_within_chargeable(new_date))
  end

  def proration
    if Customer.current_subdomain == 'marketplacehomes' && account_id.in?(
      [
        2735, 2736, 2737, 3284, 4751, # Resident Benefits
        3398, 3403, 3404, 3408, 4927  # Damage Waiver
      ]
    )
      return 'prorated_whole'
    end

    charge_schedule.proration
  end

  private

  def exact_charge_amount_distribution
    return if allocations.sum(&:amount_cents) == amount_cents

    errors.add(:allocations, 'must equal total charge amount')
  end

  def set_default_dates
    self.start_date ||= charge_schedule.start_date
    self.end_date ||= charge_schedule.end_date
  end

  def date_clamped_within_chargeable(date)
    return nil unless date

    date = Date.parse(date) if date.is_a?(String)

    return date unless chargeable.start_date && chargeable.end_date

    date.clamp(chargeable.start_date..chargeable.end_date)
  end
end
