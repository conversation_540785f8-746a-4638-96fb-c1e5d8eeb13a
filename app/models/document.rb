class Document < Attachment
  include Document::VerificationDocumentKinds
  include HasMetadata

  attr_accessor :override_content_type

  belongs_to :template, optional: true, class_name: 'Document'
  has_many :generated_attachments, class_name: 'Document',
                                   foreign_key: 'template_id',
                                   inverse_of: :template,
                                   dependent: :nullify
  has_many :agreement_types, class_name: 'Agreements::AgreementType',
                             inverse_of: :document_template,
                             foreign_key: :document_template_id,
                             dependent: :nullify

  scope :template_type, ->(type) { template_options(template_type: type) }

  scope :template_options, lambda { |options|
    where('template_options @> ?', options.to_json)
  }

  has_attached_file :upload, storage: :s3,
                             bucket: ENV.fetch('S3_UPLOADS_BUCKET', nil),
                             s3_region: 'us-east-1',
                             s3_server_side_encryption: 'AES256',
                             s3_protocol: 'https',
                             path: '/:root/:hash/:filename',
                             s3_headers: ->(attachment) { attachment.s3_headers },
                             hash_secret: \
                             Rails.application.secrets.secret_key_base

  do_not_validate_attachment_file_type :upload

  Paperclip.interpolates :root do |attachment, _style|
    document = attachment.instance
    klass = if document.parent
              document.parent.class.name.underscore
            else
              'document'
            end

    [
      ('development_uploads' if Rails.env.development?),
      ('test_uploads' if Rails.env.test?),
      Customer.current.subdomain,
      klass,
      document.id
    ].compact.join('/')
  end

  TEMPLATE_KINDS = {
    lease: 'Lease',
    agreement_template: 'Agreement',
    management_contract: 'Management Content',
    lease_renewal_form: 'Renewal Form',
    preferred_lease_renewal_form: 'Preferred Renewal Form',
    letter: 'Letter',
    payment_plan_agreement: 'Payment Plan Agreement',
    vendor_packet: 'Vendor Packet'
  }.freeze

  def use_count
    generated_attachments.count
  end

  def last_used_at
    generated_attachments.order(created_at: :desc).first&.created_at
  end

  def name
    upload_file_name
  end

  def s3_headers
    {
      'Content-Type' => override_content_type
    }
  end

  def direct_upload_url
    super || upload.url
  end
end
