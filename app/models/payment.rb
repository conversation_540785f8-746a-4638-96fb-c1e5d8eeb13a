class Payment < ApplicationRecord
  include AcceptsLegacyAttachments
  include CommercialDocument
  include HasInvoicePayments
  include Payment::ActsAsInvoiceVoid
  include Payment::ElectronicPayment
  include Payment::HasBankAccounts
  include Payment::HasJournalEntries
  include Payment::PayerPayeeDirtyTracking
  include Payment::PreventedByEvictions
  include Payment::Prepayment
  include Payment::Reconcilable
  include Payment::Reversable
  include Payment::Searchable
  include Payment::ApiV2Resource # Specs fail if this isn't last

  enum :kind, {
    check: 0,
    ach: 1,
    credit: 2,
    cash: 3,
    money_order: 4,
    credit_note: 5
    # TODO: mail_check: 6,
  }

  # @!attribute [rw] status
  enum :status, {
    completed: 0,
    pending: 1,
    processing: 2,
    failed: 3,
    canceled: 4
  }

  belongs_to :payer, polymorphic: true
  belongs_to :payee, polymorphic: true
  belongs_to :batch
  belongs_to :scheduled_payment, optional: true

  has_many :invoice_payments, dependent: :destroy,
                              inverse_of: :payment,
                              after_add: :on_invoice_payments_changed,
                              after_remove: :on_invoice_payments_changed
  has_many :invoices, through: :invoice_payments

  has_many :shrine_attachments, as: :parent, inverse_of: :parent,
                                dependent: :destroy

  validates :date, presence: true
  validates :amount, numericality: { greater_than: 0 }
  validates :payer, presence: true
  validates :payer, exclusion: {
    in: ->(payment) { [payment.payee] },
    message: 'must be different than payee'
  }, if: :payee
  validates :payer_type, inclusion: { in: Accounting::TRANSACTION_ENTITIES }
  validates :payee, presence: true
  validates :payee_type, inclusion: { in: Accounting::TRANSACTION_ENTITIES }
  validate :valid_payer_payee_pair?
  validate :all_invoices_approved

  alias_attribute :post_date, :date

  strip_attributes only: :check_number

  monetize :amount_cents
  monetize :convenience_fee_cents, allow_nil: true

  audited
  has_associated_audits

  include HasAuditedInvoicePayments

  PERMISSABLE_ATTRIBUTES = %i[
    amount date reversed_at description kind check_number note
    debit_bank_account_id credit_bank_account_id
    payable_credit_note_account_id receivable_credit_note_account_id
    payer_lease_membership_id payee_lease_membership_id
    payer_unit_id payee_unit_id
    loan_id
    audit_comment
  ].freeze

  def self.kind_options
    kind_titles = {
      'ach' => 'ACH',
      'credit' => 'Card Payment'
    }

    kinds.keys
         .without('credit_note')
         .map { |k| [kind_titles[k] || k.titleize, k] }
  end

  def to_s
    description.presence || 'Payment'
  end

  def url
    routes.accounting_payment_path(self)
  end

  def credit(date = nil)
    if date
      if reversed? && reversed_at <= date
        Money.zero
      elsif date && self.date > date
        Money.zero
      else
        ips = invoice_payments.reject do |ip|
          ip.reversed? && ip.reversed_at <= date
        end

        ips = ips.select do |ip|
          ip.date <= date
        end

        amount - Money.sum(ips.map(&:amount))
      end
    elsif reversed?
      Money.zero
    else
      amount - Money.sum(active_invoice_payments.map(&:amount))
    end
  end

  def available_credit?
    credit.positive?
  end

  def reference_number
    zeamster_transaction&.reference_number ||
      profit_stars_transaction&.reference_number ||
      pay_lease_transaction&.reference_number ||
      check_number
  end

  # Whether or not this payment was made by the customer.
  # Determines if the debit_bank_account can be adjusted.
  def from_customer?
    payer_type.in?(%w[Company Property])
  end

  # Assuming payer and payee are set (validated elsewhere), a {Payment} must be
  # associated to at least an entity or property; e.g. a {Vendor} cannot pay a
  # {Tenant}.
  def valid_payer_payee_pair?
    return true unless payer && payee

    return true if valid_target_pair?(payer, payee)

    errors.add(:base, 'At least Payer or Payee must be a Company or Property')

    false
  end

  def attachments
    shrine_attachments
  end

  # TODO: move / handle update
  def invoices=(invoices)
    remaining = amount

    invoices.each do |invoice|
      break unless remaining.positive?

      amount = [invoice.balance, remaining].min

      next unless amount.positive?

      invoice_payments.build(
        invoice: invoice,
        amount: amount,
        avoid_validates_associated_loop_from_payment: true
      )

      remaining -= amount
    end
  end

  def all_invoices_approved
    return if payee_type == 'Tenant' ||
              payer_type == 'Tenant' ||
              payer_type == 'Vendor'

    invoices = invoice_payments.map(&:invoice)
    return if invoices.none?(&:invoice_payment_unapproved?)

    errors.add(:base, 'Not all invoices are approved.')
  end

  def on_invoice_payments_changed(_)
    return unless Feature.enabled?(:prepayment, Customer.current)

    return if new_record?

    return if destroying

    self.invoice_payments_changed = true
    save!
  end

  def check_printable?
    check? && withdrawal_bank_account&.customer_managed?
  end

  delegate :currency, to: :amount
end
