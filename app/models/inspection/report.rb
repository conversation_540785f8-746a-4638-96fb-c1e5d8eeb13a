class Inspection::Report < ApplicationRecord
  DUE_DATE_TIME_ZONE = 'Eastern Time (US & Canada)'.freeze

  include Archivable
  include Inspection::Report::HasREAMSOrder
  include Inspection::Report::HasMercuryOrder
  include Inspection::Report::Inspectify
  include Inspection::Report::Searchable
  include Inspection::Report::SierraLeone
  include Inspection::Report::Status
  include Inspection::Report::ActivityLogging

  self.ignored_columns += %i[templates]

  enum :kind, {
    acquisition: 0,
    move_out: 1,
    move_in: 2,
    other: 3,
    rehab: 4,
    recurring: 5
  }

  NOT_IN_PROGRESS_STATUSES = %i[completed in_review canceled].freeze

  belongs_to :template
  belongs_to :property
  belongs_to :target, polymorphic: true
  belongs_to :task, inverse_of: :inspection

  belongs_to :opened_by, class_name: 'PropertyManager',
                         inverse_of: :inspection_reports

  belongs_to :assigned_to, polymorphic: true

  has_many :records, dependent: :destroy
  has_many :responses, through: :records
  has_many :punch_list_entries, through: :responses
  has_many :photos, through: :responses

  has_one :estimate, class_name: 'Maintenance::Estimate',
                     inverse_of: :inspection,
                     foreign_key: :inspection_id,
                     dependent: :restrict_with_error

  has_one :move_out, class_name: 'Lease::MoveOut',
                     inverse_of: :inspection,
                     foreign_key: :inspection_id,
                     dependent: :nullify

  validates :name, presence: true
  validates :kind, presence: true
  validates :status, presence: true
  validates :property, presence: true
  validates :template, presence: true
  validates :target, presence: true

  validate :future_earliest_start_date
  validate :future_due_date
  validates :due_date, comparison: {
                         greater_than_or_equal_to: :earliest_start_date,
                         message: 'can\'t be earlier than start date'
                       },
                       allow_blank: true,
                       if: :earliest_start_date?

  before_validation :force_name,
                    if: -> { Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]) }
  before_save :auto_transition_to_requested, if: :assigned_to_changed?
  after_save :notify_property_manager_assignment, if: :assigned_to_previously_changed?

  scope :incomplete, -> { where(completed_at: nil) }
  scope :pending, -> { unarchived.where(completed_at: nil) }
  scope :complete, -> { where.not(completed_at: nil) }

  # Assuming due_date is Eastern Time
  scope :overdue, lambda {
    cutoff = ActiveSupport::TimeZone[DUE_DATE_TIME_ZONE].today

    pending.not_canceled.where(due_date: ...cutoff)
  }

  scope :assigned, -> { where.not(assigned_to: nil) }

  scope :unassigned, -> { where(assigned_to: nil) }

  audited
  has_associated_audits

  delegate :reams_id, to: :reams_order, allow_nil: true

  alias service_area property

  def to_s
    "Inspection of #{service_area.name}"
  end

  def due_at
    created_at + 5.days
  end

  def assigned_to_inspection_partner?
    active_inspectify_order.present? || sierra_leone_inspection_order.present?
  end

  def case_number
    return unless reams_order

    reams_order.case_number
  end

  def overdue?
    return false if due_date.blank? || inactive?

    cutoff = due_date.in_time_zone(DUE_DATE_TIME_ZONE).end_of_day

    cutoff.past?
  end

  def in_progress?
    NOT_IN_PROGRESS_STATUSES.exclude?(status.to_sym) && unarchived?
  end

  def inactive?
    completed? || canceled? || archived?
  end

  def url
    "/operations/inspections/#{id}"
  end

  def as_json(options = {})
    super.merge(
      reams_id:
    )
  end

  private

  def auto_transition_to_requested
    return unless assigned_to.present? && may_request?

    request
  end

  def assigned_to_changed?
    assigned_to_id_changed? || assigned_to_type_changed?
  end

  def assigned_to_previously_changed?
    assigned_to_id_previously_changed? || assigned_to_type_previously_changed?
  end

  def force_name
    prefix = case kind
             when 'move_in', 'move_out', 'rehab'
               kind.titleize.tr(' ', '-')
             else
               'Inspect'
             end

    self.name = [prefix, target&.name].compact.join(' ')
  end

  def future_earliest_start_date
    return unless earliest_start_date.present? && earliest_start_date_changed?

    errors.add(:base, 'Start date must be in the future') if earliest_start_date.past?
  end

  def future_due_date
    return unless due_date.present? && due_date_changed?

    errors.add(:due_date, 'must be in the future') if due_date.past?
  end

  def notify_property_manager_assignment
    return unless assigned_to.is_a?(PropertyManager)

    assigned_to.notifications.create!(
      resource: self,
      kind: :inspection_assignment,
      title: 'You were assigned to an inspection',
      description: name,
      link: Rails.application.routes.url_helpers.operations_inspection_url(
        self, subdomain: Customer.current_subdomain
      )
    )
  end
end
