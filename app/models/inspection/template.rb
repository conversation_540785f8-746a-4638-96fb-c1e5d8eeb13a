class Inspection::Template < ApplicationRecord
  include Archivable

  validates :name, presence: true

  has_many :questions, dependent: :destroy

  has_one :reams_configuration, class_name: 'REAMS::Configuration',
                                inverse_of: :inspection_template,
                                foreign_key: :inspection_template_id,
                                dependent: :restrict_with_error

  has_one :mercury_configuration, class_name: 'Mercury::Configuration',
                                  inverse_of: :inspection_template,
                                  foreign_key: :inspection_template_id,
                                  dependent: :restrict_with_error

  def references_property?
    questions.exists?(category: :property)
  end

  def references_units?
    questions.exists?(category: %i[unit bedroom bathroom])
  end
end
