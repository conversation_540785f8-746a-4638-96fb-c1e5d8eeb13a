class Tunisia::DebitCard < ApplicationRecord
  belongs_to :deposit_account, class_name: 'Tunisia::DepositAccount', optional: false

  validates :tunisia_card_id, presence: true, uniqueness: true
  validates :last_four, presence: true
  validates :expiration, presence: true
  validates :status, presence: true
  validates :card_created_at, presence: true

  audited on: [:update, :destroy]

  def expiration=(value)
    parsed_value = case value
                   when nil then nil
                   when Date then value.end_of_month
                   when String
                     Date.strptime(value, '%Y-%m')&.end_of_month
                   else
                     fail ArgumentError
                   end

    super(parsed_value)
  end
end
