# See the SFTP To Go API documentation at:
# https://sftptogo.com/docs/automation/using-webhook-notifications-to-trigger-processes
# for more information.

class SFTPToGoController < ApplicationController
  include SFTPToGo::Support

  skip_before_action :verify_authenticity_token
  before_action :verify_signature

  def webhooks
    event = JSON.parse(raw_request_body).deep_symbolize_keys

    (handler_for(event) || noop_for(event)).call

    head :no_content
  end

  private

  def handler_for(event) # rubocop:disable Metrics/MethodLength
    case event
    in { Topic: 'file.created', Data: { Path: encoded_remote_file_path } }
      remote_file_path = URI.decode_www_form_component(encoded_remote_file_path)

      case remote_file_path
      in %r{imports/member_directory/(.+)\.(csv|xlsx)$}
        lambda {
          SFTPToGoImportJob.perform_later(
            import_class_name: Importers::V3::MemberDirectory.name,
            remote_file_path:,
            options: {
              send_invites: false
            }
          )
        }
      in %r{imports/employee_directory/(.+)\.(csv|xlsx)$}
        lambda {
          SFTPToGoImportJob.perform_later(
            import_class_name: Importers::V3::EmployeeDirectory.name,
            remote_file_path:
          )
        }
      in %r{imports/alliance_transactions/.*?/(.+)\.(csv|xlsx)$}
        lambda {
          SFTPToGoImportJob.perform_later(
            import_class_name: Importers::V3::AllianceTransactions.name,
            remote_file_path:,
            options: {
              customer_subdomain: remote_file_path.match(%r{alliance_transactions/([^/]+)})[1]
            }
          )
        }
      in %r{(.+/inspection_orders/completed/[^.]+)\.xml$}
        lambda {
          SierraLeone::Inspection::Orders::Completed::ImportJob.perform_later(
            remote_file_slug: ::Regexp.last_match(1)
          )
        }
      else
        nil
      end
    else
      nil
    end
  end

  def noop_for(event)
    lambda {
      Rails.logger
           .error "No job to process SFTPToGo #{event.fetch(:Topic,
                                                            'unknown')} webhook #{event.inspect}"

      JobsMailer.notify(
        subject: 'SFTP Processing Unsuccesful',
        body: "No job found to process the file uploaded to: #{event.dig(:Data, :Path)}"
      ).deliver_later
    }
  end

  def verify_signature
    signature = request.headers['X-Hub-Signature']
    body = raw_request_body

    return if sftp_webhook_signature_valid?(signature, body)

    head :unauthorized
  end

  def raw_request_body
    @raw_request_body ||= request.body.read
  end
end
