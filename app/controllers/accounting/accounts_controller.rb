##
# Shows the contents of a {Plutus::Account} when looking at the individual
# accounts of a journal.
class Accounting::AccountsController < AccountingController
  before_action :set_journal
  around_action :in_basis, only: :show
  before_action :transform_filters, only: :show
  before_action :set_account, except: :index
  before_action :set_balance_date, only: :show
  before_action :set_balance, only: :show
  before_action :set_amounts, only: :show

  def index
    respond_to do |format|
      format.json do
        query = Plutus::AccountsQuery.new(@journal.accounts).search

        query = query.invoiceable if params[:invoiceable]

        render json: query
      end
    end
  end

  def show
    respond_to do |format|
      format.html { show_html }
      format.xlsx { show_xlsx }
    end
  end

  private

  def set_journal
    @journal = Company.find(params[:journal_id])
  end

  def set_account
    @account = Plutus::AccountsQuery
               .new(@journal.accounts).search
               .find(params[:id])
  end

  # Transform params coming in from accounting reports
  def transform_filters
    return if params[:filters].blank?

    params[:filters][:start_date] ||= params[:filters][:from_date]
    params[:filters][:end_date] ||= params[:filters][:to_date]
  end

  def in_basis(&)
    if params.dig(:filters, :basis) == 'cash'
      Plutus.in_cash_basis(journal_cache_hint: @journal, &)
    else
      yield
    end
  end

  def set_balance_date
    @balance_date = if (value = params.dig(:filters, :end_date).presence)
                      Date.parse(value)
                    else
                      Time.zone.today
                    end
  end

  def set_balance
    amounts = Plutus::AmountsQuery
              .new.search
              .where(account: @account)
              .joins(:entry)
              .merge(entry_filter)
              .merge(Plutus.entry_class.where(date: ..@balance_date))

    @balance = Money.new(
      amounts.debits.sum(:amount) - amounts.credits.sum(:amount)
    )

    @balance *= -1 if @account.credit_normal?
  end

  def entry_filter
    @entry_filter ||= begin
      entries = Plutus::EntriesQuery
                .new.search
                .where(journal: @journal)
                .reorder(date: :desc, id: :desc)

      if params.dig(:filters, :skip_equity_transfers)
        entries = entries.where.not(
          id: Plutus::EntriesQuery.new.search.equity_transfers(
            params.dig(:filters, :skip_equity_transfers)
          )
        )
      end

      entries
    end
  end

  def set_amounts
    # TODO: Currently Using join/preload instead of includes/references here because LEFT OUTER JOIN
    # doesn't work well with the current cash basis implementation, but INNER JOIN does.
    @amounts = Plutus::AmountsQuery
               .new.search
               .where(account: @account)
               .joins(:entry)
               .preload(:entry)
               .merge(Plutus.entry_class.order(date: :desc, id: :desc))
               .where(entry: entry_filter)
               .order_with(params[:sort])
               .filter_with(params[:filters])
  end

  def show_html
    @amounts = @amounts.max_paginated(params[:page], 12)
  end

  def show_xlsx
    amounts = @amounts.limit(1000)

    render xlsx: ActionTable::Accounting::Amounts.new(self, amounts)
  end
end
