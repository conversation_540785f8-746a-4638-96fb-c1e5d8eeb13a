class Accounting::PaymentsController < AccountingController
  before_action :set_payment, except: %i[index new create payers apply_ledger]
  before_action :defer_flash_notices, only: %i[index show destroy]

  decorates_assigned :payment

  # Backs a filterable list of {Payment}s
  def index
    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def new
    @payment = Payment.new

    respond_to do |format|
      format.html { assign_new_payment_defaults }
      format.js do
        hydrate_payment
        render 'update_form', format: :js, layout: nil
      end
    end
  end

  def create
    @payment = Payment.new(payment_params)

    hydrate_payment

    @payment.attachment_ids = params[:attachment_ids]
    @payment.description = 'Payment' if @payment.description.blank?

    if params.dig(:payment, :selected_invoice_ids)
      invoices = Invoice.order(post_date: :asc)
                        .where(id: params.dig(:payment, :selected_invoice_ids))
      @payment.invoices = invoices
    end

    respond_to do |format|
      format.js do
        if @payment.save
          flash[:success] = if @payment.credit_note?
                              'Credit Note Created Successfully'
                            else
                              'Payment Created Successfully'
                            end

          if params[:new]
            options = if @payment.credit_note?
                        { payment_kind: :credit_note }
                      else
                        {}
                      end

            redirect_to new_accounting_payment_path(options)
          else
            redirect_to accounting_payment_path(@payment)
          end
        else
          render_ujs_errors @payment.errors.full_messages
        end
      end
    end
  end

  def show; end

  def edit
    respond_to do |format|
      format.html
      format.js do
        hydrate_payment

        render 'update_form', format: :js, layout: nil
      end
    end
  end

  def update
    hydrate_payment

    @payment.attachment_ids = params[:attachment_ids]

    respond_to do |format|
      format.js do
        if @payment.save
          flash[:success] = 'Payment Updated Successfully'
          redirect_to accounting_payment_path(@payment)
        else
          render_ujs_errors @payment.errors.full_messages
        end
      end
    end
  end

  def destroy
    if @payment.destroy
      flash[:success] = 'Payment Removed Successfully'
      redirect_to accounting_payments_path
    else
      flash.now[:error] = @payment.errors.full_messages
      render :show, status: :unprocessable_entity
    end
  end

  def payers
    tenants = TenantsQuery.for_user(current_property_manager).map do |tenant|
      { id: "Tenant-#{tenant.id}", name: tenant.name }
    end

    properties = current_property_manager.properties.map do |property|
      { id: "Property-#{property.id}", name: property.name }
    end

    vendors = Vendor.all.map do |vendor|
      { id: "Vendor-#{vendor.id}", name: vendor.name }
    end

    companies = Company.all.map do |company|
      { id: "Company-#{company.id}", name: company.name }
    end

    render json: tenants + properties + vendors + companies
  end

  def void
    result = Payment::Void.call(@payment)

    if result.successful?
      flash[:success] = 'Payment Voided Successfully'
    else
      flash[:error] = result.errors
    end

    redirect_to accounting_payment_path(@payment)
  end

  def refund
    result = Payment::Refund.call(@payment)

    if result.successful?
      flash[:success] = 'Payment Refunded Successfully'
    else
      flash[:error] = result.errors
    end

    redirect_to accounting_payment_path(@payment)
  end

  def check
    authorize! to: :print_checks?, with: CheckPrintingPolicy

    @check = Accounting::Check.new(payment: @payment)
  end

  def apply_ledger
    @payment = Payment.new
    hydrate_payment
    render 'update_apply_ledger', format: :js, layout: nil
  end

  private

  def set_payment
    @payment = payments.find(params[:id])
  end

  def assign_new_payment_defaults
    @payment.date = Time.zone.today
    @payment.kind = params[:payment_kind] if params[:payment_kind].present?

    assign_new_payment_defaults_tenant if params[:tenant_id].present?

    assign_new_payment_defaults_loan if params[:loan_id].present?
  end

  def assign_new_payment_defaults_tenant
    tenant = Tenant.find(params[:tenant_id])

    @payment.payer = tenant

    membership = tenant.current_lease_membership ||
                 tenant.lease_memberships.unarchived.last

    return unless membership

    @payment.payee = membership.property
    @payment.payee_unit = membership.unit
    @payment.payer_lease_membership = membership

    @payment.credit_bank_account = \
      BankAccountsQuery.new.search.default_rent_deposit_account(membership)
  end

  def assign_new_payment_defaults_loan
    loan = Lending::Loan.find(params[:loan_id])

    @payment.payer = loan.property
    @payment.payee = loan.lender
    @payment.loan = loan
  end

  def hydrate_payment
    @payment.assign_attributes(payment_params)
    @payment.payer = locate_payer
    @payment.payee = locate_payee

    if (membership = @payment.payer_lease_membership)
      @payment.payee = membership.property
      @payment.payee_unit = membership.unit
    elsif (membership = @payment.payee_lease_membership)
      @payment.payer = membership.property
      @payment.payer_unit = membership.unit
    end
  end

  def index_html
    @payments = filtered_payments.page(params[:page]).per(12).decorate
  end

  def index_xlsx
    send_data workbook.stream.string,
              filename: workbook_filename,
              type: :xlsx
  end

  def filtered_payments
    payments
      .preload(
        :payer, :payee,
        :zeamster_transaction, :pay_lease_transaction, :profit_stars_transaction
      )
      .filter_with(params[:filters])
      .order_with(params[:sort])
  end

  def payments
    PaymentsQuery.for_user(current_property_manager)
  end

  def workbook
    payments = filtered_payments.limit(1000)

    PaymentsIndexExporter.new(payments).export
  end

  def workbook_filename
    "payments_#{Time.zone.now.to_fs(:export)}.xlsx"
  end

  def payment_params
    params.require(:payment).permit(Payment::PERMISSABLE_ATTRIBUTES)
  end

  def locate_payer
    GlobalID::Locator.locate_signed(params.dig(:payment, :payer))
  end

  def locate_payee
    GlobalID::Locator.locate_signed(params.dig(:payment, :payee))
  end

  def defer_flash_notices
    @noport = true
    @defer_flash_notices = true
  end
end
