class Accounting::Payables::PaymentsController < AccountingController
  helper_method :maximum_invoice_count

  before_action :transform_updated_params, :require_overdraft_acknowledgement, only: :create

  def new
    @payment = Payment.new
  end

  def safe_to_create
    render json: { safe_to_create: safe_to_create? }
  end

  def create
    result = Accounting::CreatePayablePayment.call(
      params, invoices, accounting_context, current_property_manager
    )

    @payment = result.payment

    if result.successful?
      flash[:success] = 'Payment Successfully Created'
      redirect_to_payment(@payment)
    else
      flash[:error] = result.errors
      render :new, status: :unprocessable_entity
    end
  end

  def payees
    vendors = VendorsQuery.for_user(current_property_manager)
                          .includes(:bank_account_payment_methods)

    companies = CompaniesQuery.for_user(current_property_manager)
                              .customer_managed
                              .includes(:bank_account_payment_methods)

    owner_properties = Property.joins(:company)
                               .where(company: companies)
                               .includes(company: :bank_account_payment_methods)

    if Customer.current_subdomain == 'adpi'
      companies += adpi_specific_company_modification(companies)
    end

    tenants = TenantsQuery.for_user(current_property_manager)
                          .includes(:bank_account_payment_methods)

    payees = vendors + companies + tenants + owner_properties

    data = payees.sort_by(&:name).map do |payee|
      {
        id: payee.to_global_id.to_s,
        name: payee.name,
        ach_available: payee.electronic_deposits_available?
      }
    end

    render json: data
  end

  private

  def accounting_context
    # TODO
    (invoices.first&.buyer || Property.first).accounting_context
  end

  def payee
    GlobalID::Locator.locate(params[:payee_gid])
  end

  def invoices
    Invoice
      .where(seller: payee)
      .unpaid
      .where(id: params[:invoice_ids].split(','))
      .limit(maximum_invoice_count)
  end

  def redirect_to_payment(payment)
    if params[:new]
      redirect_to new_accounting_payables_payment_path
    else
      redirect_to accounting_payment_path(payment)
    end
  end

  def maximum_invoice_count
    25 if Customer.current_subdomain.start_with?('mph', 'marketplacehomes')
  end

  def adpi_specific_company_modification(companies)
    company_ids = companies.pluck(:id)

    chapter_entity_id_to_hc_id = {
      2 => 158,
      4 => 159,
      18 => 160,
      21 => 164,
      58 => 161,
      140 => 162,
      155 => 165,
      156 => 180
    }

    list = []

    chapter_entity_id_to_hc_id.each do |chapter_entity_id, hc_id|
      if company_ids.include?(chapter_entity_id) && company_ids.exclude?(hc_id)
        list << Company.find(hc_id)
      end
    end

    list << Company.find(1) unless company_ids.include?(1)

    list
  end

  def bank_account
    BankAccount.find_by(id: params[:bank_account_id])
  end

  def safe_to_create?
    return true unless params.dig(:payment, :kind) == 'ach'
    return true unless bank_account.tunisia_account?

    amount = params.dig(:payment, :amount).to_money
    return false if bank_account.tunisia_deposit_account.available < amount

    true
  end

  def transform_updated_params
    return unless params[:updated_form] == 'true'

    payer_sgid = params[:payment].delete(:payer)
    payee_sgid = params[:payment].delete(:payee)

    _payer = GlobalID::Locator.locate_signed(payer_sgid)
    payee = GlobalID::Locator.locate_signed(payee_sgid)

    params[:bank_account_id] = params[:payment][:debit_bank_account_id]
    params[:payee_gid] = payee&.to_global_id.to_s
  end

  def require_overdraft_acknowledgement
    return unless params[:payment]&.key?(:overdraft_acknowledgement)

    acknowledgement = params[:payment][:overdraft_acknowledgement]

    return render_ujs_errors 'Overdraft acknowledgement cannot be blank.' if acknowledgement.blank?

    params[:payment][:audit_comment] = "Overdraft Acknowledgement: #{acknowledgement}"
  end
end
