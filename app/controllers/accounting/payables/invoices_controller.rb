class Accounting::Payables::InvoicesController < Accounting::InvoicesController
  before_action(only: %i[batch_approve batch_add_to_batch]) do
    set_batched_invoices
  end

  before_action :set_insertion_payment_batch, only: %i[batch batch_add_to_batch]

  skip_before_action :set_invoice, only: %i[batch_approve batch_add_to_batch]

  # TODO: Remove and use superclass method
  def index_json
    seller = if params[:vendor_id]
               Vendor.find_by(id: params[:vendor_id])
             else
               GlobalID::Locator.locate(params[:payee_gid])
             end

    @invoices = InvoicesQuery.new.search
                             .by_user(current_property_manager).where(seller: seller)
                             .unpaid
                             .approved
                             .preload(:buyer, :seller)
                             .includes(:invoice_total_payment, :line_items)

    if request.referer.include?('accounting/payables/payments')
      @invoices = @invoices.limit(maximum_invoice_count)
    end

    render json: @invoices
  end

  def create
    result = Accounting::CreatePayable.call(params: params)

    @invoice = result.invoice

    if result.successful?
      if params[:maintenance_ticket_id].present?
        redirect_to maintenance_ticket_path(params[:maintenance_ticket_id])
      else
        flash[:success] = 'Invoice Recorded Successfully'
        redirect_to_invoice(result.invoice)
      end
    elsif params[:maintenance_ticket_id].present?
      flash[:error] = result.errors
      redirect_to maintenance_ticket_path(params[:maintenance_ticket_id])
    else
      flash.now[:error] = result.errors
      render :new, status: :unprocessable_entity
    end
  end

  def show
    super

    respond_to do |format|
      format.html
      format.json { render json: serialize(@invoice).as_json }
    end
  end

  def forward; end

  def approve
    res = Approvals::Approve.call \
      @invoice, :invoice_payment, current_property_manager

    if res.successful?
      flash[:success] = 'Invoice has been approved'
    else
      flash[:error] = res.errors
    end

    redirect_to_invoice(@invoice)
  end

  def unapprove
    res = Approvals::Unapprove.call \
      @invoice, :invoice_payment, current_property_manager

    if res.successful?
      flash[:success] = 'Invoice has been unapproved'
    else
      flash[:error] = res.errors
    end

    redirect_to_invoice(@invoice)
  end

  def batch
    @insertion_payment_batch.add_invoice!(@invoice)
    flash[:success] = 'Invoice Added to Batch'
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = e.record.errors.full_messages
  ensure
    redirect_to_invoice(@invoice)
  end

  def unbatch
    if @invoice.update(payment_batch: nil, payment_batch_order: 0)
      flash[:success] = 'Invoice Removed from Batch'
    else
      flash[:error] = @invoice.errors.full_messages
    end

    redirect_to_invoice(@invoice)
  end

  def batch_approve
    result = Approvals::BulkApprove.call(
      @batched_invoices,
      :invoice_payment,
      current_property_manager
    )

    if result.successful?
      flash[:success] = Approvals::BulkApprove.message(result, 'Invoice')
    else
      flash[:error] = result.errors
    end

    redirect_back fallback_location: index_path
  end

  def batch_add_to_batch
    @batched_invoices = @batched_invoices.includes(
      :line_items, :journal_entries, :invoice_payments, :payment_batch
    )

    ActiveRecord::Base.transaction do
      @batched_invoices.each do |invoice|
        @insertion_payment_batch.add_invoice!(invoice)
      end
    end

    flash[:success] = "#{@batched_invoices.size}
                       #{'Invoice'.pluralize(@batched_invoices.size)}
                       Successfully Added to Batch '#{@insertion_payment_batch.name}'"
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = e.record.errors.full_messages
  ensure
    redirect_back fallback_location: index_path
  end

  def owner_visibility
    if @invoice.update(owner_visible: params[:visible])
      flash[:success] = if @invoice.owner_visible?
                          'Invoice Added to Owner Portal'
                        else
                          'Invoice Removed from Owner Portal'
                        end
    else
      flash[:error] = @invoice.errors.full_messages
    end

    redirect_to_invoice(@invoice)
  end

  def pdf_attachment
    send_file Down.download(@invoice.pdf_attachment.direct_upload_url)
  end

  private

  def set_insertion_payment_batch
    @insertion_payment_batch = Payment::BatchesQuery.new
                                                    .search
                                                    .insertable_by(current_property_manager)
                                                    .find(params[:payment_batch_id])
  end

  def invoices
    InvoicesQuery
      .new.search
      .payables_by_user(current_property_manager)
      .includes(:invoice_total_payment)
      .preload(:buyer, :seller)
      .filter_with(params[:filters])
      .order_with(params[:sort])
      .order(physical_date: :desc, id: :desc)
      .distinct
  end

  def index_path
    accounting_payables_path
  end

  def redirect_to_invoice(invoice)
    if params[:new]
      redirect_to new_accounting_payables_invoice_path
    elsif params[:copy]
      redirect_to new_accounting_payables_invoice_path(copy_id: invoice.id)
    else
      redirect_to accounting_payables_invoice_path(invoice)
    end
  end

  def accounting_side
    :payables
  end

  def prepare_default_invoice
    super

    if params[:seller_sgid].present?
      @invoice.seller = GlobalID::Locator.locate_signed(params[:seller_sgid])
    end

    if Customer.current.single_family_management?
      @invoice.buyer = Customer.current.client_entity
    end

    if (net_days = CustomerSpecific::Behavior.payable_invoice_default_net_days) # rubocop:disable Style/GuardClause
      @invoice.due_date = Time.zone.today + net_days.days
    end
  end

  def maximum_invoice_count
    25 if Customer.current_subdomain.start_with?('mph', 'marketplacehomes')
  end
end
