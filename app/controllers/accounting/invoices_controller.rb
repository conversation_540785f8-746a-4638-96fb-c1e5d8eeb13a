##
# A base class for defining commonality between the payables and receivables
# InvoicesControllers
class Accounting::InvoicesController < AccountingController
  before_action :set_invoice, except: %i[create index new batch_destroy batch_download]
  before_action :set_batched_invoices, only: %i[batch_destroy batch_download]
  before_action :defer_flash_notices, only: %i[index show destroy]

  helper_method :accounting_side

  def index
    respond_to do |format|
      format.html { index_html }
      format.json { index_json }
      format.xlsx { index_xlsx }
    end
  end

  def index_html
    @invoices = invoices

    # Hide invoice forwards in indexes
    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
      @invoices = @invoices.where(forwarded_from_id: nil)
    end

    @invoices = @invoices.max_paginated(params[:page], 12).decorate
  end

  def index_xlsx
    send_data workbook.stream.string,
              filename: workbook_filename,
              type: :xlsx
  end

  def show; end

  def edit
    respond_to do |format|
      format.html
      format.js { update_form }
    end
  end

  def new
    respond_to do |format|
      format.html { prepare_new_invoice }
      format.js { update_form }
    end
  end

  def update
    ActiveRecord::Base.transaction do
      @invoice
        .shrine_attachments
        .select { |a| a.id.to_s.in?(params[:remove_shrine_attachment_ids]) }
        .each(&:mark_for_destruction)

      @invoice.attachment_ids = params[:attachment_ids]

      @invoice.update!(edit_invoice_params)

      Invoice::Forward.call(@invoice)

      flash[:success] = 'Invoice Updated Successfully'
      redirect_to_invoice(@invoice)
    end
  rescue ActiveRecord::RecordInvalid => e
    flash.now[:error] = e.record.errors.full_messages
    render :edit, status: :unprocessable_entity
  end

  def destroy
    if @invoice.destroy
      flash[:success] = 'Invoice removed successfully'
      redirect_to_index
    else
      flash.now[:error] = @invoice.errors.full_messages
      render :show, status: :unprocessable_entity
    end
  end

  def batch_destroy
    @batched_invoices = @batched_invoices.includes(
      :invoice_payments,
      :shrine_attachments,
      :billing,
      :billing_submitted_maintenance_ticket_events,
      :linkages,
      :invoice_processing_email,
      :payment_batch,
      :approval_requests, :approvals, :rejections,
      line_items: %i[sourcing_markup sourced_markup labor_item],
      journal_entries: %i[journal credit_amounts debit_amounts]
    )

    ActiveRecord::Base.transaction do
      @batched_invoices.each(&:destroy!)

      flash[:success] = "#{@batched_invoices.size}
                         #{'Invoice'.pluralize(@batched_invoices.size)}
                         Removed Successfully"
    end
  rescue ActiveRecord::RecordNotDestroyed => e
    flash[:error] = e.record.errors.full_messages
  ensure
    redirect_back fallback_location: index_path
  end

  def batch_download
    task_id = SecureRandom.hex

    Downloads::DownloadInvoicesJob.set(wait: 0.2.seconds).perform_later(
      user: current_property_manager,
      download_id: task_id,
      invoice_ids: @batched_invoices.map(&:id)
    )

    follow_background_task(task_id: task_id)
  end

  def mark_paid
    receivable = request.path.include?('receivables')
    result = Invoice::MarkPaid.call(invoice: @invoice,
                                    params: params,
                                    receivable: receivable)

    if result.successful?
      flash[:success] = 'Payment Recorded Successfully'
    else
      flash[:error] = result.errors
    end

    redirect_to_invoice(@invoice)
  end

  def void
    result = Invoice::Void.call(
      invoice: @invoice,
      receivable: controller_path.include?('receivables')
    )

    if result.successful?
      flash[:success] = 'Invoice voided successfully'
    else
      flash[:error] = result.errors
    end

    redirect_to_invoice(@invoice)
  end

  protected

  def invoices
    fail NotImplementedError
  end

  def redirect_to_index
    redirect_to index_path
  end

  private

  def prepare_new_invoice
    if params[:copy_id].present?
      prepare_copied_invoice
    else
      prepare_default_invoice
    end
  end

  def prepare_default_invoice
    @invoice = Invoice.new(post_date: Time.zone.today,
                           physical_date: Time.zone.today,
                           due_date: 30.days.from_now.to_date,
                           invoice_number: '')

    @invoice.line_items.build
  end

  def prepare_copied_invoice
    copy_from = Invoice.find(params[:copy_id])
    @invoice = copy_from.amoeba_dup
  end

  def update_form
    @invoice = Invoice.new if @invoice.blank?
    @invoice.assign_attributes(edit_invoice_params)

    if (membership = @invoice.buyer_lease_membership)
      @invoice.seller = membership.property
      @invoice.seller_unit = nil
      @invoice.buyer_unit = membership.unit
    elsif (membership = @invoice.seller_lease_membership)
      @invoice.buyer = membership.property
      @invoice.buyer_unit = nil
      @invoice.seller_unit = membership.unit
    end

    render 'accounting/shared/invoices/update_form', format: :js, layout: nil
  end

  def workbook
    RubyXL::Workbook.new.tap do |book|
      sheet = book[0]

      # Header
      sheet.change_row_bold(0, true)
      columns = %w[Date Due To From Invoice Description Amount Balance]
      columns.each.with_index do |value, x|
        sheet.add_cell(0, x, value)
      end

      min_width = 12
      max_width = 36
      maxes = Array.new(columns.length, min_width)

      # Cannot use find_each with date sort; sort after
      workbook_rows.sort_by { |row| row[0] }.reverse.each.with_index do |row, y|
        row.each.with_index do |value, x|
          sheet.add_cell(y + 1, x, value)
          len = value.to_s.length
          maxes[x] = len if len > maxes[x]
        end
      end

      maxes.each.with_index do |width, x|
        sheet.change_column_width(x, [width, max_width].min)
      end
    end
  end

  def workbook_rows
    invoices.limit(1000).find_each.map do |invoice|
      [
        invoice.physical_date.to_s,
        invoice.due_date.to_s,
        invoice.buyer.name,
        invoice.seller.name,
        invoice.invoice_number,
        invoice.description,
        invoice.amount.to_f,
        invoice.balance.to_f
      ]
    end
  end

  def workbook_filename
    name = self.class.name.demodulize.underscore.split('_').first
    "#{name}_#{Time.zone.now.to_fs(:export)}.xlsx"
  end

  def edit_invoice_params
    params.require(:invoice)
          .permit(:buyer, :seller, *Invoice::PERMISSABLE_ATTRIBUTES)
          .tap do |permitted_params|
            if permitted_params[:buyer]
              permitted_params[:buyer_sgid] = permitted_params.delete(:buyer)
            end

            if permitted_params[:seller]
              permitted_params[:seller_sgid] = permitted_params.delete(:seller)
            end
          end
  end

  def set_invoice
    @invoice = Invoice.includes(
      :journal_entries, line_items: %i[payable_account receivable_account]
    ).find(params[:id]).decorate
  end

  def set_batched_invoices
    @batched_invoices = if params[:overselection] == 'true'
                          invoices
                        else
                          invoices.where(id: params[:selected_ids])
                        end
  end

  def defer_flash_notices
    @noport = true
    @defer_flash_notices = true
  end
end
