class Accounting::InvoiceTargetsController < AccountingController
  respond_to :json

  # TODO: Refactor with query for user

  def payable
    companies = Company.all
    properties = Property.all.includes(:company)
    items = companies + properties
    render json: serialize(items)
  end

  def receivable
    lease_memberships = LeaseMembership.all.includes(:tenant, lease: [unit: [property: [:company]]])
    companies = Company.all
    vendors = VendorsQuery.for_user(current_property_manager).none
    applicants = LeaseApplicationMembershipsQuery
                 .for_user(current_property_manager)
                 .unarchived
                 .submitted
                 .includes(:tenant, lease_application: :property)
    prospects = GuestCard.unarchived.where.not(property_id: nil)
    items = lease_memberships + companies + vendors + applicants + prospects
    render json: serialize(items)
  end

  def targets
    query = params[:q]
    scoped = params[:scoped]
    query_generator = if scoped
                        Searchable::Query::ScopedAccess
                      else
                        Searchable::Query::ScopedAccessOnlyTenants
                      end
    results = Searchable.search_all(query,
                                    [Company, Property, Vendor, Tenant],
                                    scope_to: current_property_manager,
                                    query_generator:)

    render json: {
      results: results.group_by(&:class).to_h do |klass, records|
        [
          klass.name.tableize,
          {
            name: klass.name.humanize,
            results: records.filter_map do |record|
              next if record.is_a?(Archivable) && record.archived?

              journal_id = case record.class.name
                           when 'Company'
                             record.id
                           when 'Property'
                             record.company_id
                           end

              description = if record.is_a?(Property)
                              record.company.name
                            else
                              klass.name
                            end

              {
                title: record.to_s,
                description: description,
                sgid: record.to_sgid.to_s,
                journal_id: journal_id
              }.compact
            end
          }
        ]
      end
    }
  end

  private

  def serialize(array)
    array.map do |item|
      item.accounting_context.as_json
    end
  end
end
