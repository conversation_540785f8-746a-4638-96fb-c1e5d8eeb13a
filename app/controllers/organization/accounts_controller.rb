class Organization::AccountsController < OrganizationController
  before_action :set_chart, except: :index
  around_action :in_basis, only: :show
  before_action :set_account, only: %i[destroy edit show update]

  def index
    render json: accounts
  end

  def show
    @account = AccountDecorator.decorate(@account)

    # TODO: Currently Using join/preload instead of includes/references here because LEFT OUTER JOIN
    # doesn't work well with the current cash basis implementation, but INNER JOIN does.
    @amounts = Plutus::AmountsQuery
               .new.search
               .where(account: @account)
               .joins(entry: :journal)
               .preload(entry: :journal)
               .merge(Plutus.entry_class.reorder(date: :desc, id: :desc))
               .merge(Plutus.entry_basis_filter)
               .order_with(params[:sort])
               .filter_with(params[:filters])

    respond_to do |format|
      format.html do
        @amounts = @amounts.max_paginated(params[:page], 12)
      end
      format.xlsx do
        amounts = @amounts.limit(1000)

        render xlsx: ActionTable::Accounting::Amounts.new(self, amounts)
      end
    end
  end

  def new
    @account = @chart.accounts.build
  end

  def create
    @account = @chart.accounts.build(account_params)

    respond_to do |format|
      format.html do
        if @account.save
          flash[:success] = 'Account Created Successfully'
          redirect_to [:organization, @chart, plutus_account]
        else
          flash[:error] = @account.errors.full_messages
          render :new
        end
      end

      format.js do
        if @account.save
          render :remote_created
        else
          render_ujs_errors @account.errors.full_messages
        end
      end
    end
  end

  def edit; end

  def update
    if @account.update(account_params)
      flash[:success] = 'Account Updated Successfully'
      redirect_to [:organization, @chart, plutus_account]
    else
      flash[:error] = @account.errors.full_messages
      render :edit
    end
  end

  def destroy
    if @account.destroy
      flash[:success] = 'Account Deleted Successfully'
      redirect_to [:organization, @chart]
    else
      flash[:error] = @account.errors.full_messages
      render :edit
    end
  end

  private

  def account_params
    submitted_type = params.dig(:account, :type)

    if submitted_type
      klass, category = submitted_type.split('/')
      params[:account][:type] = klass
      params[:account][:category] = category
      params[:account][:category] = 'Equity' if klass == 'Plutus::Equity'
    end

    if (suggestion = params.dig(:account, :suggested_gl_code))
      params[:account][:gl_code] = params[:account][:gl_code].presence ||
                                   suggestion
    end

    params.require(:account)
          .permit(:name, :type, :header, :gl_code, :description, :category,
                  :contra, :owner_suppressed, :passthrough)
  end

  def set_account
    @account = @chart.accounts.find(params[:id])
  end

  def set_chart
    @chart = ChartOfAccounts.find(params[:chart_of_accounts_id])
  end

  # For polymorphic path helpers
  def plutus_account
    @account.becomes(Plutus::Account)
  end

  def accounts
    # For billing / invoicing
    gid = params[:billing_context_gid]
    context = GlobalID::Locator.locate gid
    property = context if context.is_a?(Property)
    company = context if context.is_a?(Company)

    query = Plutus::AccountsQuery
            .for_user(current_property_manager)
            .filter_type(params[:type])
            .filter_property(property)
            .filter_company(company)
            .order(:gl_code, :name)

    if params[:chart_of_accounts_id]
      query.where(tenant: ChartOfAccounts.find(params[:chart_of_accounts_id]))
    else
      query
    end
  end

  def in_basis(&)
    if params.dig(:filters, :basis) == 'cash'
      Plutus.in_cash_basis(journal_cache_hint: @chart.companies, &)
    else
      yield
    end
  end
end
