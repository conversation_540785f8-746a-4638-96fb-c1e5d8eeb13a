class Organization::Tunisia::CardActivitiesController < OrganizationController
  include BulkArchivingController

  before_action :set_bank_account
  before_action :set_transaction,
                only: %i[review edit update matching_invoices missing_receipt remove_attachment]
  before_action :set_transactions, only: :bulk_update

  helper_method :next_transaction
  helper_method :plutus_accounts

  MAX_PAGINATION_SIZE = 12

  def index
    @transactions_count = debit_card_purchases.unarchived.count
    @reviewed_transactions_count = count_reviewed_transactions
    @filtered_debit_card_purchases = filtered_purchases
                                     .max_paginated(params[:page], MAX_PAGINATION_SIZE)
                                     .decorate
    @all_debit_card_purchases = filtered_purchases.decorate
  end

  def review
    return unless @transaction.reviewed?

    redirect_to edit_organization_tunisia_bank_account_card_activity_path(
      @bank_account,
      @transaction
    )
  end

  def edit; end

  def update # rubocop:disable Metrics/AbcSize
    forward_to = GlobalID::Locator.locate_signed(transaction_params[:forward_sgid])

    transaction_attributes = transaction_params.except(:forward_sgid).merge(forward_to: forward_to)
    next_pending_transaction = next_transaction

    ActiveRecord::Base.transaction do
      @transaction.update!(transaction_attributes)
      @transaction.reviewed! if @transaction.pending?

      if params[:next] && next_pending_transaction
        flash[:toast_success] = "Transaction (#{@transaction.amount.format}) Ready to Post"
        redirect_to review_organization_tunisia_bank_account_card_activity_path(
          @bank_account,
          next_pending_transaction
        )
      else
        flash[:toast_success] = 'Transaction updated successfully'
        redirect_to organization_tunisia_bank_account_card_activities_path(@bank_account)
      end
    end
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  rescue ActiveRecord::StaleObjectError
    render_ujs_errors(
      'Another user has made changes to the selected transaction(s). ' \
      'Please refresh and try again.'
    )
  end

  def missing_receipt
    @affidavit = @transaction.build_missing_receipt_affidavit(affidavit_params)

    ActiveRecord::Base.transaction do
      @affidavit.save!
      Accounting::MissingReceiptAffidavitGenerator.call(@transaction, current_property_manager)
    end

    update_receipt_segment
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def remove_attachment
    if @transaction.receipt.attached?
      @transaction.receipt.purge
    else
      @transaction.missing_receipt_affidavit_attachment.purge
      @transaction.missing_receipt_affidavit.destroy
    end

    update_receipt_segment
  end

  def post_transactions
    transactions_to_post = debit_card_purchases.reviewed

    if transactions_to_post.any?
      count = transactions_to_post.count
      Accounting::PostTransactionsJob.perform_later(ids: transactions_to_post.pluck(:id),
                                                    property_manager: current_property_manager)
      transactions_to_post.update_all(status: :processed)
      flash[:toast_success] = "#{count} Transactions Posted"
    else
      flash[:error] =
        'Please mark at least one transaction as ready in order to post to the ledger.'
    end

    redirect_to organization_tunisia_bank_account_card_activities_path(@bank_account)
  end

  def matching_invoices # rubocop:disable Metrics/AbcSize
    @invoices = InvoicesQuery.new
                             .search
                             .by_user(current_property_manager)
                             .open
                             .where(amount_cents: @transaction.amount_cents)
                             .where(post_date: ..Time.zone.today)
                             .with_one_line_item
                             .limit(10)

    if params[:vendor_id].present?
      @invoices = @invoices.where(seller: Vendor.find(params[:vendor_id]))
    end

    if params[:property_id].present?
      @invoices = @invoices.where(buyer: Property.find(params[:property_id]))
    end
    render 'organization/tunisia/card_activities/invoice_rows', layout: false
  end

  def bulk_update
    ActiveRecord::Base.transaction do
      @transactions.each do |transaction|
        transaction
          .update!(
            bulk_transaction_params
              .slice(:vendor_id, :account_id, :property_id)
              .merge(lock_version: lock_version_for(transaction))
          )

        transaction.reviewed! if transaction.pending?
      end
    end

    count = @transactions.count
    flash[:toast_success] = "#{count} #{'Transaction'.pluralize(count)} marked as Ready to Post"
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = e.record.errors.full_messages
  rescue ActiveRecord::StaleObjectError
    flash[:error] = 'Another user has made changes to the selected transaction(s). ' \
                    'Please refresh and try again.'
  ensure
    redirect_to organization_tunisia_bank_account_card_activities_path(@bank_account)
  end

  private

  def update_receipt_segment
    render 'update_receipt_segment', format: :js
  end

  def archivables
    debit_card_purchases
  end

  def bulk_archive_path
    bulk_archive_organization_tunisia_bank_account_card_activities_path
  end

  def bulk_unarchive_path
    bulk_unarchive_organization_tunisia_bank_account_card_activities_path
  end

  def archivables_path
    organization_tunisia_bank_account_card_activities_path(@bank_account)
  end

  def archive_terminology
    'Ignored'
  end

  def unarchive_terminology
    'Unignored'
  end

  def count_reviewed_transactions
    debit_card_purchases.reviewed.count
  end

  def bulk_selection_partial_modal
    'ignore_transactions_modal'
  end

  def debit_card_purchases
    @debit_card_purchases ||= Accounting::DebitCardPurchasesQuery.for_bank_account(@bank_account, archived: display_archived?) # rubocop:disable Layout/LineLength
  end

  def set_bank_account
    @bank_account = BankAccountsQuery.new
                                     .search
                                     .by_user(current_property_manager)
                                     .find(params[:bank_account_id])
  end

  def set_transaction
    @transaction = Accounting::DebitCardPurchase.find(params[:id])
  end

  def selected_transactions
    @selected_transactions ||= JSON.parse(params[:selected_transactions])
  end

  def set_transactions
    @transactions = debit_card_purchases.where(id: selected_transactions.pluck('id'))
  end

  def lock_version_for(transaction)
    selected_transactions.find { |t| t['id'] == transaction.id }['lock_version']
  end

  def transaction_params
    params.require(:accounting_debit_card_purchase).permit(:description, :vendor_id, :account_id,
                                                           :property_id, :invoice_id, :forward_sgid,
                                                           :markup_raw, :markup_kind, :lock_version, receipt: []).tap do |p|
      if p[:receipt].is_a?(Array)
        p[:receipt] =
          p[:receipt].first
      end
    end
  end

  def affidavit_params
    params.require(:accounting_missing_receipt_affidavit)
          .permit(:items_purchased, :purpose_of_expense, :reason_for_missing_receipt)
  end

  def bulk_transaction_params
    params.permit(:vendor_id, :account_id, :property_id, :selected_transactions, :overselection,
                  :_method, :authenticity_token, :bank_account_id)
  end

  def next_transaction
    return @next_transaction if defined?(@next_transaction)

    ids = pending_transaction_ids
    return if ids.empty? || ids.length == 1

    index = ids.find_index(@transaction.id)

    return unless index

    next_index = (index + 1) % ids.length
    @next_transaction = Accounting::DebitCardPurchase.find_by(id: ids[next_index])
  end

  def pending_transaction_ids
    debit_card_purchases.pending.order(transaction_at: :desc).pluck(:id)
  end

  def filtered_purchases
    debit_card_purchases
      .order(transaction_at: :desc)
      .filter_with(params[:filters])
  end

  def plutus_accounts
    Plutus::AccountsQuery.new(@bank_account.owner.accounts).search.expensable.reorder(
      gl_code: :asc, id: :asc
    )
  end

  def display_archived?
    params.dig(:filters, :status) == 'archived' ||
      params[:action] == 'bulk_unarchive'
  end
end
