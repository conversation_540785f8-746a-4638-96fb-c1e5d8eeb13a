class Organization::DocumentsController < OrganizationController
  include Api::V1::S3SignedUpload

  def index
    respond_to do |format|
      format.html { index_html }
      format.json { index_json }
    end
  end

  def index_html
    @noport = true
  end

  def index_json
    render json: Document.all
  end

  def create
    @document = current_property_manager.documents.new(document_params)
    @document.save
    render json: @document
  end

  def new; end

  def destroy
    @document = Document.find(params[:id])
    @document.destroy!
    flash[:success] = 'Document removed successfully'
    redirect_to organization_documents_path
  end

  protected

  def bucket_name
    ENV.fetch('S3_UPLOADS_BUCKET', nil)
  end

  def resource_type
    'document'
  end

  private

  def document_params
    params.require(:attachment).permit(:direct_upload_url, :upload_content_type,
                                       :upload_file_name, :upload_file_size)
  end
end
