class Organization::EmployeesController < OrganizationController
  include ArchivingController
  include ContactController

  EMPLOYEES_PER_PAGE = 12
  AUDITS_PER_PAGE = 50

  before_action :set_employees, except: :target

  before_action :set_employee, only: %i[
    audits show edit update archive unarchive send_password_reset
  ]

  before_action :require_administrator, only: %i[
    new create edit update send_password_reset
  ]

  def index
    @employees = @employees
                 .includes(:role)
                 .references(:role)
                 .filter_with(params[:filters])
                 .order_with(params[:sort])

    respond_to do |format|
      format.html { index_html }
      format.json { index_json }
      format.xlsx { index_xlsx }
    end
  end

  def index_html
    @noport = true
    @defer_flash_notices = true
    @employees = @employees.page(params[:page]).per(EMPLOYEES_PER_PAGE)
  end

  def index_json
    if params[:skip_self]
      @employees = @employees.where.not(id: current_property_manager.id)
    end
    render json: @employees
  end

  def index_xlsx
    export = EmployeeListExporter.new(employees: @employees)

    send_data export.workbook.stream.string,
              filename: export.filename,
              type: :xlsx
  end

  def show
    respond_to do |format|
      format.html { show_html }
      format.json { show_json }
    end
  end

  def show_html
    render :show
  end

  def show_json
    render json: @employee.as_json(only: %i[first_name last_name phone email])
  end

  # TODO: remove
  def mockup
    @employee = @employees.unscoped.find(params[:id])
    super
  end

  def new
    @employee = PropertyManager.new
  end

  def create
    @employee = PropertyManager.new(employee_params)
    @employee.skip_password_validation = true

    assign_property_membership

    if @employee.save
      flash[:success] = 'Employee Added'
      redirect_to organization_employee_path(@employee)
    else
      flash[:error] = @employee.errors.full_messages
      render :new
    end
  end

  def edit; end

  def update
    assign_property_membership

    if @employee.update(employee_params)
      flash[:success] = 'Employee Updated Successfully'
      redirect_to organization_employee_path(@employee)
    else
      flash.now[:error] = @employee.errors.full_messages
      render :edit, status: :unprocessable_entity
    end
  end

  def send_password_reset
    @employee.send_reset_password_instructions
    flash[:success] = "Password Reset Email Sent to #{@employee.email}"
    redirect_to organization_employee_path(@employee)
  end

  def target
    targets = [Company, Portfolio, Property].flat_map do |klass|
      klass.all.map do |item|
        { name: item.name, id: item.to_global_id.to_s }
      end
    end

    render json: targets
  end

  def audits
    respond_to do |format|
      format.html do
        @audits = @employee.authored_audits
                           .order(created_at: :desc)
                           .page(params[:page])
                           .per(AUDITS_PER_PAGE)
      end

      format.xlsx do
        report = Reports::V3::AuditLog.new(
          filters: OpenStruct.new(
            employee_id: @employee.id,
            start_date: '', end_date: ''
          )
        )

        render xlsx: report
      end
    end
  end

  private

  def employee_params
    params.require(:property_manager)
          .permit(:first_name, :last_name,
                  :email, :phone,
                  :role_id, :top_level,
                  :billing_rate)
  end

  def set_employees
    @employees = EmployeesQuery.new.search
  end

  def set_employee
    @employee = EmployeeDecorator.decorate(
      # Unscoped to find archived employees
      @employees.unscoped.find(params[:id])
    )
  end

  def require_administrator
    return if current_property_manager.role.administrator?

    flash[:error] = 'Only administrators can modify employees.'
    redirect_to organization_employees_path
  end

  def assign_property_membership
    return if params[:target_id].nil?

    target_ids = params[:target_id]

    targets = target_ids.compact_blank.map do |target|
      GlobalID::Locator.locate(target)
    end

    AssignPropertyMembership.new(@employee)
                            .with_targets(targets)
                            .create
  end

  def archivable
    @employee
  end

  def archivable_path
    organization_employee_path(@employee)
  end
end
