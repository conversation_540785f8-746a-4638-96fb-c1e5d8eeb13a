class InvoiceProcessing::EmailsController < InvoiceProcessingController
  before_action :set_email, except: %i[index statistics]
  before_action :set_attachments, only: %i[process_invoice]

  def index
    @emails = \
      InvoiceProcessingEmailsQuery
      .new.search
      .includes(:assigned_to, :processed_by)
      .filter_with(params[:filters])
      .order_with(params[:sort])
      .order(created_at: :desc)

    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def index_html
    @emails = @emails.max_paginated(params[:page], 12)
  end

  def index_xlsx
    render xlsx: ActionTable::InvoiceProcessingEmails.new(self, @emails)
  end

  # TODO: Credit form
  def show
    @invoice = Invoice.new
    @invoice.line_items.build
  end

  def claim
    assigner = InvoiceProcessing::Assigner.new(
      @email,
      processor: current_invoice_processor
    )
    assigner.assign
    @email.save!

    redirect_to_email
  end

  def complete
    @email.complete(user: current_invoice_processor)
    @email.save!

    redirect_to_email
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = e.record.errors.full_messages
    redirect_to_email
  end

  def process_invoice
    result = Accounting::CreatePayable.call(params: params) do |invoice|
      invoice.invoice_processing_email = @email

      @attachments.each do |attachment|
        invoice.invoice_processing_associated_attachments.build(
          attachment: attachment
        )
      end
    end

    if result.successful?
      redirect_to_email
    else
      @invoice = result.invoice

      flash.now[:error] = result.errors
      render :show
    end
  end

  def reject
    @email.reject(
      user: current_invoice_processor,
      status: params.dig(:invoice_processing_email, :status),
      rejected_reason: params.dig(:invoice_processing_email, :rejected_reason)
    )
    @email.save!

    redirect_to_email
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def statistics
    @emails = InvoiceProcessingEmailsQuery.new.search
    @customers = CustomersQuery.new.search.uses_invoice_processing
    @assignees = InvoiceProcessing::User.all.where(
      id: @emails.select(:assigned_to_id)
    )
  end

  private

  def redirect_to_email
    redirect_to invoice_processing_email_path(
      @email,
      customer_subdomain: params[:customer_subdomain]
    )
  end

  def set_email
    Customer.activate!(params[:customer_subdomain])
    @email = InvoiceProcessing::Email.find(params[:id])
  end

  def set_attachments
    @attachments = @email.attachments.find(
      JSON.parse(params.dig(:invoice, :selected_attachment_ids))
    )
  end
end
