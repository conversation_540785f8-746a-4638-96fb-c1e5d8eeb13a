class Settings::TagsController < ManagementController
  layout 'settings/tags'

  helper_method :tabs

  before_action :set_tab

  before_action :set_tag, only: %i[edit update destroy]

  def index
    @tags = TagsQuery.new
                     .search
                     .for_user(current_property_manager)
                     .for_type(@tab.taggable_type)
                     .filter_with(params[:filters])
                     .order_with(params[:sort])

    @tags = @tags.includes(:property) if params[:type] == 'agreements_simple_agreements'

    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def index_html
    @noport = true
    @defer_flash_notices = true
  end

  def index_xlsx
    render xlsx: ActionTable::Settings::Tags.new(self, @tags)
  end

  def create
    @tag = Tag.new(tag_params)

    if @tag.save
      flash[:toast_success] = 'Tag Added Successfully'
      redirect_back_to_tags(@tag.taggable_type.underscore.tr('/', '_').pluralize)
    else
      render_ujs_errors @tag.errors.full_messages
    end
  end

  def edit
    render partial: 'settings/tags/form', locals: { tag: @tag }
  end

  def update
    if @tag.update(tag_params)
      flash[:success] = 'Tag Updated Successfully'
      redirect_back_to_tags
    else
      render_ujs_errors @tag.errors.full_messages
    end
  end

  def destroy
    if @tag.destroy
      flash[:success] = 'Tag Removed Successfully'
      redirect_back_to_tags
    else
      render_ujs_errors @tag.errors.full_messages
    end
  end

  def bulk_destroy
    @tags = Tag.where(id: params[:selected_ids])

    ActiveRecord::Base.transaction do
      @tags.each(&:destroy!)
    end

    count = @tags.size

    flash[:success] = "#{count} #{'Tag'.pluralize(count)} Removed Successfully"
    redirect_back_to_tags
  end

  def bulk_merge
    @tags = Tag.where(id: params[:selected_ids])

    result = Tag::Merge.call(tags: @tags)

    if result.successful?
      count = result.count

      merged = count - 1

      into = result.tag.tag

      flash[:success] = "#{merged} #{'Tag'.pluralize(merged)} Merged Successfully Into #{into}"
    else
      flash[:error] = result.errors
    end

    redirect_back_to_tags
  end

  private

  def set_tab
    @tab = tabs.find { |tab| tab.filter == params[:type] } || tabs.first
  end

  def set_tag
    @tag = Tag.find(params[:id])
  end

  def tag_params
    params.require(:tag).permit(:tag, :taggable_type, :property_id)
  end

  def redirect_back_to_tags(taggable_type = @tab.filter)
    query = { type: taggable_type }.compact_blank

    return redirect_to settings_tags_path(query) if @tab.filter != taggable_type

    redirect_back fallback_location: settings_tags_path(query)
  end

  class Tab
    def initialize(taggable)
      @taggable = taggable
    end

    def title
      @taggable
        .name.demodulize
        .gsub('Simple', '')
        .gsub('MaintenanceTicket', 'WorkOrder')
        .gsub('Tenant', I18n.t('tenant_term'))
        .titleize.pluralize
    end

    def filter
      @taggable.table_name
    end

    def taggable_type
      @taggable.name
    end

    def to_param
      filter
    end
  end

  def tabs
    Taggable.models.map { |klass| Tab.new(klass) }.sort_by(&:title)
  end
end
