class LandingPage::MarketingSiteProxyController < ApplicationController
  include ReverseProxy::Controller

  MARKETING_DOMAIN = 'marketing-site.revela.co'.freeze

  def proxy
    expires_in 5.minutes, public: true

    options = {
      headers: {
        'Host' => MARKETING_DOMAIN,
        'Accept-Encoding' => 'deflate',
        'X-Real-IP' => request.remote_ip,
        'X-Forwarded-For' => request.remote_ip
      },
      http: {
        open_timeout: 5.seconds,
        read_timeout: 5.seconds
      }
    }

    reverse_proxy "https://#{MARKETING_DOMAIN}", **options
  end
end
