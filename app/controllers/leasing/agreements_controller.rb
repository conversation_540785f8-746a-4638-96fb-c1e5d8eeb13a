class Leasing::AgreementsController < LeasingController
  include ArchivingController
  include ElectronicSignableController

  layout 'agreements', only: :index

  before_action :set_agreement_type
  before_action :set_agreement, except: %i[
    new index create
    batch_invite batch_add_tag batch_remove_tag
  ]

  rescue_from ActiveRecord::RecordInvalid, with: :render_ujs_exception

  helper_method :display_leases?, :display_loans?

  def index
    @noport = true
    @defer_flash_notices = true

    collection = agreements

    collection = collection.unarchived.current if params.dig(:filters, :status).blank?

    respond_to do |format|
      format.html do
        page_size = params[:page_size] || 12

        collection = collection.max_paginated(params[:page], page_size)

        @agreements = AgreementDecorator.decorate_collection(collection)
      end
      format.xlsx do
        @agreements = AgreementDecorator.decorate_collection(collection)

        table = if params[:type] == 'leases'
                  ActionTable::Leases.new(self, @agreements)
                elsif params[:type] == 'loans'
                  ActionTable::Loans.new(self, @agreements)
                else
                  ActionTable::Agreements.new(self, @agreements)
                end

        render xlsx: table
      end
    end
  end

  def show
    @agreement = AgreementDecorator.decorate(@agreement)
  end

  def new; end

  def create
    ActiveRecord::Base.transaction do
      property = Property.find_by(id: params.dig(:agreement, :property_id))

      tenant_attributes = {
        first_name: params.dig(:agreement, :first_name),
        last_name: params.dig(:agreement, :last_name),
        email: params.dig(:agreement, :email),
        phone: params.dig(:agreement, :phone)
      }

      member = TenantsQuery.new.search.discover_existing(
        **tenant_attributes
      ).first || Tenant.create!(tenant_attributes)

      member.resident!

      @agreement = @agreement_type.simple_agreements.create!(
        company: property&.company,
        property: property,
        start_date: params.dig(:agreement, :start_date),
        end_date: params.dig(:agreement, :end_date)
      ) do |agreement|
        agreement.memberships.primary.build(tenant: member)
      end

      type_name = @agreement_type.name.singularize

      flash[:success] = "#{type_name} Created Successfully"
      redirect_to leasing_agreement_path(@agreement, type: params[:type])
    end
  end

  def edit; end

  def update
    property = Property.find_by(id: params.dig(:agreement, :property_id))

    @agreement.assign_attributes(agreement_params)
    @agreement.update!(property: property, company: property&.company)

    type_name = @agreement_type.name.singularize

    flash[:success] = "#{type_name} Updated Successfully"
    redirect_to leasing_agreement_path(@agreement, type: params[:type])
  end

  def batch_invite
    filtered_agreements = if params[:overselection] == 'true'
                            agreements.filter_with(params[:filters])
                          else
                            agreements.where(id: params[:selected_ids])
                          end

    members = if params[:type] == 'leases'
                Tenant.joins(:leases).merge(filtered_agreements).merge(LeaseMembership.primary_tenant)
              else
                Tenant.joins(:simple_agreements).merge(filtered_agreements)
              end

    available_count = members.count

    members = members
              .where(confirmed_at: nil)
              .where(confirmation_sent_at: [nil, ...24.hours.ago])
              .where.not(email: ['', nil])

    invited_count = members.count

    members.each(&:send_confirmation_instructions)

    skipped_count = available_count - invited_count
    message = "Sent #{helpers.pluralize(invited_count, 'Invite')}"
    if skipped_count.positive?
      message += " (#{skipped_count} Recently Invited or Already Confirmed)"
    end

    flash[:success] = message
    redirect_to leasing_agreements_path(params[:type])
  end

  def batch_add_tag
    filtered_agreements = if params[:overselection] == 'true'
                            agreements.filter_with(params[:filters])
                          else
                            agreements.where(id: params[:selected_ids])
                          end

    tag_id = params[:tag_id]

    ActiveRecord::Base.transaction do
      filtered_agreements.find_each do |agreement|
        agreement.taggings.find_or_create_by!(tag_id: tag_id)
      end
    end
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = e.record.errors.full_messages
  ensure
    redirect_back(fallback_location: leasing_agreements_path(params[:type]))
  end

  def batch_remove_tag
    filtered_agreements = if params[:overselection] == 'true'
                            agreements.filter_with(params[:filters])
                          else
                            agreements.where(id: params[:selected_ids])
                          end

    Tagging
      .joins(:tag)
      .where(taggable: filtered_agreements, tag_id: params[:tag_id])
      .delete_all

    redirect_back(fallback_location: leasing_agreements_path(params[:type]))
  end

  private

  def agreements
    if params[:type] == 'leases'
      leases
    elsif params[:type] == 'loans'
      loans
    else
      simple_agreements
    end
  end

  def leases
    LeasesQuery
      .new(Lease.all).search
      .by_user(current_property_manager)
      .joins(unit: :property)
      .includes(:primary_tenant)
      .merge(Unit.unarchived)
      .order_with(params[:sort])
      .filter_with(params[:filters])
  end

  def loans
    Lending::LoansQuery
      .new.search
      .order(loan_number: :asc)
      .includes(:borrower, :property, :tags)
      .references(:borrower, :property)
      .order_with(params[:sort])
      .filter_with(params[:filters])
  end

  def simple_agreements
    AgreementsQuery
      .new.search
      .by_user(current_property_manager)
      .by_type_slug(params[:type])
      .order_with(params[:sort])
      .filter_with(params[:filters])
      .includes(:agreement_type, :property, :tags, memberships: :tenant)
  end

  def set_agreement_type
    return if params[:type] == 'lease'

    @agreement_type = Agreements::AgreementType.find_by(slug: params[:type])
  end

  def set_agreement
    @agreement = Agreements::SimpleAgreement.find(params[:id])
  end

  def archivable
    @agreement
  end

  def electronic_signable
    @agreement
  end

  def primary_signers
    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
      [electronic_signable.primary_tenant]
    else
      electronic_signable.tenants
    end
  end

  def countersigner
    return nil if params[:countersigner_id].blank? || params[:countersigner_id] == '-1'

    PropertyManager.find(params[:countersigner_id])
  end

  def archivable_path
    leasing_agreement_path(@agreement, type: params[:type])
  end

  def render_ujs_exception(exception)
    render_ujs_errors exception.record.errors.full_messages
  end

  def agreement_params
    params
      .require(:agreement)
      .permit(:start_date, :end_date, tags: [])
  end

  def display_leases?
    !display_loans? || CustomerSpecific::Behavior.lending_loans_enabled?
  end

  def display_loans?
    Customer.current.lending? || CustomerSpecific::Behavior.lending_loans_enabled?
  end
end
