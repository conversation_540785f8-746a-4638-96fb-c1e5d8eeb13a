class Maintenance::TicketsController < MaintenanceController
  include Maintenance::Tickets::ShowHelpers

  layout 'maintenance_ticket', only: :show

  before_action :set_maintenance_ticket, except: %i[
    new create available index service_areas service_area assignable_employees vendors assignees
  ]

  after_action :clear_notifications, only: :show

  decorates_assigned :maintenance_ticket

  def new
    @maintenance_ticket = MaintenanceTicket.new

    return if params[:regarding_sgid].blank?

    target = GlobalID::Locator.locate_signed(params[:regarding_sgid])
    MaintenanceTicket::Regard.call(@maintenance_ticket, target)
  end

  def index
    if !Rails.env.test? &&
       Feature.enabled?(:maintenance_tickets_default_open, Customer.current) &&
       params.dig(:filters, :status).nil?
      params[:filters] ||= {}
      params[:filters][:status] = 'show-open'
    end

    @maintenance_tickets = \
      MaintenanceTicketsQuery
      .new.search
      .by_user(current_property_manager)
      .filter_with(params[:filters])
      .order_with(params[:sort])
      .order(opened_at: :desc)
      .includes(
        :property, :unit, :assigned_user, :vendors
      )

    respond_to do |format|
      format.html { index_html }
      format.json { index_json }
      format.xlsx { index_xlsx }
    end
  end

  def index_html
    @noport = true
    @defer_flash_notices = true
    @maintenance_tickets = @maintenance_tickets
                           .max_paginated(params[:page], 15)
                           .decorate

    # @TODO: right now if the URL is too long, it will crash out the cookie
    # it seems that nine properties within the filters is too much to handle
    if (url = request.fullpath).length < 1_500
      session[:maintenance_tickets_index_url] = url
    else
      session[:maintenance_tickets_index_url] = nil
      Honeybadger.notify("Maintenance tickets index URL too long.")
    end
  end

  def index_json
    render json: @maintenance_tickets.page(params[:page]).per(15)
  end

  def index_xlsx
    tickets = @maintenance_tickets.limit(1000)
    exporter = MaintenanceTicketsExporter.new(tickets: tickets)
    send_data exporter.workbook.stream.string,
              filename: exporter.filename,
              type: :xlsx
  end

  def show
    @noport = true
    @defer_flash_notices = true
    @maintenance_ticket_json = serialize @maintenance_ticket
    @todo_list_json = serialize @maintenance_ticket.todo_list
    @maintenance_ticket.seen! if @maintenance_ticket.fresh?
    @timeline_events = (
      @maintenance_ticket.events.includes(
        :employee_assignee, :vendor_assignee
      ) + @maintenance_ticket.regarding_messages
    ).sort_by(&:created_at)
  end

  def create
    result = MaintenanceTicket::Persist.call(params, current_property_manager)

    if result.successful?
      flash[:success] = 'Maintenance Ticket Submitted Successfully'
      redirect_to maintenance_ticket_path(result.ticket)
    else
      render_ujs_errors result.errors
    end
  end

  def update
    result = MaintenanceTicket::Persist.call(params, current_property_manager)

    @maintenance_ticket = result.ticket

    flash[:error] = result.errors unless result.successful?

    redirect_to @maintenance_ticket
  end

  def destroy
    # Preload assignments to send unassignment mail after ticket destroy
    assignments = @maintenance_ticket
                  .vendor_assignments
                  .open
                  .includes(:maintenance_ticket)
                  .to_a

    if @maintenance_ticket.destroy
      unless @maintenance_ticket.resolved? || @maintenance_ticket.closed?
        assignments.each do |assignment|
          VendorAssignmentsMailer
            .unassign_vendor(assignment, user: current_property_manager)
            .deliver_now
        end
      end

      flash[:success] = 'Maintenance Ticket Removed Successfully'
      redirect_to maintenance_tickets_path
    else
      flash[:error] = @maintenance_ticket.errors.full_messages
      render :show
    end
  end

  def available
    # TODO: assume always elasticsearch
    available = if params[:search].present?
                  query = params[:q]
                  results = Searchable.search_all(query, [Property, Unit, Tenant],
                                                  scope_to: current_property_manager)

                  {
                    results: results.group_by(&:class).to_h do |klass, records|
                      [
                        klass.name.tableize,
                        {
                          name: klass.name.humanize,
                          results: records.filter_map do |record|
                            next nil if record.try(:archived?)

                            description = case record
                                          when Tenant
                                            [
                                              record.service_area&.name,
                                              record.formatted_phone
                                            ].compact.join('  |  ')
                                          when Unit
                                            record.property.name
                                          when Property
                                            record.description
                                          end

                            {
                              title: record.to_s,
                              description: description.presence,
                              sgid: record.to_sgid.to_s
                            }.compact
                          end
                        }
                      ]
                    end
                  }
                else
                  TicketRegardableQuery.for_user(current_property_manager)
                end

    render json: available
  end

  def service_areas
    render json: TicketRegardableQuery.for_user(
      current_property_manager,
      service_areas_only: true
    )
  end

  def service_area
    regarding = GlobalID::Locator.locate_signed(params[:sgid])

    @service_area = regarding&.service_area

    respond_to do |format|
      format.js { render 'maintenance/tickets/new/service_area' }
    end
  end

  def vendors
    query = params[:q]

    return render json: { results: {} } if query.blank?

    all_results = Searchable.search_all(query, [Vendor])

    return render json: { results: {} } if all_results.empty?

    results = all_results.map do |result|
      {
        title: result.name,
        sgid: result.to_sgid.to_s,
        id: result.id.to_s,
        klass: result.class.name
      }
    end

    render json: { results: }
  end

  def assignable_employees
    tmp_ticket = MaintenanceTicket.new
    if (regarding = GlobalID::Locator.locate_signed(params[:sgid]))
      MaintenanceTicket::Regard.(tmp_ticket, regarding)
    end

    assignable_employee_options =
      tmp_ticket.assignable_employees.map do |user|
        { name: user.name, value: user.id.to_s }
      end + [{ name: 'None', value: '' }]

    render json: assignable_employee_options
  end

  def assignees
    query = params[:q]

    return render json: { results: {} } if query.blank?

    all_results = Searchable.search_all(query, [PropertyManager])

    return render json: { results: {} } if all_results.empty?

    results = {
      property_managers: {
        name: 'Property Manager',
        results: all_results.map do |result|
          {
            title: result.name,
            sgid: result.to_sgid.to_s,
            id: result.id.to_s,
            klass: result.class.name
          }
        end
      }
    }

    render json: { results: }
  end

  def create_child
    @maintenance_ticket.create_child
    render json: @maintenance_ticket
  end

  def add_vendor
    head :no_content
  end

  def remove_vendor
    head :no_content
  end

  def open
    result = MaintenanceTicket::Reopen.call(
      @maintenance_ticket, current_property_manager
    )

    if result.successful?
      flash[:success] = "#{@maintenance_ticket} reopened"
    else
      flash[:error] = result.errors
    end

    redirect_to maintenance_ticket_path(@maintenance_ticket)
  end

  def close
    result = WorkOrder::Close.call(work_order: @maintenance_ticket,
                                   user: current_property_manager)

    if result.successful?
      flash[:success] = "#{@maintenance_ticket} closed"
    else
      flash[:error] = result.errors
    end

    redirect_to maintenance_ticket_path(@maintenance_ticket)
  end

  def resolve
    result = WorkOrder::Resolve.call(
      work_order: @maintenance_ticket,
      user: current_property_manager
    )

    if result.successful?
      flash[:success] = "#{@maintenance_ticket} resolved"
    else
      flash[:error] = result.errors
    end

    redirect_to maintenance_ticket_path(@maintenance_ticket)
  end

  def associate_tenant
    @maintenance_ticket.update!(tenant_id: params[:tenant_id])

    redirect_to maintenance_ticket_path(@maintenance_ticket)
  end

  private

  def set_maintenance_ticket
    @maintenance_ticket =
      MaintenanceTicketsQuery.new.search.by_user(current_property_manager).find(params[:id])
  end

  def clear_notifications
    ClearNotificationsJob.perform_later @maintenance_ticket,
                                        current_property_manager
  end
end
