class ApplicationController < ActionController::Base
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :exception

  include ActionPolicy::UnauthorizedRedirecting
  include ActiveStorage::SetCurrent
  include ControllerCommon
  include UjsControllerHelpers
  include UserProfileContext
  include Serialization
  include FingerprintConfirmation

  before_action :update_sidebar_path

  def boring_science_user
    current_admin_user
  end

  private

  def update_sidebar_path
    ::Sidebar::Base.current_path = request.path
  end
end
