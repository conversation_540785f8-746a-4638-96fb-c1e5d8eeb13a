class Reports::AccountingContextsController < ManagementController
  skip_before_action :require_manager

  def index
    render json: contexts.as_json
  end

  def accounts
    sgid = params[:context_id]
    context = GlobalID::Locator.locate_signed(sgid)

    if context
      accounts = context.accounting_context.accounts.reorder(gl_code: :asc, id: :asc)

      json = accounts.map do |account|
        { value: account.id, name: account.display_name }
      end

      render json: json
    else
      render json: []
    end
  end

  private

  def parent
    sgid = params[:parent_sgid]

    GlobalID::Locator.locate_signed(sgid) if sgid.present?
  end

  def allow_portfolios?
    params[:allow_portfolios] == 'true' &&
      Feature.enabled?(:reports_scope_portfolios, Customer.current)
  end

  def show_client_entity?(employee)
    employee.try(:top_level?) ||
      employee.companies.pluck(:id).include?(Customer.current.client_entity&.id)
  end

  def contexts
    contextables = if parent
                     parent.accounting_context.children
                   elsif (employee = find_current_property_manager)
                     employee_top_level(employee)
                   elsif (owner = find_current_owner)
                     owner_top_level(owner)
                   else
                     []
                   end

    contextables.map(&:accounting_context)
  end

  def employee_top_level(employee)
    if allow_portfolios? && show_client_entity?(employee)
      user_portfolios(employee) + [Customer.current.client_entity]
    elsif allow_portfolios?
      user_portfolios(employee)
    else
      user_companies(employee)
    end
  end

  def owner_top_level(owner)
    user_companies(owner)
  end

  def user_portfolios(user)
    portfolios = PortfoliosQuery
                 .new.search
                 .by_user(user)
                 .where(setup: true)

    if portfolios.many?
      portfolios.order(name: :asc).includes(:configuration)
    else
      user_companies(user)
    end
  end

  def user_companies(user)
    CompaniesQuery
      .new.search
      .by_user(user)
      .setup
      .includes(
        :chart_of_accounts,
        properties: :units,
        portfolio: [configuration: :chart_of_accounts]
      )
  end

  def find_current_owner
    return nil unless request.referer&.include?('/owners/')

    current_owner
  end

  def find_current_property_manager
    return nil if request.referer&.include?('/owners/')

    current_property_manager
  end
end
