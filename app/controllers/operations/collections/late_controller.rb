class Operations::Collections::LateController <
  Operations::CollectionsController
  def index
    @ledgers =
      late_ledgers
      .includes(:primary_tenant, property: :address)
      .order_with(params[:sort])

    if Customer.current_subdomain.start_with?('mph', 'marketplacehomes')
      @ledgers = @ledgers.includes(primary_tenant: :tags)
    end

    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  private

  def index_html
    @ledgers = @ledgers.page(params[:page]).per(12)
  end

  def index_xlsx
    render xlsx: ActionTable::Collections::Late.new(self, @ledgers)
  end

  def late_ledgers
    CollectionsQuery.new.search.late.filter_with(params[:filters])
  end
end
