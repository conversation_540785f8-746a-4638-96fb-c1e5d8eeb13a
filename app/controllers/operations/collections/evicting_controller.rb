class Operations::Collections::EvictingController <
  Operations::CollectionsController
  def index
    @ledgers = \
      evicting_ledgers
      .includes(:primary_tenant, property: :address, eviction: :assigned_vendor)
      .order_with(params[:sort])

    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def modal
    selected = if params.dig(:selection, :overselection) == 'true'
                 unassigned_ledgers
               else
                 unassigned_ledgers
                   .where(id: params.dig(:selection, :ids).values)
               end

    selected_ids = selected.pluck(:id)

    update_bulk_selection_modal(
      :assign_eviction_vendor_modal,
      'operations/collections/evicting/assign_eviction_vendor_modal',
      selected_ids
    )
  end

  def packet
    download_id = params.require(:download_id)

    eviction = Collections::Eviction.find(params[:evicting_id])

    Collections::Eviction::DownloadPacketJob.set(wait: 0.2).perform_later(
      download_id: download_id,
      user: current_property_manager,
      eviction: eviction
    )

    head :no_content
  end

  # Assign vendors
  def create
    vendor = Vendor.find_by(id: params[:vendor_id])

    return render_ujs_errors 'Please select a vendor' unless vendor

    ActiveRecord::Base.transaction do
      ledgers = unassigned_ledgers.where(id: params[:selected_ids])

      count = ledgers.count
      things = 'Ledger'.pluralize(count)

      evictions = Collections::Eviction.where(id: ledgers.select(:eviction_id))

      evictions.update(assigned_vendor: vendor, assigned_at: Time.zone.now)

      message = params[:message]

      evictions.each do |eviction|
        AfterCommitEverywhere.after_commit do
          Collections::EvictionVendorsMailer
            .assign_eviction_vendor(eviction: eviction, message: message)
            .deliver_later
        end
      end

      flash[:success] = "Assigned #{count} #{things} Successfully"
    end

    redirect_to operations_collections_evicting_index_path
  end

  private

  def index_html
    @ledgers = @ledgers.page(params[:page]).per(12)
  end

  def index_xlsx
    render xlsx: ActionTable::Collections::Evicting.new(self, @ledgers)
  end

  def evicting_ledgers
    CollectionsQuery
      .new.search
      .evicting
      .filter_with(params[:filters])
  end

  def unassigned_ledgers
    evicting_ledgers.without_eviction_vendor
  end
end
