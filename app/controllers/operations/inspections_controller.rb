class Operations::InspectionsController < OperationsController
  include ArchivingController
  include BulkArchivingController

  before_action :set_inspection,
                except: %i[index new create targets assignees] + BULK_ARCHIVING_ACTIONS
  before_action :noport, only: %i[index show review reams]
  layout 'inspection', only: %i[show review reams]

  def index
    @all_inspections = inspections

    @filtered_inspections =  @all_inspections
                             .unarchived
                             .includes(:template)
                             .preload(:assigned_to, :target)
                             .order(created_at: :desc)
                             .filter_with(params[:filters])
                             .order_with(params[:sort])

    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def show
    return unless @inspection.in_progress?

    redirect_to operations_inspection_page_path(@inspection, 1)
  end

  def new
    @inspection = Inspection::Report.new

    return if params[:target_id].blank?

    # Assign a target from the URL, e.g. coming from a unit actions menu
    @target = GlobalID::Locator.locate_signed(params[:target_id])
    @inspection.target_id = params[:target_id]
    @inspection.name = "Inspect #{@target.name}"
  end

  def edit; end

  def update
    inspection_params = params.require(:inspection_report).permit(
      :name, :earliest_start_date, :due_date, :entry_code
    )

    assignment_handler

    @inspection.update!(inspection_params)

    flash[:success] = 'Inspection Updated Successfully'

    redirect_to operations_inspection_path(@inspection)
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def targets
    esq = params[:q] || '*'

    models = Searchable.search_all(esq, [Property, Unit], scope_to: current_property_manager)

    render json: {
      results: models.map do |record|
        {
          title: record.name,
          description: record.description,
          id: record.to_sgid.to_s
        }
      end
    }
  end

  def assignees
    esq = params[:q] || '*'

    models = [PropertyManager, Tenant]

    if Feature.enabled?(:vendor_inspections, Customer.current)
      models = case params[:type]
               when 'vendor'
                 [Vendor]
               when 'property_manager'
                 [PropertyManager]
               when 'tenant'
                 [Tenant]
               else
                 [Vendor, PropertyManager, Tenant]
               end
    end

    results = Searchable.search_all(esq, models, scope_to: current_property_manager)

    json = {
      results: results.group_by(&:class).to_h do |klass, records|
        klass_name = if klass.name == 'PropertyManager'
                       'Employee'
                     else
                       klass.name
                     end

        [
          klass_name,
          {
            name: klass_name,
            results: records.map do |record|
              description = case klass_name
                            when 'Employee'
                              record.role.name
                            when 'Tenant'
                              record.current_lease&.unit&.qualified_name || 'Tenant'
                            when 'Vendor'
                              record.name
                            end

              {
                title: record.to_s,
                description: description.presence,
                sgid: record.to_sgid.to_s
              }.compact
            end
          }
        ]
      end
    }

    render json: json
  end

  def create
    result = Inspection::Prepare.call \
      user: current_property_manager, params: params

    if result.successful?
      @inspection = result.inspection
      redirect_to operations_inspection_path(@inspection)
    else
      render_ujs_errors result.errors
    end
  end

  def review
    @wizard = Inspections::Wizard.new(inspection: @inspection)
  end

  def submit
    if Feature.enabled?(
      :validate_reams_submissions, Customer.current
    ) && @inspection.reams_order.present?
      survey = @inspection.reams_survey

      return render_ujs_errors survey.errors unless survey.valid?
    end

    @inspection.update!(comments: params.dig(:inspection, :comments), submitted_at: Time.zone.now)

    if @inspection.reams_order.present?
      @inspection.review!
    else
      @inspection.complete!
    end

    flash[:success] = 'Inspection Submitted Successfully'

    redirect_to operations_inspection_path(@inspection)
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def reopen
    @inspection.reopen!

    flash[:success] = 'Inspection Reopened Successfully'

    redirect_to operations_inspection_path(@inspection)
  end

  def question
    @url = operations_inspection_responses_path(@inspection)
    @question = @inspection.template.questions.find(params[:question_id])
    record = @inspection.records.find(params[:record_id])
    @response = @inspection.responses.order(id: :desc).find_or_initialize_by(
      question: @question,
      record: record,
      room_index: params[:room_index] || 0,
      room_id: params[:room_id].presence
    )
    @response.photos.build
    render 'shared/inspections/observation', format: :js, layout: nil
  end

  def reams; end

  def submit_reams
    order = @inspection.reams_order

    result = REAMS::Order::EnqueueSubmission.call(order:)

    if result.successful?
      flash[:success] = 'REAMS Order Enqueued for Submission'
    else
      flash[:error] = result.errors
    end

    redirect_to reams_operations_inspection_path(@inspection)
  end

  def mercury; end

  def submit_mercury
    order = @inspection.mercury_order

    result = Mercury::Order::EnqueueSubmission.call(order:)

    if result.successful?
      flash[:success] = 'Mercury Order Enqueued for Submission'
    else
      flash[:error] = result.errors
    end

    redirect_to mercury_operations_inspection_path(@inspection)
  end

  private

  def bulk_archive_additional_message
    inspections = Inspection::Report
                  .where(id: @selected_ids)
                  .includes(:active_inspectify_order, :sierra_leone_inspection_order)

    return unless inspections.any?(&:assigned_to_inspection_partner?)

    'Please note that any vendor assignments that have already started ' \
      'cannot be canceled and will still incur a cost.'
  end

  def archivable
    @inspection
  end

  def archivable_path
    operations_inspection_path(@inspection)
  end

  def archivables
    inspections
  end

  def bulk_archive_path
    bulk_archive_operations_inspections_path
  end

  def archivables_path
    operations_inspections_path
  end

  def inspections
    InspectionsQuery
      .new.search
      .by_user(current_property_manager)
  end

  def set_inspection
    @inspection = inspections.find(params[:id]).decorate
  end

  def noport
    @noport = true
    @defer_flash_notices = true
  end

  def index_html
    @filtered_inspections = @filtered_inspections.max_paginated(params[:page], 12)
  end

  def index_xlsx
    @filtered_inspections = @filtered_inspections.limit(1000)

    render xlsx: ActionTable::Inspections.new(self, @filtered_inspections)
  end

  def assignment_handler
    assigned_to_sgid = params.dig(:inspection_report, :assigned_to)

    if assigned_to_sgid.present?
      @inspection.assigned_to = GlobalID::Locator.locate_signed(assigned_to_sgid)
    elsif assigned_to_sgid == ''
      @inspection.assigned_to = nil
      @inspection.open!
    end
  end
end
