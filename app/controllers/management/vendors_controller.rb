class Management::VendorsController < ManagementController
  include ArchivingController

  before_action :find_vendor, except: %i[index new create]

  def index
    respond_to do |format|
      format.html { index_html }
      format.json { index_json }
    end
  end

  def index_html
    @noport = true
    @defer_flash_notices = true
    @vendors = vendors
               .includes(:tags, :vendor_contacts)
               .filter_kind(params.dig(:filters, :kind))
               .filter_tag(params.dig(:filters, :tag))
               .page(params[:page]).per(15)
  end

  def index_json
    render json: vendors.includes(:bank_account, :vendor_contacts), include: ''
  end

  def show
    return unless Feature.enabled?(:updated_vendor_show, Customer.current)

    @noport = true
    @defer_flash_notices = true
    @contact = @vendor
    render 'contacts/contact'
  end

  def new
    @vendor = Vendor.new(prefilled_vendor_params)
    vendor.build_address
    vendor.build_bank_account
    vendor.build_taxpayer_identification
    vendor.vendor_contacts.build
  end

  def create
    persist 'Vendor Created Successfully'
  end

  def edit
    vendor.build_address unless vendor.address
    vendor.build_bank_account unless vendor.bank_account
    vendor.build_taxpayer_identification unless vendor.taxpayer_identification
    vendor.vendor_contacts.build unless vendor.vendor_contacts.any?
  end

  def update
    persist 'Vendor Updated Successfully'
  end

  def invite
    Vendor::Invite.create!(vendor: vendor)
    flash[:success] = "Invite Sent to #{vendor.email}"
    redirect_to vendor_path(vendor)
  end

  def send_insurance_reminder
    Vendor::InsurancePolicy::SendReminder.call(vendor)

    flash[:success] = "Insurance Policy reminder has been sent to #{vendor.name}"

    redirect_back fallback_location: vendor_path(vendor)
  end

  def archivable
    vendor
  end

  def archivable_path
    vendor_path(vendor)
  end

  def invoices
    render partial: 'management/vendors/invoices', locals: { vendor: vendor }
  end

  private

  attr_reader :vendor

  def vendors
    VendorsQuery.new(Vendor.unscoped.unarchived)
                .search
                .by_user(current_property_manager)
                .filter_email(params[:email])
                .filter_name(params[:name])
                .filter_tags(params[:tags])
                .filter_text(params.dig(:filters, :search))
                .order_with(params[:sort])
                .limit(params[:limit])
                .offset(params[:offset])
  end

  def find_vendor
    @vendor = VendorsQuery.new(Vendor.all).search
                          .by_user(current_property_manager)
                          .find(params[:id])
  end

  def persist(successful_message)
    result = Vendor::Persist.call(params)

    @vendor = result.vendor

    respond_to do |format|
      format.html do
        if result.successful?
          flash[:success] = successful_message
          redirect_to vendor_path(@vendor)
        else
          flash[:error] = result.errors
          template = vendor.new_record? ? :new : :edit
          render template, status: :unprocessable_entity
        end
      end

      format.json do
        if result.successful?
          flash.now[:success] = successful_message
          render json: @vendor.as_json
        else
          flash.now[:error] = result.errors
          render json: { errors: result.errors }, status: :unprocessable_entity
        end
      end

      format.js do
        render_ujs_errors result.errors unless result.successful?
      end
    end
  end

  def prefilled_vendor_params
    prefilled_params = params.permit(prefilled: %i[name])
    prefilled_params['prefilled']
  end
end
