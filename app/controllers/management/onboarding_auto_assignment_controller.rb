class Management::OnboardingAutoAssignmentController < Management::PortfoliosController
  before_action :set_portfolio
  before_action :require_onboarding_enhancement_enabled

  def batch_turn_on_modal
    selected_property_ids = if params.dig(:selection, :overselection) == 'true'
                              selectable_properties.pluck(:id)
                            else
                              params.dig(:selection, :ids).values
                            end

    configurations = MemberOnboarding::Configuration
                     .auto_assignable_for_properties(selected_property_ids)

    auto_assignment_onboarding_options = configurations.map do |configuration|
      [configuration.name, configuration.id]
    end

    update_bulk_selection_modal(
      :turn_on_auto_assignment_onboarding_modal,
      'management/portfolios/turn_on_auto_assignment_onboarding_modal',
      selected_property_ids,
      partial_locals: {
        auto_assignment_onboarding_options: auto_assignment_onboarding_options
      }
    )
  end

  def batch_turn_on
    selected_properties = selectable_properties.where(id: params[:selected_ids])

    result = MemberOnboarding::CreateAutoAssignments.call(
      selected_properties: selected_properties,
      onboarding: onboarding
    )

    unless result.successful?
      flash[:toast_error] = result.errors.join(', ')
      return redirect_back fallback_location: portfolio_path(@portfolio)
    end

    count = selected_properties.count
    flash[:toast_success] =
      "Auto Assignment turned on for #{count} #{'property'.pluralize(count)} successfully"

    redirect_back fallback_location: portfolio_path(@portfolio)
  end

  def batch_turn_off
    auto_assignment_onboarding_memberships = MemberOnboarding::PropertyMembership.where(
      property_id: params[:property_ids].split(','),
      enhanced: true,
      auto_assign: true
    )

    auto_assignment_onboarding_memberships.each do |membership|
      membership.update!(auto_assign: false)
    end

    count = auto_assignment_onboarding_memberships.length

    flash[:toast_success] =
      "Auto Assignment turned off for #{count} #{'property'.pluralize(count)} successfully"

    redirect_back fallback_location: portfolio_path(@portfolio)
  end

  private

  def require_onboarding_enhancement_enabled
    return if Feature.enabled?(:onboarding_enhancements, Customer.current)

    Honeybadger.notify('OnboardingAutoAssignments called without onboarding_enhancements flag')
    flash[:toast_error] = 'This action is not available when onboarding enhancements are disabled.'
    redirect_back fallback_location: portfolio_path(@portfolio)
  end

  def set_portfolio
    @portfolio = Portfolio.find(params[:portfolio_id])
  end

  def onboarding
    @onboarding ||= MemberOnboarding::Configuration.find(params[:configuration_id])
  end

  def selectable_properties
    @selectable_properties ||= PropertiesQuery.for_user(current_property_manager)
                                              .unarchived
                                              .by_portfolio(@portfolio)
  end
end
