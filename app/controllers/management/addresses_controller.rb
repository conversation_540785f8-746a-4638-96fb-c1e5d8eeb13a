class Management::AddressesController < ManagementController
  before_action :set_addressable, only: :create
  before_action :set_address, only: :update

  rescue_from ActiveRecord::RecordInvalid do |e|
    render_ujs_errors e.record.errors.full_messages
  end

  def create
    @addressable.create_address!(address_params)

    redirect_back fallback_location: root_path
  end

  def update
    @address.update!(address_params)

    redirect_back fallback_location: root_path
  end

  private

  def set_addressable
    @addressable = GlobalID::Locator.locate_signed(params[:addressable_sgid])
  end

  def set_address
    @address = GlobalID::Locator.locate_signed(params[:id])
  end

  def address_params
    params.require(:address).permit(Address::PERMISSABLE_ATTRIBUTES)
  end
end
