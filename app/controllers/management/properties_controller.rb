class Management::PropertiesController < ManagementController
  include ArchivingController

  before_action :set_property, :redirect_setup, except: :index

  def prepare_archive
    @property.archived_at = Time.zone.today
  end

  def archive
    archived_at = params.dig(:property, :archived_at)
    @property.archive!(archived_at)

    flash[:success] = 'Property Archived Successfully'
    redirect_to property_path(@property)
  rescue Archivable::ArchivingError => e
    flash.now[:error] = e.message
    render :prepare_archive
  end

  def index
    properties = PropertiesQuery
                 .new(filtered_properties).search
                 .filter_with(params[:filters])
                 .order_with(params[:sort])

    include_floorplans = params[:include_floorplans]

    properties = properties.includes(:floorplans) if include_floorplans

    respond_to do |format|
      format.html do
        @noport = true
        @properties = properties.includes(
          :address, :occupancy_count, company: :portfolio
        )

        @properties = if Feature.enabled?(:onboarding_enhancements, Customer.current)
                        @properties.includes(:auto_assign_onboarding_configuration)
                      else
                        @properties.includes(:default_member_onboarding_configuration_assignment)
                      end
      end

      format.xlsx do
        properties = properties.includes(
          :address, :occupancy_count, company: :portfolio
        )

        render xlsx: ActionTable::Properties.new(self, properties)
      end

      format.json do
        render json: properties, include_floorplans: include_floorplans
      end
    end
  end

  def show
    units = @property.units.self_unarchived.includes(:floorplan)

    @available_square_feet = units.vacant.sum(&:square_feet)
    @total_square_feet = units.sum(&:square_feet)

    units = units.includes(active_leases: :charge_schedule_entries)

    tenant_term = I18n.t('tenant_term')

    tenant_column = tenant_term.downcase.pluralize.to_sym

    @props = {
      data: serialize(units, each_serializer: UnitsTableSerializer).as_json,
      widths: {
        name: 'one',
        tenant_column => 'two',
        expires: 'two',
        rent: 'one',
        market_rate: 'one',
        floorplan: 'one',
        square_feet: 'one',
        status: 'one'
      }
    }

    return if tenant_column == :tenants

    @props[:data] = @props[:data].map do |row|
      array = row.to_a
      array[2][0] = tenant_column
      array.to_h
    end
  end

  def export
    download_id = SecureRandom.hex

    DataExportJob
      .set(wait: 0.5.seconds)
      .perform_later(
        user: current_property_manager,
        download_id: download_id,
        target: @property
      )

    follow_background_task(task_id: download_id)
  end

  def edit; end

  def update
    persist 'Property Updated Successfully'
  end

  def offer_refinance
    owner = @property.company.owners.first

    if owner&.email.blank?
      flash[:error] = 'Property must have an owner contact with an email.'
    else
      invite = Financing::Refinance::Invite.create!(
        property: @property,
        owner: owner,
        created_by: current_property_manager
      )

      Opportunities::RefinancesMailer.send_invite(invite).deliver_later

      flash[:success] = "Refinance Offer Sent to #{owner.email}"
    end

    redirect_to property_path(@property)
  end

  def update_custom_status
    status = params.dig(:property, :custom_status)

    @property.meta!(:custom_status, status)

    head :no_content
  end

  private

  def set_property
    @property = current_property_manager.properties.find(params[:id])
  end

  def redirect_setup
    redirect_to property_setup_path(@property) unless @property.setup?
  end

  def persist(successful_message)
    result = Property::Persist.call(params)

    @property = result.property

    if result.successful?
      flash[:success] = successful_message
      redirect_to property_path(@property)
    else
      flash[:error] = result.errors
      render :edit, status: :unprocessable_entity
    end
  end

  def filtered_properties
    if params[:portfolio_id]
      current_property_manager.portfolios
                              .find(params[:portfolio_id])
                              .properties
    elsif params[:company_id]
      CompaniesQuery
        .for_user(current_property_manager)
        .find(params[:company_id])
        .properties
    else
      current_property_manager.properties
    end.unarchived
  end

  def archivable
    @property
  end

  def archivable_path
    property_path(@property)
  end
end
