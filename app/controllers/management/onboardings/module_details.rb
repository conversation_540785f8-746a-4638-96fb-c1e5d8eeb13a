# rubocop:disable Layout/LineLength
module Management::Onboardings::ModuleDetails
  def self.modules
    modules = {
      information_collection: {
        name: 'Membership Information Collection',
        icon: 'address card',
        description: 'Collect information about members such as SSN, dietary restrictions, text message opt-in, etc.'
      },
      guarantor: {
        name: 'Guarantor/Guardian',
        icon: 'user',
        description: 'Collect Guarantor or Guardian Information'
      },
      membership_agreement: {
        name: 'Membership Agreement',
        icon: 'file alternate',
        description: 'Collect signatures on a membership agreement'
      },
      lease_agreement: {
        name: I18n.t('lease_agreement_term'),
        icon: 'file contract',
        description: "Generate a #{I18n.t('lease_agreement_term').downcase} and terms"
      },
      charge: {
        name: 'Charge',
        icon: 'comment dollar',
        description: 'Collect one-time or recurring charges'
      }
    }

    if Feature.enabled?(:risk_release_enrollments, Customer.current) &&
       !CustomerSpecific::Behavior.auto_enroll_damage_waiver?
      modules[:risk_release] = {
        name: 'Risk Release',
        icon: 'shield alternate',
        description: 'Add a charge for this damage protection solution'
      }
    end
    # rubocop:enable Layout/LineLength

    modules.with_indifferent_access
  end

  def self.module_form_partial(module_id)
    "management/onboardings/modules/forms/#{module_id}"
  end
end
