class Management::Onboardings::AssignmentsController < ManagementController
  PER_PAGE = 12
  before_action :set_onboarding
  before_action :set_assignments, only: %i[index]
  helper_method :initial_exclusions, :unassigned_properties, :assigned_members

  def index
    @assignments = @assignments.max_paginated(params[:page], PER_PAGE)
  end

  def new
    @members = members
               .includes(:active_onboarding_assignment, :simple_agreements)
               .max_paginated(params[:page], params[:page_size].presence || PER_PAGE)

    return if Feature.enabled?(:onboarding_enhancements, Customer.current)

    @has_existing_defaults = properties.joins(:default_member_onboarding_configuration_assignment)
    render 'legacy_new'
  end

  def assign
    @members = members
               .includes(:active_onboarding_assignment, :simple_agreements)
               .max_paginated(params[:page], params[:page_size].presence || PER_PAGE)
  end

  def create
    assignment_attributes = resolve_tenant_ids.map do |tenant_id|
      { tenant_id: tenant_id, configuration_id: @onboarding.id }
    end
    if assignment_attributes.blank?
      redirect_to new_onboarding_assignment_path(@onboarding), flash: {
        error: 'No Specific Assignees Selected. You can skip assigning individuals.'
      }
    else
      MemberOnboarding::Assignment.upsert_all assignment_attributes,
                                              unique_by: %i[tenant_id configuration_id]
      flash[:success] = 'Assigned Successfully'
      redirect_to onboardings_path
    end
  end

  def unassign_modal
    @assignment = MemberOnboarding::Assignment.find(params[:id])

    if @assignment.completed?
      flash[:error] = 'Cannot unassign a member from a completed assignment'
      return redirect_to onboarding_assignments_path(@onboarding)
    end

    render 'management/onboardings/assignments/unassign/unassign_modal', layout: false
  end

  def bulk_unassign_modal
    @selected_ids = if params.dig(:selection, :overselection) == 'true'
                      set_assignments
                      @assignments.pluck(:id)
                    else
                      params.dig(:selection, :ids).values
                    end

    update_bulk_selection_modal(
      :confirm_unassign_modal,
      'management/onboardings/assignments/unassign/bulk_unassign_modal',
      @selected_ids.uniq
    )
  end

  def bulk_unassign
    @assignments = MemberOnboarding::Assignment.where(id: params[:selected_ids])
    total_unassigned = 0

    ActiveRecord::Base.transaction do
      @assignments.each do |assignment|
        next if assignment.completed?

        total_unassigned += 1
        assignment.destroy!
      end
    end

    flash[:toast_success] =
      "#{total_unassigned} #{'member'.pluralize(total_unassigned)} successfully unassigned"
  rescue ActiveRecord::RecordNotDestroyed => e
    flash[:error] = e.record.errors.full_messages
  ensure
    redirect_back fallback_location: onboarding_assignments_path(@onboarding)
  end

  def bulk_assign_modal
    selected_ids = if params.dig(:selection, :overselection) == 'true'
                     members.pluck(:id)
                   else
                     params.dig(:selection, :ids).values
                   end

    render_bulk_assign_modal(selected_ids)
  end

  def bulk_assign
    members = Tenant.includes(:active_onboarding_assignment).where(id: params[:selected_ids])

    total_assigned = 0

    ActiveRecord::Base.transaction do
      members.each do |member|
        member.active_onboarding_assignment.destroy! if member.active_onboarding_assignment.present?
        member.create_active_onboarding_assignment(configuration: @onboarding)
        total_assigned += 1
      end
    end

    flash[:toast_success] =
      "#{total_assigned} #{'member'.pluralize(total_assigned)} successfully assigned"
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = e.record.errors.full_messages
  ensure
    if params[:redirect_path].present?
      redirect_to params[:redirect_path]
    else
      redirect_back fallback_location: onboarding_assignments_path(@onboarding)
    end
  end

  def submit_assign
    # No rows are selected
    next_page_path = onboarding_auto_assignments_path(@onboarding)
    if params[:tenant_ids].blank? && params[:overselection] == 'false'
      return redirect_to next_page_path
    end

    selected_members = if params[:overselection] == 'true'
                         members
                       else
                         members.where(id: params[:tenant_ids].split(','))
                       end

    render_bulk_assign_modal(selected_members.pluck(:id), redirect_path: next_page_path, open_modal: true)
  end

  def property_contacts
    @property = properties.find(params[:property_id])

    onboardable_members = TenantsQuery.new.search.onboardable(@property)

    already_completed_this_onboarding = MemberOnboarding::Assignment.completed.where(
      tenant: onboardable_members,
      configuration: @onboarding
    )
    deselectable_ids = onboardable_members
                       .where.not(id: already_completed_this_onboarding.select(:tenant_id))
                       .pluck(:id)

    selectable_ids = onboardable_members
                     .left_joins(:active_onboarding_assignment)
                     .where(member_onboarding_assignments: {
                              configuration_id: [@onboarding.id, nil]
                            })
                     .pluck(:id)

    sub_selection_exclusions = (
      params[:sub_selection_exclusions] ||
      initial_exclusions[@property.id] ||
        []
    ).map(&:to_i)

    filtered_members = onboardable_members
                       .filter_search(params[:search_term])
                       .max_paginated(current_page, PER_PAGE)
    render partial: 'property_contacts',
           locals: {
             property: @property,
             filtered_members: filtered_members,
             total_count: onboardable_members.count,
             deselectable_ids: deselectable_ids,
             selectable_ids: selectable_ids,
             sub_selection_exclusions: sub_selection_exclusions
           }
  end

  private

  def assigned_members
    @onboarding.decorate.assigned_members(current_property_manager)
  end

  def render_bulk_assign_modal(selected_tenant_ids, redirect_path: nil, open_modal: false)
    show_warning = MemberOnboarding::Assignment.active.exists?(tenant_id: selected_tenant_ids)
    modal_mode = show_warning ? :warning : :safe

    update_bulk_selection_modal(
      :confirm_assign_modal,
      'management/onboardings/assignments/assign/bulk_assign_modal',
      selected_tenant_ids.uniq,
      options: { open_modal: },
      partial_locals: { modal_mode:, redirect_path: }
    )
  end

  def render_bulk_assign_modal(selected_tenant_ids, redirect_path: nil, open_modal: false)
    show_warning = MemberOnboarding::Assignment.active.exists?(tenant_id: selected_tenant_ids)
    modal_mode = show_warning ? :warning : :safe

    update_bulk_selection_modal(
      :confirm_assign_modal,
      'management/onboardings/assignments/assign/bulk_assign_modal',
      selected_tenant_ids.uniq,
      options: { open_modal: },
      partial_locals: { modal_mode:, redirect_path: }
    )
  end

  def resolve_tenant_ids
    sub_selection_exclusions = JSON.parse(
      assignments_params[:sub_selection_exclusions]
    )&.compact || {}

    property_ids = (assignments_params[:property_ids] || []).compact_blank

    property_ids.map do |property_id|
      property_tenant_ids = TenantsQuery.new
                                        .search
                                        .onboardable(property_id.to_i)
                                        .pluck(:id)
      property_tenant_ids - (sub_selection_exclusions[property_id] ||
        initial_exclusions[property_id.to_i])
    end.flatten.uniq
  end

  def current_page
    params[:page] || 1
  end

  def assignments_params
    params.require(:assignments)
          .permit(
            :sub_selection_exclusions,
            property_ids: []
          )
  end

  def set_onboarding
    @onboarding = OnboardingsQuery.new
                                  .search
                                  .by_user(current_property_manager)
                                  .find(params[:onboarding_id])
  end

  def set_assignments
    @assignments = MemberOnboarding::AssignmentsQuery
                   .new
                   .search
                   .by_user(current_property_manager)
                   .by_configuration(@onboarding)
                   .includes(
                     tenant: [
                       { lease_memberships: { lease: { unit: :property } } },
                       { simple_agreement_memberships: { simple_agreement: :tags } }
                     ],
                     configuration: %i[portfolio properties member_completions]
                   )
                   .filter_with(params[:filters])
                   .order_with(params[:sort])
  end

  def members
    @members ||= TenantsQuery.new
                             .search
                             .by_user(current_property_manager)
                             .onboardable(@onboarding.onboardable_properties)
                             .filter_search(params.dig(:filters, :search))
                             .filter_onboardable_properties(params.dig(:filters, :property_id))
                             .filter_membership_tags(params.dig(:filters, :tags))
                             .filter_onboarding_assigned(params.dig(:filters, :onboarding_assigned))
                             .order_with(params[:sort])
  end

  def initial_exclusions
    @initial_exclusions ||= unassigned_properties.to_h do |property|
      onboardable_members = TenantsQuery.new.search.onboardable(property.id)
      [
        property.id,
        (onboardable_members.pluck(:id) - @onboarding.member_assignments.pluck(:tenant_id))
      ]
    end
  end

  def unassigned_properties
    @unassigned_properties ||=
      properties.where.not(id: @onboarding.properties)
  end

  def properties
    @properties ||= PropertiesQuery.new(@onboarding.portfolio.properties).search
                                   .by_user(current_property_manager)
  end
end
