class Management::OnboardingsController < ManagementController # rubocop:disable Metrics/ClassLength
  include ArchivingController
  include BulkArchivingController

  PER_PAGE = 12

  before_action :set_token, :set_member_onboarding_wizard,
                only: %i[edit update module_selection process_module_selection]

  before_action :set_onboarding_configuration,
                only: %i[show unassign copy archive assignees default_properties]

  def index
    @all_onboardings = onboardings

    @filtered_onboardings = @all_onboardings
                            .unarchived
                            .order(created_at: :desc)
                            .filter_with(params[:filters])
                            .order_with(params[:sort]).max_paginated(params[:page], PER_PAGE)

    @filtered_onboardings = @filtered_onboardings.decorate
  end

  def unassign
    tenant = Tenant.find(params[:tenant_id])
    MemberOnboarding::Assignment.find_by!(
      tenant: tenant,
      configuration: @member_onboarding_configuration
    ).destroy!

    flash[:toast_success] = 'Member successfully unassigned'
    redirect_back fallback_location: tenant.url
  rescue ActiveRecord::RecordNotFound
    flash[:error] =
      "MemberOnboarding Assignment for tenant #{tenant.name} " \
      "and onboarding #{@member_onboarding_configuration.id} not found"
    redirect_back fallback_location: onboardings_path
  end

  def assignees
    tenants = Tenant.joins(:active_onboarding_assignment)
                    .includes(simple_agreements: :property)
                    .where(active_onboarding_assignment: { configuration_id:
                      @member_onboarding_configuration.id })

    @tenants = TenantsQuery.new(tenants)
                           .search
                           .filter_search(params[:search_term])
                           .max_paginated(current_page, PER_PAGE)

    @tenants_with_properties = @tenants.map do |tenant|
      simple_agreement = tenant.simple_agreements.current.first ||
                         tenant.simple_agreements.unarchived.last
      [tenant.name, simple_agreement&.property&.name]
    end

    render partial: 'assignees',
           locals: { tenants: @tenants,
                     tenants_with_properties: @tenants_with_properties }
  end

  def default_properties
    unfiltered_properties = if Feature.enabled?(:onboarding_enhancements, Customer.current)
                              @member_onboarding_configuration.onboardable_properties
                            else
                              @member_onboarding_configuration.properties
                            end
    @properties = PropertiesQuery.new(unfiltered_properties)
                                 .search
                                 .by_user(current_property_manager)
                                 .filter_search(params[:search_term])
                                 .max_paginated(current_page, PER_PAGE)
    render partial: 'default_properties', locals: { properties: @properties }
  end

  def selectable_properties
    portfolio = Portfolio.find(params[:portfolio_id])
    @properties = PropertiesQuery.for_user(current_property_manager)
                                 .unarchived
                                 .by_portfolio(portfolio)
                                 .reorder(PropertiesQuery::BROWNSORT)
                                 .pluck(:name, :id)

    render 'selectable_properties', layout: false
  end

  def current_page
    params[:page] || 1
  end

  def new
    @member_onboarding_configuration = MemberOnboarding::Configuration.new
  end

  def show
    module_id = @member_onboarding_configuration.module_ids.first
    redirect_to onboarding_module_path(@member_onboarding_configuration, module_id)
  end

  def create
    @member_onboarding_configuration = build_configurations_from_params

    @member_onboarding_configuration.onboardable_property_ids = scoped_selected_property_ids(
      portfolio: @member_onboarding_configuration.portfolio,
      property_ids: params.dig(
        :member_onboarding_configuration,
        :onboardable_property_ids
      )&.compact_blank
    )

    unless @member_onboarding_configuration.valid?
      return render_ujs_errors @member_onboarding_configuration.errors.full_messages
    end

    token = generate_token
    member_onboarding_wizard = MemberOnboarding::Wizard.new(token: token)
    member_onboarding_wizard.insert_configuration(@member_onboarding_configuration)

    redirect_to onboarding_module_selection_path(token)
  end

  def edit
    @member_onboarding_configuration =
      @member_onboarding_wizard.build_configuration_from_attributes
    @properties_filtered = @member_onboarding_wizard.properties_filtered?
  end

  def update
    @member_onboarding_configuration = build_configurations_from_params
    @member_onboarding_configuration.portfolio_id = @member_onboarding_wizard.portfolio_id

    unless @member_onboarding_configuration.valid?
      return render_ujs_errors @member_onboarding_configuration.errors.full_messages
    end

    @member_onboarding_wizard.update_configuration(@member_onboarding_configuration)
    redirect_to onboarding_module_selection_path(@token)
  end

  def module_selection
    @selected_modules = @member_onboarding_wizard.module_ids || []
  end

  def process_module_selection
    @selected_modules = params[:selected_modules].split(',')

    if @selected_modules.empty?
      flash[:error] = 'Please select at least one module.'
      redirect_to onboarding_module_selection_path and return
    end

    @member_onboarding_wizard.upsert_selected_modules(@selected_modules)
    redirect_to @member_onboarding_wizard.module_path(@selected_modules.first)
  end

  def copy
    token = generate_token
    member_onboarding_wizard = MemberOnboarding::Wizard.new(token: token)

    @member_onboarding_configuration.name = "Copy of #{@member_onboarding_configuration[:name]}"

    # Track the original property count
    original_property_ids = @member_onboarding_configuration.onboardable_properties.pluck(:id)

    # Get the filtered property IDs based on user access
    filtered_property_ids = scoped_selected_property_ids(
      portfolio: @member_onboarding_configuration.portfolio,
      property_ids: original_property_ids
    )

    @member_onboarding_configuration.onboardable_property_ids = filtered_property_ids

    member_onboarding_wizard.insert_configuration(@member_onboarding_configuration)
    member_onboarding_wizard.upsert_selected_modules(@member_onboarding_configuration.module_ids)

    @member_onboarding_configuration.module_ids.each do |module_id|
      record = @member_onboarding_configuration.send(module_id)
      member_onboarding_wizard.upsert_module_data(record, module_id)
    end

    # Set a flag in the wizard if properties were filtered out
    if Feature.enabled?(:onboarding_enhancements, Customer.current) &&
       original_property_ids.size > filtered_property_ids.size
      member_onboarding_wizard.set_properties_filtered_flag
    end

    redirect_to edit_onboarding_path(token)
  end

  private

  def archivable
    @member_onboarding_configuration
  end

  def archivables
    onboardings
  end

  def bulk_archive_path
    bulk_archive_onboardings_path
  end

  def archivable_path
    onboardings_path(@member_onboarding_configuration)
  end

  def archivables_path
    onboardings_path
  end

  def onboardings
    @onboardings ||= OnboardingsQuery.new.search
                                     .by_user(current_property_manager)
                                     .includes(:portfolio, :properties, :member_assignments)
  end

  def set_member_onboarding_wizard
    @member_onboarding_wizard = MemberOnboarding::Wizard.new(token: @token)
  end

  def set_token
    @token = params[:onboarding_token] || params[:token]
  end

  def set_onboarding_configuration
    @member_onboarding_configuration = MemberOnboarding::Configuration.find(params[:id])
  end

  def build_configurations_from_params
    configuration = MemberOnboarding::Configuration.new(onboarding_setup_params)

    configuration.skip_module_selection_validation = true

    configuration
  end

  # Getting the explicit list of properties hereprevents issues
  # with anything scoped to property when the property list changes
  def scoped_selected_property_ids(portfolio:, property_ids:)
    return [] unless Feature.enabled?(:onboarding_enhancements, Customer.current)

    properties = PropertiesQuery.for_user(current_property_manager)
                                .unarchived
                                .by_portfolio(portfolio)
                                .reorder(PropertiesQuery::BROWNSORT)

    properties = properties.where(id: property_ids) if property_ids.present?

    properties.pluck(:id)
  end

  def onboarding_setup_params
    params.require(:member_onboarding_configuration).permit(:name, :portfolio_id, :message)
  end

  def generate_token
    SecureRandom.hex(10)
  end
end
