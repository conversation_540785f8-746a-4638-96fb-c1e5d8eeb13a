class Management::DocumentTemplatesController < ManagementController
  include DocxFilling

  before_action :set_parent

  def create
    template = Document.find(params[:template_id])

    downloaded = DocumentFilling::DocumentCache.fetch_or_download(
      document: template
    )

    output_file = Tempfile.new([template.filename.gsub(/\.*/, ''), '.docx'])
    docx_file = generate_docx(template: downloaded, output_file: output_file)

    pdf_tempfile = Tempfile.new([template.filename.gsub(/\.*/, ''), '.pdf'])
    Libreconv.convert(docx_file.path, pdf_tempfile.path)

    document = ShrineAttachment.create!(
      parent: @parent,
      upload: pdf_tempfile,
      uploaded_by: current_property_manager
    ) do |doc|
      name = [
        template.filename.gsub(/\.*/, ''),
        Time.zone.now.to_fs(:short_date),
        @parent.name
      ].join(' - ')

      doc.upload.metadata['filename'] = "#{name}.pdf"
    end

    Contact::TimelineEntry.create!(
      author: current_property_manager,
      regarding: @parent,
      kind: :document,
      body: 'Document Generated'
    )

    if params[:electronic_delivery] == '1'
      TenantDocumentsMailer.document(
        @parent,
        document,
        additional_attachment: params[:additional_attachment]
      ).deliver_later
    end

    flash[:success] = 'Document Created Successfully'

    redirect_to_parent
  end

  private

  def set_parent
    @parent = Tenant.find_by(id: params[:tenant_id]) ||
              Owner.find_by(id: params[:owner_id])

    fail ActiveRecord::RecordNotFound unless @parent
  end

  def redirect_to_parent
    path = case @parent
           when Tenant then tenant_path(@parent, anchor: '/documents')
           when Owner then owner_path(@parent, anchor: '/documents')
           end

    redirect_to path
  end

  def docx_values
    pdf_values
  end

  def pdf_values
    tenant_values.merge(lease_values).merge(metadata_values)
  end

  def tenant_values
    {
      name: @parent.name,
      first_name: @parent.first_name,
      last_name: @parent.first_name,
      email: @parent.email,
      phone: @parent.formatted_phone,
      todays_date: Time.zone.today.to_fs(:human_date),
      property: current_property
    }
  end

  def lease_values
    return {} unless @parent.is_a?(Tenant)

    lease = @parent.current_lease

    return {} unless lease

    Lease::GenerateDocument.new(lease: lease, template: nil).send(:pdf_values)
  end

  def metadata_values
    (params[:metadata]&.to_unsafe_h || {}).transform_keys do |k|
      "metadata[#{k}]"
    end
  end

  def current_property
    return nil unless @parent.is_a?(Tenant)

    @parent.current_property ||
      @parent.leases.last&.property ||
      @parent.simple_agreements.last&.property
  end
end
