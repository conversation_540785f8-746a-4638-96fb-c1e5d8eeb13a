class Management::TenantsController < ManagementController
  include ContactController

  before_action :set_tenant, except: %i[index batch_invite]

  def index
    @tenants = filtered_residents.includes(:tags)

    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def index_html
    @noport = true
    @defer_flash_notices = true
    @tenants = @tenants.page(params[:page]).per(12)
  end

  def index_xlsx
    render xlsx: ActionTable::Tenants.new(self, @tenants)
  end

  def show
    respond_to do |format|
      format.html
      format.zip { show_zip }
    end
  end

  # TODO: Remove
  def mockup
    prepend_view_path 'app/views/management/tenants/'
    super
  end

  def show_zip
    zip = nil

    DataExporting::ExportTenant.call(
      @tenant, user: current_property_manager
    ) do |directory|
      string = Zip::OutputStream.write_buffer do |zio|
        Dir[File.join(directory, '**', '**')].each do |path|
          next if path == directory

          filename = path.gsub("#{directory}/", '')

          zio.put_next_entry(filename)
          zio.write(File.read(path))
        end
      end

      zip = string.string
    end

    filename = "#{@tenant.name.parameterize}.zip"

    send_data zip, filename: filename, type: :zip
  end

  def edit
    @tenant.build_metadata if @tenant.metadata.blank?
  end

  def update
    @tenant.assign_attributes(tenant_params)

    unless params[:require_confirmation]
      @tenant.skip_reconfirmation!
      @tenant.unconfirmed_email = nil if @tenant.email_changed?
    end

    @tenant.save!

    flash[:success] = "#{I18n.t('tenant_term')} Updated Successfully."

    if @tenant.unconfirmed_email &&
       @tenant.unconfirmed_email_previously_changed?
      flash[:success] +=
        " An Email Confirmation Was Sent to #{@tenant.unconfirmed_email}."
    end

    redirect_to tenant_path(@tenant)
  rescue ActiveRecord::RecordInvalid => e
    respond_to do |format|
      format.html do
        flash[:error] = e.record.errors.full_messages
        render :edit, status: :unprocessable_entity
      end

      format.js do
        render_ujs_errors e.record.errors
      end
    end
  end

  def transactions
    render partial: 'shared/transactions', locals: { object: @tenant }
  end

  def seven_day_notice
    SevenDayNoticeJob.perform_later(current_property_manager, @tenant)
    flash[:success] = "Notice sent to #{@tenant.email}"
    redirect_to tenant_path(@tenant)
  end

  def send_invite
    result = Tenant::SendWelcomeEmail.call(@tenant)
    if result.successful?
      flash[:success] = "Invite Sent to #{@tenant.email}"
    else
      flash[:error] = result.errors
    end

    redirect_to tenant_path(@tenant)
  end

  def send_password_reset
    @tenant.send_reset_password_instructions
    flash[:success] = "Password Reset Email Sent to #{@tenant.email}"
    redirect_to tenant_path(@tenant)
  end

  def batch_invite
    tenants = if params[:overselection] == 'true'
                filtered_residents
              else
                filtered_residents.where(id: params[:selected_ids])
              end

    tenants = tenants
              .where(confirmed_at: nil)
              .where(confirmation_sent_at: [nil, ...24.hours.ago])
              .where.not(email: ['', nil])

    count = tenants.count

    tenants.each(&:send_confirmation_instructions)

    flash[:success] = "Sent #{helpers.pluralize(count, 'Invite')}"
    redirect_to tenants_path
  end

  private

  def tenant_params
    params.require(:tenant).permit(
      :first_name, :last_name, :email, :phone,
      :date_of_birth, :notes,
      :electronic_payments_disabled,
      tags: [],
      forwarding_address_attributes: Address::PERMISSABLE_ATTRIBUTES,
      insurance_policies_attributes: Insurance::Policy::PERMISSABLE_ATTRIBUTES,
      metadata_attributes: [:id, data: {}]
    )
  end

  def set_tenant
    @tenant = tenants.find(params[:id])
  end

  def tenants
    TenantsQuery.for_user(current_property_manager)
  end

  def filtered_residents
    TenantsQuery.new(tenant_relation)
                .search
                .active
                .filter_tag(params.dig(:filters, :tag))
                .filter_current_location(params.dig(:filters, :location))
                .filter_current_property_id(params.dig(:filters, :property))
                .filter_search(params.dig(:filters, :search))
                .order_with(params[:sort])
                .distinct # TODO: remove, make query filters distinct implicitly
  end

  def tenant_relation
    if params[:group]
      type, id = params[:group].split('-')
      if type == 'property'
        current_property_manager.properties.find(id).tenants
      else
        current_property_manager.portfolios.find(id).tenants
      end
    else
      tenants
    end
  end

  def workbook_filename
    "tenants_#{Time.zone.now.to_fs(:export)}.xlsx"
  end
end
