class Management::DocumentsController < ManagementController
  include DocxFilling

  def show
    file = generate_docx
    send_file file, filename: filename, type: :docx
  end

  private

  def tenant
    Tenant.find(params[:tenant_id])
  end

  def lease
    tenant.current_lease || tenant.leases.last
  end

  def pdf_values
    docx_values
  end

  def docx_values
    {
      property_address: lease.property.address.simple_address,
      tenant_names: lease.tenants.map(&:name).join(', '),
      start_date: lease.start_date.to_datetime.to_fs(:human_date),
      end_date: lease.end_date.to_datetime.to_fs(:human_date),
      unit_address: lease.unit.address.simple_address,
      unit_name: lease.unit.name
    }
  end

  def docx_template
    Down.download(template.direct_upload_url)
  end

  def template
    Document.template_options(
      property_id: lease.property.id,
      template_type: template_type
    ).last
  end

  def template_type
    params[:template_type].presence || :lease_renewal_form
  end

  def filename
    case template_type.to_s
    when 'lease_renewal_form'
      'lease_renewal_letter.docx'
    when 'preferred_lease_renewal_form'
      'preferred_lease_renewal_letter.docx'
    end
  end
end
