class Tenants::MemberOnboardingsController < TenantController # rubocop:disable Metrics/ClassLength
  include ::HasPortalMasquerading
  include ::HasPortalMessages
  layout 'tenants/member_onboarding'

  skip_before_action :require_completed_member_profile
  before_action :require_matching_wizard!, except: :clear
  before_action :set_step!, only: MemberOnboarding::Tenants::Wizard::ALL_STEP_KLASSES.map(&:id)
  before_action :hide_hamburger_menu,
                only: MemberOnboarding::Tenants::Wizard::ALL_STEP_KLASSES.map(&:id)
  helper_method :property, :onboarding, :onboarding_wizard
  delegate :cohort, :property, :onboarding, to: :onboarding_wizard

  rescue_from ActionView::Template::Error, ActiveRecord::RecordNotFound,
              with: :retreat_to_welcome

  def welcome; end

  def pass_welcome
    result = onboarding_wizard.steps['welcome'].handle(params: params, request: request)
    if result.successful?
      onboarding_wizard.advance!
      redirect_to onboarding_wizard.current_step.show_path
    else
      render_ujs_errors(result.errors)
    end
  end

  def member_profile
    @member = current_tenant
    if onboarding_wizard.steps['member_profile'].profile_attributes.present?
      @member.assign_attributes(onboarding_wizard.steps['member_profile'].profile_attributes)
    end
    @required_information = onboarding_wizard.steps['member_profile'].required_information
  end

  def create_member_profile
    result = onboarding_wizard.steps['member_profile'].handle(
      params: params, request: request
    )
    if result.successful?
      onboarding_wizard.advance!
      redirect_to onboarding_wizard.current_step.show_path
    else
      render_ujs_errors(result.errors)
    end
  end

  def guarantor_profile
    guarantor_step = onboarding_wizard.steps['guarantor_profile']
    @guarantor = guarantor_step.find_or_build_guarantor
    @required_information = guarantor_step.required_information
  end

  def create_guarantor_profile
    guarantor_step = onboarding_wizard.steps['guarantor_profile']
    result = guarantor_step.handle params: params, request: request
    if result.successful?
      onboarding_wizard.advance!
      redirect_to onboarding_wizard.current_step.show_path
    else
      flash[:error] = result.errors
      redirect_to guarantor_profile_tenants_member_onboarding_path(onboarding)
    end
  end

  def risk_management_program
    dummy_membership = Agreements::SimpleAgreement::Membership.new(
      tenant: current_tenant, role: :primary,
      simple_agreement: Agreements::SimpleAgreement.new(
        property: property,
        company: property.company,
        charge_schedule: ChargeSchedule.new
      )
    )
    @risk_release_config = onboarding.risk_release.decorate
    @installments_params = MemberOnboarding::CreateRiskReleaseEnrollment.new(
      risk_release_configuration: onboarding.risk_release,
      simple_agreement_membership: dummy_membership
    ).installments_params
  end

  def select_risk_management_program
    damage_waiver_step = onboarding_wizard.steps['risk_management_program']
    result = damage_waiver_step.handle params: params, request: request
    if result.successful?
      onboarding_wizard.advance!
      redirect_to onboarding_wizard.current_step.show_path
    else
      render_ujs_errors(result.errors)
    end
  end

  def membership_agreement
    @include_damage_waiver = onboarding.membership_agreement.include_damage_waiver?

    @electronic_signature =
      ElectronicSignatures::InlineForm.new(
        post_path:
          sign_membership_agreement_tenants_member_onboarding_path(onboarding),
        hosted_document_path:
          membership_agreement_document_tenants_member_onboarding_path(onboarding)
      )
  end

  def membership_agreement_document
    agreement_type = Agreements::AgreementType.first!

    agreement = agreement_type.simple_agreements.build(property: property)
    agreement.memberships.primary.build(tenant: current_tenant)

    tempfile = MemberOnboarding::MembershipAgreement::FillDocument.call(
      membership_agreement: agreement,
      template_document_id: onboarding.membership_agreement.membership_template.id
    )

    send_file tempfile
  end

  def sign_membership_agreement
    if onboarding.membership_agreement.include_damage_waiver? &&
       params.dig(:electronic_signature, :damage_waiver_acknowledged) != '1'
      return render_ujs_errors(
        [
          'Please acknowledge enrollment in Greek House Shield; ' \
          'you may opt out after onboarding submission.'
        ]
      )
    end

    # if at some point this does not need to be submitted after completion, it
    # needs a way of persisting whatever is needed to electronically sign the membership agreement
    if onboarding_wizard.steps['membership_agreement'].submit_after_completed?
      result = onboarding_wizard.perform_submit(params, request)
      render_ujs_errors(result.errors) && return unless result.successful?
    end

    onboarding_wizard.advance!
    redirect_to onboarding_wizard.current_step.show_path
  end

  def lease_agreement
    @lease = Lease.find(onboarding_wizard.steps['lease_agreement'].lease_id)
    @electronic_signature = @lease.electronic_signatures.find_by(
      recipient: @lease.primary_tenant
    )
    @success_redirect_key = :configurable_onboarding_next
  end

  def lease_agreement_document
    assert
    @lease = Lease.find(onboarding_wizard.steps['lease_agreement'].lease_id)
    lease_signature = @lease.electronic_signatures.find_by(
      recipient: @lease.primary_tenant
    )
    send_file Down.download(lease_signature.document_url)
  end

  def completion_summary
    return if onboarding_wizard.steps['completion_summary'].completed?

    # TODO: remove when onboarding_enhancements is completed
    completion = mark_onboarding_as_completed!

    onboarding_wizard.steps['completion_summary'].completion_id = completion.id
    current_tenant.meta!('completed_member_profile', true)
    MemberOnboarding::Tenants::Wizard.clear!(cohort, current_tenant)
  end

  def clear
    MemberOnboarding::Tenants::Wizard.clear!(cohort, current_tenant)
    redirect_to tenants_dashboard_path
  end

  def retreat_to_welcome(exception)
    Honeybadger.notify(exception)
    flash[:error] = "There was an error. You can check your forms \
and try again, or clear your onboarding and start over."
    redirect_to onboarding_wizard.steps['welcome'].show_path
  end

  private

  def require_matching_wizard!
    resolved_onboarding_id = onboarding_wizard&.cohort&.onboarding&.id
    return if resolved_onboarding_id.present? && resolved_onboarding_id.to_s == params[:id].to_s

    flash[:error] =
      if resolved_onboarding_id
        'You have been assigned to a different onboarding'
      else
        'You have been unassigned from this onboarding'
      end
    redirect_to tenants_dashboard_path
  end

  def set_step! # rubocop:disable Metrics/AbcSize
    step_id = params[:action].to_sym

    target_step = onboarding_wizard.steps[step_id]
    if target_step.visitable? && target_step.applicable?
      onboarding_wizard.current_step_id = step_id
      onboarding_wizard.persist!
      nil
    else
      flash[:error] = if target_step.completed?
                        "The '#{target_step.name}' has already been saved to your account"
                      else
                        "The '#{target_step.name}' isn't ready to complete yet"
                      end
      onboarding_wizard.current_step_id = :welcome
      onboarding_wizard.persist!
      redirect_to onboarding_wizard.steps['welcome'].show_path
    end
  end

  def mark_onboarding_as_completed!
    completion = onboarding.member_completions.create!(tenant: current_tenant)

    assignment = onboarding.member_assignments.find_by(tenant: current_tenant)

    assignment = onboarding.member_assignments.create!(tenant: current_tenant) if assignment.nil?

    assignment.complete!

    completion
  end

  def hide_hamburger_menu
    @hide_hamburger_menu = true
  end
end
