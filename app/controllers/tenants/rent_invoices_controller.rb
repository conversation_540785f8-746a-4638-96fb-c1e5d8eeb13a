class Tenants::RentInvoicesController < TenantController
  def index
    @invoices =
      invoices
      .includes(:invoice_total_payment)
      .reorder(date: :desc)
      .decorate

    @balance = if current_tenant_is_guardian?
                 current_guardian_member.balance
               else
                 current_tenant.balance
               end
  end

  def show
    @invoice = invoices.find(params[:id])

    @active_payment_plan =
      PaymentPlansQuery
      .new(@invoice.payment_plans)
      .search
      .with_current_or_upcoming_installments
      .first
  end

  delegate :ledger, to: :current_tenant

  private

  def invoices
    invoices = if current_tenant_is_guardian?
                 current_guardian_member.ledger.invoices
               else
                 ledger.invoices
               end

    if show_future_invoices?
      invoices
    else
      invoices.where(post_date: ..Time.zone.today)
    end
  end
end
