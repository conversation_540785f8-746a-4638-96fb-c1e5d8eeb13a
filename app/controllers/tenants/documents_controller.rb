class Tenants::DocumentsController < TenantController
  before_action :set_documents

  private

  def set_documents
    @documents = all_documents.sort_by { _1[:date] }.reverse
  end

  def all_documents
    all = \
      lease_documents +
      executed_lease_documents +
      simple_agreements +
      completed_inspections +
      utility_transfers +
      customer_specific_documents

    if Feature.enabled?(:public_tenant_documents, Customer.current)
      all += property_documents
      all += tenant_documents
    end

    all
  end

  def lease_documents
    name = if california? || Customer.current.home_owners_association?
             'Agreement'
           else
             'Lease'
           end

    current_tenant.leases.map do |lease|
      {
        name: name,
        date: lease.created_at,
        link: tenants_agreement_path(lease, type: 'leases')
      }
    end
  end

  def executed_lease_documents
    current_tenant.leases.filter_map do |lease|
      executed = lease.executed_lease_document

      next unless executed

      {
        name: 'Executed Document',
        date: executed.created_at,
        link: executed.expiring_url,
        target: '_blank'
      }
    end
  end

  def simple_agreements
    current_tenant.simple_agreements.map do |agreement|
      type = agreement.agreement_type

      {
        name: type.name.singularize,
        date: agreement.start_date || agreement.created_at,
        link: tenants_agreement_path(agreement, type: type.slug)
      }
    end
  end

  def completed_inspections
    current_tenant.assigned_inspections.complete.map do |inspection|
      {
        name: inspection.name,
        date: inspection.completed_at,
        link: tenants_inspection_path(inspection)
      }
    end
  end

  def utility_transfers
    current_tenant.utility_transfers.map do |transfer|
      {
        name: transfer.name,
        date: transfer.created_at,
        link: tenants_utility_transfer_path(transfer)
      }
    end
  end

  def property_documents
    current_tenant.properties.flat_map do |property|
      attachments = property.attachments + property.company.attachments

      attachments.map do |document|
        {
          name: document.filename,
          date: document.created_at,
          link: document.expiring_url,
          target: '_blank'
        }
      end
    end
  end

  def tenant_documents
    current_tenant.attachments.map do |document|
      {
        name: document.filename,
        date: document.created_at,
        link: document.expiring_url,
        target: '_blank'
      }
    end
  end

  def customer_specific_documents
    case Customer.current_subdomain
    when /^pmi/
      [
        {
          name: 'Tenant Manual',
          date: Date.new(2022, 1, 1),
          link: 'https://revela-public.s3.us-east-1.amazonaws.com/sites/pmi/tenant-manual.pdf',
          target: '_blank'
        }
      ]
    else
      []
    end
  end
end
