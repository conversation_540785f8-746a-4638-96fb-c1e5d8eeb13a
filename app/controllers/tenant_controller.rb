class TenantController < ApplicationController
  include ::HasPortalMasquerading
  include ::HasPortalMessages
  include ::PaymentMethodsController

  before_action :require_tenant
  before_action :require_completed_member_profile

  serialization_scope :current_tenant

  layout 'simple'

  rescue_from ActiveRecord::RecordNotFound, with: :resource_not_found

  helper_method :brand
  helper_method :current_property
  helper_method :current_tenant_is_guardian?
  helper_method :california?

  alias portal_messaging_user current_tenant

  protected

  def current_lease_membership
    Tenant::LeaseMembershipsQuery.current_lease_membership(
      tenant: current_tenant,
      property_id: params[:property_id]
    )
  end

  def current_simple_agreement
    Tenant::SimpleAgreementsQuery.current_simple_agreement(
      tenant: current_tenant
    )
  end

  def current_property
    current_lease_membership&.property || current_simple_agreement&.property
  end

  def audited_user
    current_admin_user || masquerading_from || current_tenant
  end

  def available_merchant_accounts
    Tenant::MerchantAccountsQuery.available_merchant_accounts(
      tenant: current_tenant,
      property_id: params[:property_id]
    )
  end

  def available_payment_methods
    current_tenant
      .payment_methods
      .select { |pm| pm.merchant_account.in?(available_merchant_accounts) }
  end

  def active_payment_plan
    plan = PaymentPlansQuery.new.search.by_tenant(current_tenant).last

    return nil unless plan

    return nil if plan.installments.map(&:payment).all?(&:present?)

    plan
  end

  helper_method :available_payment_methods, :active_payment_plan

  private

  def require_tenant
    return if current_tenant

    session[:tenant_target_url] = request.url
    redirect_to new_tenant_session_path
  end

  def onboarding_wizard
    @onboarding_wizard ||= MemberOnboarding::Tenants::Wizard.find_or_create(current_tenant)
  end

  def require_completed_member_profile
    if Feature.enabled?(:onboarding_setup, Customer.current)
      require_configured_member_onboarding_if_pending_assigned_onboarding
    elsif missing_required_member_profile?
      redirect_to tenants_member_profile_path
    end
  end

  def require_configured_member_onboarding_if_pending_assigned_onboarding
    # To be replaced by the MemberOnboarding::Assignment#completed_at
    return if completed_member_profile?

    return unless (wizard = onboarding_wizard)

    redirect_to wizard.current_step.show_path
  end

  def missing_required_member_profile?
    CustomerSpecific::Behavior.require_completed_member_profile? && !completed_member_profile?
  end

  def completed_member_profile?
    return true if current_tenant_is_guardian? && !Customer.current_subdomain.start_with?('wrp')

    current_tenant.meta(:completed_member_profile).in?(['true', true])
  end

  def brand
    @brand ||= current_property&.brand || Customer.current.brand
  end

  def resource_not_found
    flash[:error] = 'The requested item was not found.'
    redirect_to tenants_dashboard_path
  end

  def show_future_invoices?
    true
  end

  def current_tenant_is_guardian?
    current_tenant.in?(guardian_members)
  end

  def current_guardian_member
    guardian_members.first if current_tenant_is_guardian?
  end

  def guardian_members
    Array(
      current_simple_agreement&.memberships&.includes(:tenant)&.select(&:guardian?)&.map(&:tenant)
    ) + Array(
      current_lease_membership&.lease&.lease_memberships&.includes(:tenant)&.select(&:guarantor?)&.map(&:tenant)
    )
  end

  def california?
    Customer.current_subdomain.start_with?('chio-mu')
  end
end
