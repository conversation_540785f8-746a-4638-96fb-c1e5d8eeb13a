class Messaging::Emails::ReceiptsController < MessagingController
  before_action :set_receipts

  def index
    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def index_html
    @noport = true
    @receipts = @receipts.max_paginated(params[:page], 20)
  end

  def index_xlsx
    receipts = @receipts.limit(1000)

    render xlsx: ActionTable::Messaging::Emails::Receipts.new(self, receipts)
  end

  def set_receipts
    @receipts = EmailReceiptsQuery.new.search.filter_with(params[:filters])
  end
end
