class ElectronicSignaturesController < ApplicationController
  layout 'electronic_signatures'

  helper_method :brand

  # TODO: Remove
  helper_method :needs_ach_information?

  before_action :set_electronic_signature

  def show; end

  def update
    # TODO: Remove
    account_number = params.dig(:electronic_signature, :account_number)
    routing_number = params.dig(:electronic_signature, :routing_number)

    # TODO: Remove
    if needs_ach_information?
      unless account_number.present? && routing_number.present?
        return render_ujs_errors 'Please enter in your ACH information.'
      end

      unless valid_account_number?(account_number)
        return render_ujs_errors 'Please enter in a valid account number.'
      end

      unless valid_routing_number?(routing_number)
        return render_ujs_errors 'Please enter in a valid routing number.'
      end
    end

    result = ElectronicSignature::Sign.call(params: params, request: request)

    # TODO: Remove
    if result.successful? && needs_ach_information?
      CustomerSpecific::Marketplace::SetupAchAccountJob.perform_later(
        lease: @electronic_signature.document,
        account_number: account_number,
        routing_number: routing_number
      )
    end

    if redirect_target
      redirect_to redirect_target
    else
      respond_to do |format|
        format.js do
          if result.successful?
            render :success
          else
            render_ujs_errors result.errors
          end
        end
      end
    end
  end

  def document
    send_file Down.download(@electronic_signature.document_url)
  end

  private

  def set_electronic_signature
    @electronic_signature = ElectronicSignature.find_by!(uuid: params[:uuid])
  end

  def brand
    @electronic_signature.document.try(:property)&.brand || Customer.current.brand
  end

  def redirect_target
    @redirect_target ||=
      case params.dig(:electronic_signature, :success_redirect_key)
      when 'configurable_onboarding_next'
        MemberOnboarding::Tenants::Wizard.find_or_create(current_tenant)&.next_step&.show_path
      end
  end

  # TODO: Remove
  def needs_ach_information?
    Customer.current_subdomain.start_with?('mph-sandbox', 'marketplacehomes', 'wrp', 'dphie') &&
      @electronic_signature.document.is_a?(Lease) &&
      @electronic_signature.recipient == @electronic_signature.document.primary_tenant
  end

  def valid_account_number?(account_number)
    digits = digits(account_number)

    digits.size >= 4 &&
      !digits.all?(&:zero?)
  end

  def valid_routing_number?(routing_number)
    validator = BankAccount.new

    digits = digits(routing_number)

    digits.size == 9 &&
      validator.send(:routing_checksum, digits).zero? &&
      !digits.all?(&:zero?)
  end

  def digits(number_string)
    number_string.to_s.gsub(/[^0-9]/, '').chars.map(&:to_i)
  end
end
