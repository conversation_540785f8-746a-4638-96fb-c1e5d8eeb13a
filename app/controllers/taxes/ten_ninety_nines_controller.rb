class Taxes::TenNinetyNinesController < AccountingController
  helper Taxes::TenNinetyNinesHelper

  decorates_assigned :candidate, :collection

  rescue_from Taxes::TransitionError, with: :render_transition_error

  def index
    @noport = true
    @defer_flash_notices = true

    if Taxes::Nelco.account.present?
      @collection = filtered_candidate1099s
                    .for_table
                    .max_paginated(params[:page], 25)
    else
      @registration_form = Taxes::Nelco::RegistrationForm.build(
        user: current_property_manager,
        client_entity: Customer.current.client_entity
      )
      render 'unregistered'
    end
  end

  def register
    registration_form = Taxes::Nelco::RegistrationForm.from_params(
      params: params, client_entity: Customer.current.client_entity,
      user: current_property_manager
    )
    registration_form.validate!

    response = Taxes::Nelco::CreateAccount::Service.call(
      user: current_property_manager,
      form: registration_form
    )

    response.validate!

    @nelco_account = Taxes::Nelco::Account.create!(
      submitted_by: current_property_manager,
      username: registration_form.nelco_user_id,
      email: registration_form.email,
      password: registration_form.password
    )

    flash[:success] = 'Nelco Registration Submitted Successfully'
    redirect_to taxes_ten_ninety_nines_path
  rescue ActiveModel::ValidationError => e
    render_ujs_errors(e.model.errors.full_messages)
  rescue Taxes::Nelco::CreateAccount::FormError => e
    render_ujs_errors([e.message])
  end

  def skip
    Taxes::Skipping.new(selected_candidate1099s, current_property_manager).skip!
    redirect_to taxes_ten_ninety_nines_path
  end

  def unskip
    Taxes::Skipping.new(selected_candidate1099s,
                        current_property_manager).unskip!
    redirect_to taxes_ten_ninety_nines_path
  end

  def review
    task = Background::Task.new(
      user: current_property_manager,
      id: params[:background_task_id]
    )

    task.pending! do |toast|
      toast.title = 'Preparing Review'
      toast.subtitle = 'Please wait...'
    end

    Taxes::TenNinetyNines::ReviewCandidatesJob.perform_later(
      task: task,
      candidate_ids: selected_candidate1099s.pluck(:id),
      user: current_property_manager,
      file_case: params[:file_case]
    )

    background_task_started task: task
  end

  def show
    @candidate = Taxes::Candidate1099Query
                 .new.search
                 .for_modal
                 .find(params[:id])
    render partial: 'view_candidate'
  end

  def update_adjustment
    @candidate = Taxes::Candidate1099.find(params[:id])
    @irs_filing = @candidate.ensure_filing!
    adjustment_params = params.permit(:adjustment)
    @irs_filing.update!(adjustment: adjustment_params[:adjustment])
    if @candidate.preparing?
      @candidate.active_submission.update!(
        adjustment: adjustment_params[:adjustment],
        payments_total: candidate.payments_total
      )
    end
    @candidate.reload
    render partial: 'taxes/ten_ninety_nines/adjustment_form',
           locals: { adjustable: @candidate.decorate }
  end

  def selected_candidate1099s
    @selected_candidate1099s ||= \
      if params[:overselection] == 'true'
        filtered_candidate1099s
      else
        Taxes::Candidate1099Query.new(
          Taxes::Candidate1099.where(id: params[:ids])
        ).search
      end
  end

  def value_filters
    return { year: Taxes.default_year } unless params[:filters]

    params
      .require(:filters)
      .permit(:status, :form_name, :year, :payer)
      .reverse_merge(year: Taxes.default_year)
  end

  def issues_filters
    params.dig(:filters, :issue) || []
  end

  def filtered_candidate1099s
    @filtered_candidate1099s ||= \
      Taxes::Candidate1099Query
      .new.search
      .where(value_filters)
      .filter_issues(*issues_filters)
      .filter_search(params.dig(:filters, :search))
  end

  def render_transition_error(exception)
    flash[:error] = exception.message
    redirect_to taxes_ten_ninety_nines_path
  end
end
