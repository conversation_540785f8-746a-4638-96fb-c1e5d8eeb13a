module Reports::V3::FilterContext
  def filters
    OpenStruct.new(default_filter_values).tap do |struct|
      params.fetch(:filters, {}).each do |key, value|
        struct[key] = case key
                      when 'start_date', 'end_date', 'date',
                           'period', 'year' then Date.parse(value)
                      else
                        value
                      end
      end
    end.tap do |struct|
      if struct.period
        struct.start_date = struct.period.beginning_of_month.to_date
        struct.end_date = struct.period.end_of_month.to_date
      end

      # TODO: Remove. This is here to artificially clamp owner statement dates.
      if defined?(report_user) && report_user.is_a?(Owner)
        clamping = Reports::OwnerDateClamping.new(owner: report_user)
        min_date = clamping.minimum_date
        max_date = clamping.maximum_date

        if min_date || max_date
          struct.start_date = struct.start_date&.clamp(min_date, max_date)
          struct.end_date = struct.end_date&.clamp(min_date, max_date)
          struct.date = struct.date&.clamp(min_date, max_date)
        end

        if Customer.current_subdomain.start_with?(
          'gebrael', 'marketplacehomes', 'mph-sandbox', 'alever'
        )
          struct.basis ||= 'cash'
        end
      end
    end.tap do |struct|
      struct[:describe] = make_filters_description(struct)
    end
  end

  def default_filter_values
    {}
  end

  def scope
    if params.dig(:filters, :property_id).present?
      properties = Property.where(id: params.dig(:filters, :property_id))
      OpenStruct.new(
        describe: properties.first.name,
        properties: properties
      )
    else
      properties = if current_property_manager.top_level?
                     nil
                   else
                     current_property_manager.properties
                   end

      description = if current_property_manager.top_level?
                      'All Properties'
                    elsif properties.length == 1
                      properties.first.name
                    else
                      'Available Properties'
                    end

      OpenStruct.new(describe: description, properties: properties)
    end
  end

  def make_filters_description(struct)
    description = []

    # Only needed for legacy OpenStruct filters,
    # Reports::V3::Scope already includes the appropriate string in #description
    if scope.is_a?(OpenStruct) && struct.context_id.present?
      context = GlobalID::Locator.locate_signed(struct.context_id)

      fail ActiveRecord::RecordNotFound unless context

      description << context.name
    end

    if struct.employee_id.present?
      description << PropertyManager.find(struct.employee_id).name
    end

    description << Owner.find(struct.owner_id).name if struct.owner_id.present?

    if struct.activity_type.present?
      types = Array(struct.activity_type).map(&:titleize).map(&:pluralize)
      description += types
    end

    if struct.period.present?
      description << "#{struct.period.strftime('%B %Y')} Period"
    elsif struct.start_date.present? && struct.end_date.present?
      start = struct.start_date.strftime(Reports::V3::DATE_FORMAT)
      stop = struct.end_date.strftime(Reports::V3::DATE_FORMAT)
      description << "Dates from #{start} to #{stop}"
    elsif struct.start_date.present?
      start = struct.start_date.strftime(Reports::V3::DATE_FORMAT)
      description << "Dates from #{start}"
    elsif struct.end_date.present?
      stop = struct.end_date.strftime(Reports::V3::DATE_FORMAT)
      description << "Dates to #{stop}"
    elsif struct.date.present?
      date = struct.date.strftime(Reports::V3::DATE_FORMAT)
      description << "as of #{date}"
    elsif struct.year.present?
      description << "Year #{struct.year.year}"
    end

    if struct.tag.present?
      tag = Tag.find_by(id: struct.tag)&.tag
      description << "Tagged #{tag}" if tag.present?
    end

    description << "Type #{struct.kind.humanize}" if struct.kind.present?

    description << struct.status.titleize if struct.status.present?

    description << struct.archived.titleize if struct.archived.present?

    description << Lease.find(struct.lease_id).name if struct.lease_id.present?

    if struct.parking_lot_id.present?
      description << ParkingLot.find(struct.parking_lot_id).name
    end

    description
  end
end
