module InitialConfirmation<PERSON>ingerprintController
  def initialize_fingerprint!(account)
    unless Feature.enabled?(:require_confirmed_fingerprint, Customer.current)
      return
    end

    fail_unless_confirmed_and_fingerprints_empty!(account)

    ip_address = request.remote_ip
    user_agent = request.user_agent

    User::LoginFingerprint.create!(
      account: account,
      ip_address: ip_address,
      user_agent: user_agent,
      confirmed_at: Time.zone.now
    ) do |fingerprint|
      fingerprint.customer = Customer.current unless fingerprint.public_schema?
    end
  end
end

private

def fail_unless_confirmed_and_fingerprints_empty!(account)
  unless account.confirmed?
    fail 'Trying to initialize fingerprint on an unconfirmed account'
  end

  return if account.login_fingerprints.empty?

  fail 'Trying to initialize fingerprint, but a fingerprint already exists'
end
