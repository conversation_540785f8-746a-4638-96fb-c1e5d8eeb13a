module UjsControllerHelpers
  def render_ujs_errors(errors)
    render 'shared/ujs/errors',
           format: :js, layout: nil,
           status: :unprocessable_entity,
           locals: { errors: Array(errors) }
  end

  def background_task_started(task:)
    # TODO: Respond with J<PERSON> that shows a pending toast, so that the user has
    # visual feedback in the case that the websocket did not already do so
    head :no_content
  end

  def follow_background_task(task_id:, redirect_to: nil)
    render 'shared/ujs/background_task',
           format: :js, layout: nil,
           locals: { task_id: task_id, redirect_to: redirect_to }
  end

  def update_bulk_selection_modal(id, partial, selected_ids, options: {}, partial_locals: {})
    Thread.current[:bulk_selection_ids] = selected_ids
    options = default_options.deep_merge(options)

    render 'shared/ujs/update_bulk_selection_modal',
           format: :js, layout: nil,
           locals: { id:, options:, partial:, partial_locals: }
  ensure
    Thread.current[:bulk_selection_ids] = nil
  end

  def default_options
    {
      open_modal: false
    }
  end
end
