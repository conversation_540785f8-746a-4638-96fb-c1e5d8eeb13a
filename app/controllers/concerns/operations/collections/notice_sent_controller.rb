module Operations::Collections::NoticeSentController
  def index
    @ledgers =
      ledgers
      .includes(:primary_tenant, property: :address)
      .order_with(params[:sort])

    respond_to do |format|
      format.html { index_html }
      format.xlsx { index_xlsx }
    end
  end

  def modal
    selected_ids = if params.dig(:selection, :overselection) == 'true'
                     ledgers.pluck(:id)
                   else
                     params.dig(:selection, :ids).values
                   end

    update_bulk_selection_modal(
      :move_to_evicting_modal,
      modal_url,
      selected_ids
    )
  end

  def create
    selected_ledgers = ledgers.where(id: params[:selected_ids])

    timestamp = Time.zone.now

    bulk_insert_params = selected_ledgers.map do |ledger|
      ledger_params(ledger, timestamp)
    end

    result = Collections::Eviction.insert_all(bulk_insert_params)

    ids = result.rows.flatten
    Collections::Eviction::BulkAfterCreateJob.perform_later(ids)

    # Eviction status changed
    Accounting::Ledger::Persisted.refresh!

    count = ids.count
    things = 'Ledger'.pluralize(count)

    flash[:success] = "#{count} #{things} Moved Successfully"
    redirect_to operations_collections_evicting_index_path
  end

  private

  def index_html
    @ledgers = @ledgers.page(params[:page]).per(12)
  end

  def index_xlsx
    render xlsx: ActionTable::Collections::NoticeSent.new(self, @ledgers)
  end

  def ledger_params(ledger, timestamp)
    {
      created_by_id: current_property_manager.id,
      created_at: timestamp,
      updated_at: timestamp,
      overdue_balance_cents: ledger.overdue_resident_balance_cents,
      lease_id: ledger.ledgerable_id
    }
  end
end
