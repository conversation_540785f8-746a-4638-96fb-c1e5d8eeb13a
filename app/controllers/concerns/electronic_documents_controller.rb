module ElectronicDocumentsController
  extend ActiveSupport::Concern

  attr_reader :selected_templates

  included do
    before_action :set_selected_templates,
                  :assign_metadata,
                  only: %i[new metadata_fields create]

    helper_method :available_document_templates, :electronic_signable
  end

  def new; end

  def metadata_fields
    render partial: 'electronic_documents/packet_form/embedded_metadata_fields'
  end

  def create
    if selected_templates.none?
      return render_ujs_errors 'Select at least one document template.'
    end

    if electronic_signable.is_a?(Lease::Addendum)
      addendum_attributes = params.dig(:document, :lease_addendum).permit(
        :name, :start_date, :end_date
      )

      electronic_signable.assign_attributes(addendum_attributes)
    end

    tempfile = case electronic_signable
               when Lease
                 Lease::GenerateDocumentPacket.call(
                   lease: electronic_signable,
                   templates: selected_templates
                 )
               when Lease::Addendum
                 Lease::Addendum::GenerateDocumentPacket.call(
                   addendum: electronic_signable,
                   templates: selected_templates
                 )
               when Agreements::SimpleAgreement
                 Agreements::SimpleAgreement::GenerateDocumentPacket.call(
                   agreement: electronic_signable,
                   templates: selected_templates
                 )
               when ManagementContract
                 ManagementContract::GenerateDocument.call(
                   management_contract: electronic_signable,
                   template: selected_templates.first
                 )
               else
                 fail "Unable to generate document for '#{electronic_signable}'"
               end

    save_document(tempfile)
    save_document_pdf(tempfile)

    electronic_signable.after_document_generation

    flash['success'] = \
      'Document generated successfully. Please review the document carefully before sending for signatures.'

    redirect_to electronic_signable.url
  rescue Down::TimeoutError => e
    Honeybadger.notify(e)
    render_ujs_errors 'Timed-out while preparing documents. Please try again.'
  rescue Zip::Error => e
    Honeybadger.notify(e)
    render_ujs_errors 'Unable to parse docx files. Please ensure all templates are properly formatted docx files and try again.'
  rescue Errno::ENOENT => e
    Honeybadger.notify(e)
    render_ujs_errors 'Unable to prepare all template documents. Please try again.'
  end

  protected

  def electronic_signable
    @electronic_signable
  end

  delegate :available_document_templates, to: :electronic_signable

  private

  def set_selected_templates
    @selected_templates = \
      Document.where(id: params.dig(:document, :template_ids))
  end

  def assign_metadata
    # Assign optional metadata metadata
    additional_fields = params.dig(:document, :additional_fields)

    return unless electronic_signable.is_a?(HasMetadata)

    electronic_signable.build_metadata unless electronic_signable.metadata

    Hash(additional_fields&.to_unsafe_h).each do |key, value|
      electronic_signable.metadata.data[key] = value.presence
    end
  end

  def save_document(tempfile)
    base_template_type = if electronic_signable.is_a?(Lease::Addendum)
                           electronic_signable.base_template_type # TODO: Always?
                         else
                           electronic_signable.class.name.demodulize.underscore
                         end

    template_type = "unexecuted_#{base_template_type}_template"

    Document.new(
      parent: electronic_signable,
      upload: tempfile,
      override_content_type: Mime::Type.lookup_by_extension(:docx).to_s,
      template_options: {
        template_type: template_type,
        lease_id: (electronic_signable.id if electronic_signable.is_a?(Lease)),
        template_ids: params[:template_ids]
      }
    ).tap do |doc|
      doc.save!
      doc.update!(direct_upload_url: doc.upload.url)
    end
  end

  def save_document_pdf(packet_tempfile)
    tempfile = Tempfile.new

    packet_tempfile.open.rewind
    without_tags = DocxFilling.remove_signature_tags(packet_tempfile)
    Libreconv.convert(without_tags.path, tempfile.path)

    base_template_type = if electronic_signable.is_a?(Lease::Addendum)
                           electronic_signable.base_template_type # TODO: Always?
                         else
                           electronic_signable.class.name.demodulize.underscore
                         end

    template_type = "unexecuted_#{base_template_type}"

    Document.new(
      parent: electronic_signable,
      upload: tempfile,
      override_content_type: Mime::Type.lookup_by_extension(:pdf).to_s,
      template_options: {
        template_type: template_type,
        lease_id: (electronic_signable.id if electronic_signable.is_a?(Lease))
      }
    ).tap do |doc|
      doc.upload_file_name = electronic_signable.signature_file_name(false)
      doc.save!
      doc.update!(direct_upload_url: doc.upload.url)
    end
  end
end
