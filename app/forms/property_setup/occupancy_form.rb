class PropertySetup::OccupancyForm < Reform::Form
  include Composition

  property :reason, on: :downtime
  property :down_start_date, default: Time.zone.today, on: :downtime, from: :start_date
  property :down_end_date, on: :downtime, from: :end_date
  property :note, on: :downtime

  property :start_date, on: :lease
  property :end_date, on: :lease
  property :executed_at, on: :lease

  property :deposit, virtual: true, on: :lease

  collection :lease_memberships, on: :lease do
    property :move_in_date
    property :tenant do
      property :first_name
      property :last_name
      property :email
      property :phone
      collection :tags
    end
  end

  collection :charge_schedule_entries,
             from: :entries,
             on: :charge_schedule,
             populate_if_empty: ChargeSchedule::Entry do
    property :name, default: 'Monthly Rent'
    property :amount
    property :recurring, default: true
  end

  def deposit
    Money.zero
  end

  def lease_memberships
    super.take(1)
  end

  def persisted?
    model[:lease].persisted?
  end

  def new_record?
    model[:lease].new_record?
  end
end
