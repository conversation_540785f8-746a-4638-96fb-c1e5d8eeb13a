ActiveAdmin.register_page 'Onboarding Tools' do
  menu parent: "\u{1f916} System"

  content do
    panel 'Mark As Setup' do
      ul do
        li do
          link_to 'Setup All',
                  admin_onboarding_tools_setup_everything_path,
                  method: :post
        end

        li do
          link_to 'Setup All Portfolios',
                  admin_onboarding_tools_setup_portfolios_path,
                  method: :post
        end

        li do
          link_to 'Setup All Entities',
                  admin_onboarding_tools_setup_entities_path,
                  method: :post
        end

        li do
          link_to 'Setup All Properties',
                  admin_onboarding_tools_setup_properties_path,
                  method: :post
        end
      end
    end

    panel 'Occupancy Counts' do
      link_to 'Refresh Occupancy Counts',
              admin_onboarding_tools_refresh_occupancy_counts_path,
              method: :post
    end

    panel 'Member Profiles' do
      link_to 'Reset All "Member Profile Completed" Statuses',
              admin_onboarding_tools_reset_completed_member_profiles_path,
              method: :post
    end
  end

  # rubocop:disable Rails/SkipsModelValidations
  page_action :setup_everything, method: :post do
    Portfolio.update_all(setup: true)
    Company.update_all(setup: true)
    Property.update_all(setup: true)
    redirect_to admin_onboarding_tools_path, notice: 'Everything Marked as Setup'
  end

  page_action :setup_portfolios, method: :post do
    Portfolio.update_all(setup: true)
    redirect_to admin_onboarding_tools_path, notice: 'Portfolios Marked as Setup'
  end

  page_action :setup_entities, method: :post do
    Company.update_all(setup: true)
    redirect_to admin_onboarding_tools_path, notice: 'Entities Marked as Setup'
  end

  page_action :setup_properties, method: :post do
    Property.update_all(setup: true)
    redirect_to admin_onboarding_tools_path, notice: 'Properties Marked as Setup'
  end
  # rubocop:enable Rails/SkipsModelValidations

  page_action :refresh_occupancy_counts, method: :post do
    RefreshOccupancyCountsJob.perform_later

    redirect_to admin_onboarding_tools_path, notice: 'Refresh Triggered, Check in a Few Minutes'
  end

  page_action :reset_completed_member_profiles, method: :post do
    key = 'completed_member_profile'

    ActiveRecord::Base.transaction do
      Metadata.where(parent_type: 'Tenant').find_each do |metadata|
        if metadata.data.key?('completed_member_profile')
          metadata.data.delete(key)
          metadata.save!
        end
      end
    end

    redirect_to admin_onboarding_tools_path, notice: 'Member Profile Completed Statuses Reset'
  end
end
