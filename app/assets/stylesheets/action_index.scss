.action-index {
  .search-bar-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .search-bar {
      width: 100%;

      .with-selection {
        margin-top: 0;
      }

      .no-selection .menu .item {
        .icon {
          color: #666;
        }

        .default.text {
          color: #999;
        }
      }

      .ui.menu .item > .ui.input input {
        padding-top: 0;
        padding-bottom: 0;

        &::placeholder {
          color: #999;
        }
      }

      .ui.menu .item {
        &.search,
        &.calendar {
          max-width: 11rem;
        }

        &:hover,
        &.focus {
          background-color: rgba(0, 0, 0, .03);
        }
      }
    }

    .quick-actions {
      padding-left: 1em;

      .inline {
        display: flex;
        align-items: center;
        height: 100%;

        .ui.button:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .multiple-selection {
    .scrolling.menu {
      width: 100% !important;

      .ui.checkbox {
        display: block;
      }
    }

    .menu {
      &.visible {
        display: flex !important;

        &.finished {
          animation: none !important;
        }
      }

      .selected-items {
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid rgba(34, 36, 38, 0.1);
      }

      .ui.checkbox {
        margin: 0;
        text-align: left;
        font-size: 1em !important;
        padding: .78571429em 1.14285714em !important;
        background: 0 0 !important;
        color: rgba(0, 0, 0, .87) !important;
        text-transform: none !important;
        font-weight: 400 !important;
        box-shadow: none !important;
        transition: none !important;

        &:hover {
          background: rgba(0, 0, 0, .05) !important;
          color: rgba(0, 0, 0, .95) !important;
        }

        &.checked {
          label {
            &:before {
              background: #f05a2a !important;
              border-color: #f05a2a !important;
            }

            &:after {
              color: #fff !important;
            }
          }
        }
      }
    }

    .pills {
      display: flex;
      flex-wrap: wrap;

      .ui.label {
        margin: 0 1rem .25rem 0;
      }
    }

    &.filtered {
      .text {
        padding-right: 1.5rem;
      }

      .selected.count {
        width: 1.5rem;
        height: 1.5rem;
        background-color: #f05a2a;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        position: absolute;
        right: 2rem;
      }
    }

    &:not(.filtered) {
      .selected.count {
        display: none;
      }
    }
  }
}
