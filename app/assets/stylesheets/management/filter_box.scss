.filter.box {
  .ui.accordion.on {
    border-bottom: 1px solid #d4d4d5;

    .ui.form {
      .fields {
        margin: 0 -.5em 1em;
      }
    }
  }

  .bottom.attached.filter.button {
    background-color: #fcfcfc;
    position: absolute;
    right: 0;
    z-index: 5;

    // Prevent ugly basis button shadow
    box-shadow: 0 0 0 1px rgba(34,36,38,.15);

    .icon {
      transition: transform .3s ease-in-out;
      //transition-delay: .2s;
    }

    &.on .icon {
      transform: rotate(180deg);
    }

    &.off .icon {
      transform: rotate(0deg);
    }

    &:hover {
      background-color: #ffffff;
    }
  }
}
