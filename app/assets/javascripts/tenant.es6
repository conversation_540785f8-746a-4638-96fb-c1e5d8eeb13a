//= require jquery
//= require rails-ujs
//= require turbolinks
//= require semantic.min
//= require lodash
//= require tenant-webpack-bundle
//= require datefield
//= require react_ujs
//= require semantic_formbuilder
//= require modernizer
//= require modals
//= require magnific-popup
//= require esignatures-webpack-bundle
//= require signatures

$(window).on('turbolinks:load', () => {
  $('.signature').each(function () {
    const input = $(this).find('input')[0];
    const canvas = $(this).find('canvas')[0];
    const value = $(input).val();

    const drawLine = () => {
      const padding = 16;
      const context = canvas.getContext('2d');
      context.lineWidth = 0.5;

      context.beginPath();
      context.moveTo(padding, 120 - padding);
      context.lineTo(480 - padding, 120 - padding);
      context.stroke();

      context.beginPath();
      context.moveTo(padding + 4, 120 - padding - 12);
      context.lineTo(padding + 12, 120 - padding - 2);
      context.stroke();

      context.beginPath();
      context.moveTo(padding + 4, 120 - padding - 2);
      context.lineTo(padding + 12, 120 - padding - 12);
      context.stroke();
    };

    // var ratio = Math.max(window.devicePixelRatio || 1, 1);
    // canvas.width = canvas.offsetWidth * ratio;
    // canvas.height = canvas.offsetHeight * ratio;
    // canvas.getContext('2d').scale(ratio, ratio);

    const pad = new SignaturePad(canvas, {
      backgroundColor: 'rgb(251, 251, 249)',
      penColor: 'rgb(13, 14, 60)',
      onEnd() {
        const data = pad.toDataURL();
        $(input).val(data);
      },
    });

    pad.clear();
    pad.fromDataURL(value);
    drawLine();

    $(this).find('button').on('click', () => {
      pad.clear();
      $(input).val(null);
      drawLine();
    });
  });
});
