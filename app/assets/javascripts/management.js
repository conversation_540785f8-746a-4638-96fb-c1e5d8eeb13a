//= require jquery
//= require rails-ujs
//= require jquery.address.min
//= require turbolinks
//= require semantic.min
//= require URI
//= require d3
//= require datefield
//= require lodash
//= require action_cable
//= require main-webpack-bundle
//= require react_ujs
//= require cocoon
//= require semantic_formbuilder
//= require turbolinks_preserve_scroll
//= require dragula/dist/dragula.js
//=
//= require tunisia_customer_tokens
//= require filters
//= require tabs
//= require reports
//= require pulse

$(document).on('turbolinks:load', function () {
  $('.ui.progress').progress({ showActivity: false });
});

$(document).on('turbolinks:before-cache', function () {
  // Remove flash notices before turbolinks cache
  $('.ui.flash.message').remove();
  $('.top.center.ui.toast-container .toast-box').remove();

  // Remove popups
  $('.ui.popup').removeClass('visible');

  // Close sidebars
  $('#schedule-report-sidebar').removeClass('visible');
  $('.pusher').removeClass('dimmed');

  // Hide modals
  $('.ui.modals').css('opacity', '0');

  // Show report items
  $('.reports-list .item').css('display', 'list-item');

  // Hide tray windows
  $('.tray-window').css('display', 'none');
});

window.modalDefaults = {
  inverted: true,
  duration: 200,
  transition: 'fade up',
  offset: 200,
  allowMultiple: true,
  onShow: function () {
    initializeSemanticFields();

    this.dispatchEvent(new CustomEvent('modal:show'));
  },
};
