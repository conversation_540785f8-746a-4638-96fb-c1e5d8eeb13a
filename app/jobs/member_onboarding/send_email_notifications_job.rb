class MemberOnboarding::SendEmailNotificationsJob < ApplicationJob
  queue_as :within_one_day

  def perform
    return unless Feature.enabled?(:member_onboarding_email_notifications, Customer.current)

    to_notify.each do |assignment|
      if assignment.notified_at.present?
        MemberOnboardingsMailer.remind_assignment(assignment:).deliver_later
      else
        MemberOnboardingsMailer.notify_assignment(assignment:).deliver_later
      end

      assignment.update!(notified_at: Time.zone.now)
    end
  end

  private

  def to_notify
    # This is to prevent sending notifications for all old assignments
    # TODO: Can be cleaned up after July 12, 2025
    earliest_notification_date = [Date.new(2025, 6, 1), 45.days.ago].max

    notifiable_pending_assignments = MemberOnboarding::Assignment
                                     .active
                                     .where(created_at: earliest_notification_date..)

    notifiable_pending_assignments.where(notified_at: [nil, ...7.days.ago.end_of_day])
  end
end
