class Notifications::ApprovalNotificationsJob < ApplicationJob
  queue_as :default

  # Approvables are often destroyed before this job is performed.
  discard_on ActiveJob::DeserializationError

  attr_reader :approvable

  def perform(approvable)
    @approvable = approvable

    approval_rules.select do |rule|
      # Skip completed rules
      next if approvals.exists?(rule: rule)

      # Skip missing dependencies
      next if rule.dependency && !approvals.exists?(rule: rule.dependency)

      approvers = rule.approvers

      # TODO: Use approvable_by? here?
      if approvable.is_a?(Invoice)
        approvers = approvers.select do |approver|
          next false if approver.archived?

          next true unless approver.is_a?(PropertyManager)

          next true if approver.top_level?

          targets = [approver.companies, approver.properties].flatten

          approvable.buyer.in?(targets) || approvable.seller.in?(targets)
        end
      end

      approvers.each { |approver| notify_approver(approver) }
    end
  end

  private

  def notify_approver(approver)
    return unless approver.notification_preferences.notify_approval_required?

    return if approver.notifications.exists?(resource: approvable)

    routes = Rails.application.routes.url_helpers
    subdomain = Customer.current_subdomain

    link = case approver
           when PropertyManager
             routes.accounting_payables_invoice_url \
               approvable, subdomain: subdomain
           when Owner
             if approvable.is_a?(Maintenance::Estimate)
               request = approvable.approval_requests.find_by \
                 approver: approver
               routes.owners_approval_request_url \
                 request.token,
                 subdomain: subdomain
             else
               routes.owners_invoice_approvals_url \
                 subdomain: subdomain
             end
           end

    approver.notifications.create!(
      resource: approvable,
      kind: 'approval_required',
      title: 'An item requires your approval',
      description: approvable.description,
      link: link
    )
  end

  def approval_rules
    # TODO: any action
    Approvals::Rule.where(action: :invoice_payment).select do |rule|
      approvable.in?(rule.approvables)
    end
  end

  delegate :approvals, to: :approvable
end
