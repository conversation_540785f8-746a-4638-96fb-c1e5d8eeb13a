class Agreements::GenerateDocumentsJob < Downloads::BackgroundDownloadJob
  def perform(user:, download_id:, agreements:, document:)
    @agreements = agreements
    @document = document
    @template = Down.download(@document.direct_upload_url)

    super(user: user, download_id: download_id) do
      ShrineDownload.create!(upload: make_zip) do |attachment|
        attachment.upload.metadata['filename'] = zip_filename
      end
    end
  end

  private

  attr_reader :agreements, :document, :template

  class Generator
    include DocxFilling

    def initialize(agreement:, template:)
      @agreement = agreement
      @template = template
    end

    attr_reader :agreement, :template

    def document_pdf
      docx = generate_docx(template: template, values: pdf_values)

      pdf_tempfile = Tempfile.new

      Libreconv.convert(docx.path, pdf_tempfile.path)

      pdf_tempfile.read
    end

    def pdf_values
      todays_date = Time.zone.today

      info = [
        agreement.borrower.name,
        agreement.property.name,
        "Loan ##{agreement.loan_number}"
      ].join(' | ')

      address = agreement.borrower.address

      lines = [
        agreement.borrower.name,
        address.line_one,
        address.line_two,
        address.city_state_zip
      ].compact_blank

      values = {
        info: info,
        line_one: lines[0],
        line_two: lines[1],
        line_three: lines[2],
        line_four: lines[3]
      }

      next_due_date = todays_date.change(day: agreement.first_payment_date.day)
      next_due_date += 1.month if next_due_date.past?

      values.merge!(
        todays_date: todays_date,
        'loan.number' => agreement.loan_number,
        'loan.borrower.name' => agreement.borrower.name,
        'loan.borrower.address.street_address' => agreement.borrower.address.street_address,
        'loan.borrower.address.city_state_zip' => agreement.borrower.address.city_state_zip,
        'loan.property.address.street_address' => agreement.property.address.street_address,
        'loan.property.name' => agreement.property.name,
        'loan.installment_amount' => agreement.installment_amount.format,
        'loan.next_due_date' => next_due_date.to_fs(:short_date),
        'loan.tags' => agreement.tags.map(&:tag).join(', ')
      )

      values
    end
  end

  def make_zip
    string = Zip::OutputStream.write_buffer do |zio|
      agreements.each do |agreement|
        filename = document_filename(agreement)
        zio.put_next_entry(filename)

        generator = Generator.new(agreement: agreement, template: template)
        pdf = generator.document_pdf
        zio.write(pdf)
      end
    end

    string.rewind

    string
  end

  def zip_filename
    'agreement_documents.zip'
  end

  def document_filename(agreement)
    filename = ActiveStorage::Filename.new(document.filename)

    "#{filename.base}_#{agreement.id}#{filename.extension_with_delimiter}"
  end
end
