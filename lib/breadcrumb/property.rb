class Breadcrumb::Property < Breadcrumb::Portfolio
  def sections
    super + [portfolio_link, subdivision_link]
  end

  def identity
    {
      text: property.name,
      link: property_path(property)
    }
  end

  private

  def portfolio_link
    if user.portfolios.many?
      {
        text: portfolio.name,
        link: portfolio_path(portfolio)
      }
    elsif user.properties.length > 1 || user.top_level?
      {
        text: 'Properties',
        link: properties_path
      }
    end
  end

  def subdivision_link
    return unless subdivision.present?

    {
      text: subdivision,
      link: portfolio_path(portfolio, filters: { subdivision: subdivision })
    }
  end

  alias property resource

  delegate :portfolio, :subdivision, to: :property
end
