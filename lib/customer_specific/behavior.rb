module CustomerSpecific::Behavior
  extend self

  def respond_to_missing?(method_name, include_private = false)
    all_keys.include?(method_name) || super
  end

  def method_missing(method_name, *args, &)
    if all_keys.include?(method_name)
      subdomain = case args.first
                  when nil then Customer.current_subdomain
                  when String then args.first
                  else args.first.subdomain
                  end

      data = customer_specific_behavior[method_name]

      if method_name.to_s.end_with?('?')
        fail 'Expected array of subdomains for boolean behavior' unless data.is_a?(Array)

        data.include?(subdomain)
      else
        fail 'Expected hash by subdomain for key value attributes' unless data.is_a?(Hash)

        data[subdomain.to_sym]
      end
    else
      super
    end
  end

  private

  def all_keys
    @all_keys ||= customer_specific_behavior.keys
  end

  def customer_specific_behavior
    Rails.configuration.customer_specific_behavior
  end
end
