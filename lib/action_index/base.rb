class ActionIndex::Base
  extend Service

  include ActionView::Context
  include ActionView::Helpers
  include Rails.application.routes.url_helpers

  include ActionIndex::Buttons
  include ActionIndex::Dates
  include ActionIndex::Dropdowns
  include ActionIndex::FilterMenu
  include ActionIndex::NoResults
  include ActionIndex::Searches
  include ActionIndex::SelectionMenu

  attr_reader :view_context, :collection, :partial, :user, :quick_actions_partial

  def initialize(view_context, collection:, partial:, user:, quick_actions_partial: nil)
    @view_context = view_context
    @user = user
    @partial = partial
    @collection = collection
    @quick_actions_partial = quick_actions_partial
  end

  def call(&)
    render(&)
  end

  protected

  def filter_items
    []
  end

  def selection_items
    []
  end

  def supports_overselection?
    false
  end

  def search_debounce_delay
    100
  end

  private

  def render(&)
    data = { controller: stimulus_controllers, base_path: base_path }

    data[:filtered_count] = collection.total_count if supports_overselection?

    data[:search_debounce_delay] = search_debounce_delay

    tag.div(id: root_identifier, class: 'action-index', data: data) do
      render_bar + render_content + render_no_results
    end
  end

  def render_content
    view_context.assign({ permitted_any_selection_items: permitted_any_selection_items? })

    view_context.render \
      partial: partial[:path],
      locals: partial[:locals]
  end

  def render_bar
    tag.div(class: 'search-bar-row', style: 'margin: 1em 0;') do
      render_search_bar + render_quick_actions
    end
  end

  def render_search_bar
    tag.div(class: 'search-bar') do
      render_filter_menu + render_selection_menu
    end
  end

  def render_quick_actions
    return if quick_actions_partial.nil?

    tag.div(class: 'quick-actions') do
      view_context.render \
        partial: quick_actions_partial[:path],
        locals: quick_actions_partial[:locals]
    end
  end

  def root_identifier
    "#{self.class.name.demodulize.underscore.dasherize}-index"
  end

  def stimulus_controllers
    'multi-selection-index batch-actions-index filtered-index sorted-index'
  end

  delegate :params, to: :view_context
  delegate :can, to: :user
end
