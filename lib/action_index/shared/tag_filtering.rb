module ActionIndex::Shared::TagFiltering
  def filter_tag(type = taggable_klass)
    values = values_from_tags(TagsQuery.new.search.for_type(type))

    {
      type: 'dropdown',
      name: 'tag',
      default_text: 'Tag',
      search: {
        placeholder: 'Search tags...'
      },
      special_values: [['Any Tags', ' ']],
      values:
    }
  end

  def filter_tags(type = taggable_klass, default_text: 'Tag')
    values = values_from_tags(TagsQuery.new.search.for_type(type))

    {
      type: 'dropdown_selection',
      name: 'tags',
      default_text:,
      search: {
        placeholder: 'Search tags...'
      },
      values:
    }
  end

  def taggable_klass
    self.class.name.demodulize
  end

  private

  def values_from_tags(tags_query)
    if CustomerSpecific::Behavior.property_scoped_tags?
      tags_query.for_user(user).map do |tag|
        tag_value = tag.tag
        tag_value += " - #{tag.property.name}" if tag.property

        [tag_value, tag.id]
      end
    else
      tags_query.pluck(:tag, :id)
    end
  end
end
