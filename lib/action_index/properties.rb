class ActionIndex::Properties < ActionIndex::Base
  include ActionIndex::Shared::TagFiltering

  def base_path
    if portfolio_mode?
      portfolio_path(params[:id])
    else
      properties_path
    end
  end

  def selection_items
    return [] unless Feature.enabled?(:onboarding_setup, Customer.current)

    [add_default_onboarding, turn_on_auto_assignment_onboarding, remove_default_onboarding].compact
  end

  def filter_items
    [
      filter_portfolio,
      filter_owner,
      filter_subdivision,
      filter_occupancy,
      filter_type,
      filter_tag('Property'),
      filter_custom_status
    ].compact
  end

  private

  def filter_portfolio
    return nil if portfolio_mode?

    portfolios = PortfoliosQuery.for_user(user)
                                .order(name: :asc)
                                .pluck(:name, :id)

    return nil unless portfolios.many?

    {
      type: 'dropdown',
      name: 'portfolio_id',
      default_text: 'Portfolio',
      search: {
        placeholder: 'Search portfolios...'
      },
      values: portfolios
    }
  end

  def filter_owner
    {
      type: 'dropdown',
      name: 'company_id',
      default_text: 'Owner',
      search: {
        placeholder: 'Search owners...'
      },
      values: CompaniesQuery.for_user(user).order(name: :asc).pluck(:name, :id)
    }
  end

  def filter_subdivision
    return unless Feature.enabled?(:property_subdivision, Customer.current)

    subdivisions = \
      Property
      .where.not(subdivision: nil)
      .reorder(subdivision: :asc)
      .distinct(:subdivision)
      .pluck(:subdivision, :subdivision)

    {
      type: 'dropdown',
      name: 'subdivision',
      default_text: 'Subdivision',
      search: {
        placeholder: 'Search subdivisions...'
      },
      values: [['Any', '']] + subdivisions
    }
  end

  def filter_occupancy
    {
      type: 'dropdown',
      name: 'occupancy',
      default_text: 'Occupancy',
      values: [
        ['Any', ''],
        ['Fully Occupied', 'fully_occupied'],
        ['Partially Occupied', 'partially_occupied'],
        ['Has Vacancy', 'has_vacancy'],
        ['Fully Vacant', 'fully_vacant']
      ]
    }
  end

  def filter_type
    {
      type: 'dropdown',
      name: 'kind',
      default_text: 'Type',
      values: [['Any', nil]] + Property::PropertyTypes.property_kind_dropdown_values
    }
  end

  def filter_custom_status
    unless Customer.current_subdomain.start_with?(
      'mph-sandbox', 'marketplacehomes'
    )
      return
    end

    {
      type: 'dropdown',
      name: 'custom_status',
      default_text: 'Status',
      values: [['Any', '']] + Property.custom_status_values.map do |value|
        [value.to_s.titleize, value]
      end
    }
  end

  def add_default_onboarding
    return if Feature.enabled?(:onboarding_enhancements, Customer.current)

    {
      type: 'Button',
      text: 'Add Default Onboarding',
      id: 'add-default-onboarding-button'
    }
  end

  def turn_on_auto_assignment_onboarding
    return unless Feature.enabled?(:onboarding_enhancements, Customer.current)

    {
      type: 'button',
      text: 'Turn On Auto Assignment',
      modal: :turn_on_auto_assignment_onboarding_modal
    }
  end

  def remove_default_onboarding
    text = if Feature.enabled?(:onboarding_enhancements, Customer.current)
             'Turn Off Auto Assignment'
           else
             'Remove Default Onboarding'
           end
    {
      type: 'Button',
      text: text,
      id: 'remove-default-button'
    }
  end

  def portfolio_mode?
    view_context.controller_name == 'portfolios'
  end
end
