class ActionIndex::Listings < ActionIndex::Base
  def base_path
    marketing_listings_path
  end

  def filter_items
    [
      filter_property,
      filter_status,
      filter_agent
    ]
  end

  private

  def filter_property
    {
      type: 'dropdown',
      name: 'property_id',
      default_text: 'Property',
      search: {
        placeholder: 'Search properties...'
      },
      special_values: [['All Properties', ' ']],
      values: user.properties.pluck(:name, :id)
    }
  end

  def filter_status
    {
      type: 'dropdown',
      name: 'listing_status',
      default_text: 'Status',
      values: [
        ['All', ' '],
        ['Published', 'published'],
        ['Unpublished', 'unpublished'],
        ['No Listing', 'no_listing']
      ]
    }
  end

  def filter_agent
    listing_agents = \
      PropertyManager
      .where(id: Listing.select(:agent_id))
      .order(first_name: :asc)

    {
      type: 'dropdown',
      name: 'listing_agent_id',
      default_text: 'Agent',
      search: {
        placeholder: 'Search agents...'
      },
      values: listing_agents.map { |pm| [pm.name, pm.id] }
    }
  end
end
