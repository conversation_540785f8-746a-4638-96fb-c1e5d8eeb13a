class ActionIndex::Agreements < ActionIndex::Base
  include ActionIndex::Shared::TagFiltering
  include ActionIndex::Shared::TagActions

  def base_path
    leasing_agreements_path(type: params[:type])
  end

  def filter_items
    [
      filter_property,
      filter_status,
      filter_execution,
      filter_agreements_tag
    ].compact
  end

  def selection_items
    [
      add_charge,
      add_credit,
      (add_tag unless leases_view?),
      (remove_tag unless leases_view?),
      send_invite
    ].compact
  end

  def supports_overselection?
    true
  end

  private

  def filter_property
    {
      type: 'dropdown',
      name: 'property_id',
      default_text: 'Property',
      search: {
        placeholder: 'Search properties...'
      },
      special_values: [['Any Property', ' ']],
      values: user.properties.pluck(:name, :id)
    }
  end

  def filter_status
    {
      type: 'dropdown',
      name: 'status',
      default_text: 'Current',
      values: [
        %w[Current current],
        %w[Upcoming upcoming],
        %w[Previous previous],
        (['On Notice', 'on_notice'] if leases_view?),
        %w[Archived archived],
        %w[Unarchived unarchived]
      ].compact
    }
  end

  def filter_execution
    {
      type: 'dropdown',
      name: 'execution',
      default_text: 'Execution',
      values: [
        %w[All all],
        %w[Executed executed],
        %w[Unexecuted unexecuted]
      ]
    }
  end

  def filter_agreements_tag
    return nil if leases_view?

    filter_tag
  end

  def add_charge
    return nil if house_director?

    {
      type: 'button',
      text: 'Add Charge',
      id: 'batch-add-charge-button',
      permission: can.add_charges_and_credits_to_operations_agreements?
    }
  end

  def add_credit
    return nil if house_director?

    {
      type: 'button',
      text: 'Add Credit',
      id: 'batch-add-credit-button',
      permission: can.add_charges_and_credits_to_operations_agreements?
    }
  end

  def send_invite
    {
      type: 'button',
      text: 'Send Invite',
      action: 'batch_invite',
      confirm: 'Send Portal Invites?',
      permission: can.send_portal_invites_to_operations_agreements?
    }
  end

  def root_identifier
    "#{super}-#{params[:type]}"
  end

  def filters_identifier
    "#{super}-#{params[:type]}"
  end

  def leases_view?
    params[:type] == 'leases' || view_context.controller_name == 'leases'
  end

  def house_director?
    return false unless CustomerSpecific::Behavior.disable_based_on_role_name?

    user.role.name.in?(
      [
        'House Director',
        'Chapter Advisor / Officer',
        'House Corporation Board Member'
      ]
    )
  end

  def taggable_klass
    'Agreements::SimpleAgreement'
  end
end
