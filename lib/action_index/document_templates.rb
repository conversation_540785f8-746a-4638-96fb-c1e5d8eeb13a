class ActionIndex::DocumentTemplates < ActionIndex::Base
  def base_path
    organization_documents_templates_path
  end

  def filter_items
    [filter_template_type, filter_property]
  end

  private

  def filter_template_type
    {
      type: 'dropdown',
      name: 'template_type',
      default_text: 'All Types',
      values: [['All Types', '']] + Document::TEMPLATE_KINDS.invert.to_a
    }
  end

  def filter_property
    {
      type: 'dropdown',
      name: 'property_id',
      default_text: 'Property',
      search: {
        placeholder: 'Search properties...'
      },
      special_values: [['Any Property', ' ']],
      values: user.properties.pluck(:name, :id)
    }
  end
end
