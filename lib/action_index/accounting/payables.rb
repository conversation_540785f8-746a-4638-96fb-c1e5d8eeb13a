class ActionIndex::Accounting::Payables < ActionIndex::Accounting::Invoices
  def base_path
    accounting_payables_path
  end

  def filter_items
    super + [
      filter_approval_status, filter_invoices_batch, filter_portfolio
    ].compact
  end

  def selection_items
    ([add_to_batch, approve] + super).compact
  end

  def delete
    super&.merge(permission: user.can.destroy_accounting_payables?)
  end

  def download
    super.merge(permission: user.can.export_accounting_payables?)
  end

  def filter_approval_status
    {
      type: 'dropdown',
      name: 'approval_status',
      default_text: 'Approval',
      search: {
        placeholder: 'Search users...'
      },
      special_values: [
        ['Any Approval', ' '],
        %w[Approved approved],
        %w[Unapproved unapproved],
        ['Needs My Approval', "needs_approval_by_#{user.id}"],
        ['Approved By Me', "approved_by_#{user.id}"]
      ],
      values: PropertyManager.unarchived.order(last_name: :asc).map do |user|
        [user.name, "approved_by_#{user.id}"]
      end
    }
  end

  def filter_invoices_batch
    {
      type: 'dropdown',
      name: 'invoices_batch',
      default_text: 'Batch',
      search: {
        placeholder: 'Search batches...'
      },
      special_values: [['Any Batch', 'any'], %w[Unbatched none]],
      values: Payment::Batch.open.order(:name).map { |b| [b.name, b.id] }
    }
  end

  # Batch actions
  # TODO: Add batch_pay
  def add_to_batch
    if user.role.name.in?(['House Director', 'Chapter Advisor / Officer', 'House Corporation Board Member']) && CustomerSpecific::Behavior.disable_based_on_role_name?
      return nil
    end

    payment_batches = Payment::BatchesQuery.new
                                           .search
                                           .insertable_by(user)
                                           .reorder(name: :desc)

    {
      type: 'dropdown',
      name: 'payment_batch_id',
      text: 'Add to Batch',
      search: {
        placeholder: 'Search batches...'
      },
      values: payment_batches.pluck(:name, :id),
      action: 'invoices/batch_add_to_batch',
      permission: user.can.add_invoices_to_accounting_payables_batches?
    }
  end

  def approve
    if user.role.name.in?(['House Director', 'Chapter Advisor / Officer', 'House Corporation Board Member']) && CustomerSpecific::Behavior.disable_based_on_role_name?
      return nil
    end

    {
      type: 'button',
      text: 'Approve',
      action: 'invoices/batch_approve',
      permission: user.can.mark_paid_accounting_payables?
    }
  end
end
