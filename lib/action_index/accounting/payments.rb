class ActionIndex::Accounting::Payments < ActionIndex::Base
  include ActionIndex::Accounting::Target

  def base_path
    accounting_payments_path
  end

  def search_debounce_delay
    500
  end

  def filter_items
    [
      filter_start_date,
      filter_end_date,
      filter_target('payer'),
      filter_target('payee'),
      filter_kind,
      filter_status,
      filter_portfolio
    ].compact
  end

  private

  def filter_start_date
    {
      type: :date,
      name: :start_date,
      default_text: 'From'
    }
  end

  def filter_end_date
    {
      type: :date,
      name: :end_date,
      default_text: 'Until'
    }
  end

  def filter_kind
    {
      type: :dropdown,
      name: :kind,
      default_text: 'Method',
      values: [['Any', '']] + Payment.kinds.keys.sort.map do |key|
        display = case key
                  when 'ach' then 'ACH'
                  when 'credit' then 'Card Payment'
                  else key.titleize
                  end

        [display, key]
      end
    }
  end

  def filter_status
    {
      type: :dropdown,
      name: :status,
      default_text: 'Status',
      values: [['Any', '']] + Payment.statuses.keys.map do |key|
        [key.titleize, key]
      end
    }
  end
end
