class ActionIndex::Inspections::Reports < ActionIndex::Base
  def base_path
    operations_inspections_path
  end

  def filter_items
    [filter_assignee, filter_status, filter_property, filter_template, more]
  end

  def selection_items
    [archive]
  end

  private

  def filter_assignee
    {
      type: :search,
      name: 'assignee',
      default_text: 'Assignee',
      url: '/operations/inspections/assignees?q={query}'
    }
  end

  def filter_status
    {
      type: 'dropdown',
      name: 'status',
      default_text: 'Status',
      values: Inspection::Report.statuses.map { |s, _| [s.titleize, s] }
    }
  end

  def filter_property
    properties = user.properties.pluck(:name, :id)

    {
      type: 'dropdown',
      name: 'property_id',
      default_text: 'Property',
      search: {
        placeholder: 'Search properties...'
      },
      values: properties
    }
  end

  def filter_template
    templates = Inspection::Template.order(name: :asc).pluck(:name, :id)

    {
      type: 'dropdown',
      name: 'template_id',
      default_text: 'Template',
      search: {
        placeholder: 'Search templates...'
      },
      values: templates
    }
  end

  def more
    {
      type: 'dropdown',
      name: 'more',
      default_text: 'More',
      values: [
        ['Overdue', 'overdue'],
        ['Assigned to Me', user.id],
        ['Assigned', 'assigned'],
        ['Unassigned', 'unassigned']
      ]
    }
  end

  def archive
    {
      type: 'button',
      text: 'Archive',
      modal: :confirm_archive_modal,
      permission: user.can.archive_operations_inspections?
    }
  end
end
