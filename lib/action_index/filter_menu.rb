module ActionIndex::FilterMenu
  private

  def render_filter_menu
    tag.div(class: 'no-selection') do
      tag.div(class: 'ui small borderless menu',
              id: filters_identifier,
              data: {
                multi_selection_index_target: 'noSelectionMenu',
                turbolinks_permanent: true
              }) do
        [
          standard_search_item,
          *render_filter_items,
          clear_item
        ].reduce(:+) # rubocop:disable Performance/Sum
      end
    end
  end

  def clear_item
    link_to 'Clear Filters',
            base_path,
            class: 'item',
            data: { filtered_index_target: 'clearButton' }
  end

  def render_filter_items # rubocop:disable Metrics/CyclomaticComplexity,Metrics/MethodLength
    filter_items.compact.map do |item| # rubocop:disable Metrics/BlockLength
      case item[:type].to_s
      when 'dropdown'
        dropdown_item(item,
                      data: {
                        filtered_index_target: 'dropdown',
                        name: item[:name]
                      })
      when 'dropdown_selection'
        dropdown_selection_item(item,
                                data: {
                                  filtered_index_target: 'dropdownSelection',
                                  name: item[:name]
                                })
      when 'dropdown_search_selection'
        dropdown_search_selection_item(item,
                                       data: {
                                         filtered_index_target: 'dropdownSearchSelection',
                                         name: item[:name]
                                       })
      when 'date'
        date_item(item)
      when 'search'
        search_item(item)
      when 'button'
        button_item(item)
      else
        fail 'Unknown Filter Type'
      end
    end
  end

  def filters_identifier
    "#{self.class.name.demodulize.underscore.dasherize}-filter-content"
  end
end
