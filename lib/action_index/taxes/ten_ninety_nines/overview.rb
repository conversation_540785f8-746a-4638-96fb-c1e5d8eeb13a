class ActionIndex::Taxes::TenNinetyNines::Overview < ActionIndex::Base
  def base_path
    taxes_ten_ninety_nines_path
  end

  def supports_overselection?
    true
  end

  def selection_items
    [skip, clear_skip, review, review_correction_details]
  end

  def filter_items
    [filter_payer, filter_form_name, filter_status, filter_issue, filter_year]
  end

  private

  def filter_payer
    {
      type: 'dropdown',
      name: 'payer',
      default_text: 'Payer',
      values: [
        ['Any', ''],
        *collection.pluck(:payer_name, :payer_id).uniq
      ]
    }
  end

  def filter_status
    {
      type: 'dropdown',
      name: 'status',
      default_text: 'Status',
      values: [
        ['Any', ''],
        *(Taxes::Candidate1099::STATUSES.map { |s| [s.humanize.titleize, s] })
      ]
    }
  end

  def filter_issue
    {
      type: 'dropdown',
      name: 'issue',
      default_text: 'Issue',
      values: [
        ['Select Issue', ''],
        *Taxes::Candidate1099::ISSUE_FILTERS
          .stringify_keys
          .to_a
          .map(&:reverse)
      ]
    }
  end

  def filter_form_name
    {
      type: 'dropdown',
      name: 'form_name',
      default_text: 'Form',
      values: [
        ['Any', ''],
        ['1099 MISC', '1099MISC'],
        ['1099 NEC', '1099NEC']
      ]
    }
  end

  def filter_year
    {
      type: 'dropdown',
      name: 'year',
      default_text: Taxes.default_year,
      values: allowed_years
    }
  end

  def review
    {
      type: 'button',
      text: 'Review and File...',
      id: 'review-and-file'
    }
  end

  def review_correction_details
    {
      type: 'button',
      text: 'Corrections (Details)',
      id: 'review-correction-details'
    }
  end

  def skip
    {
      type: 'button',
      text: 'Skip',
      id: 'skip-payees'
    }
  end

  def clear_skip
    {
      type: 'button',
      text: 'Unskip',
      id: 'unskip-payees'
    }
  end

  def allowed_years
    (2022..(Time.zone.today.year)).to_a.map do |year|
      [year.to_s, year.to_s]
    end
  end
end
