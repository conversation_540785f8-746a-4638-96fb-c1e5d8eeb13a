class ActionIndex::MemberOnboarding::Assignments < ActionIndex::Base
  include ActionIndex::Shared::TagFiltering

  def base_path
    onboarding_assignments_path(params[:onboarding_id])
  end

  def taggable_klass
    'Agreements::SimpleAgreement'
  end

  def supports_overselection?
    true
  end

  def selection_items
    [bulk_unassign]
  end

  def filter_items
    [
      filter_assigned_from,
      filter_assigned_until,
      filter_tags(default_text: 'Membership Tag'),
      filter_status,
      filter_property
    ]
  end

  private

  def filter_assigned_from
    {
      type: 'date',
      name: 'from',
      default_text: 'Assigned From'
    }
  end

  def filter_assigned_until
    {
      type: 'date',
      name: 'until',
      default_text: 'Assigned Until'
    }
  end

  def filter_status
    {
      type: 'dropdown',
      name: 'status',
      default_text: 'Status',
      values: [
        ['All', ' '],
        ['Pending', 'pending'],
        ['Completed', 'completed']
      ]
    }
  end

  def filter_property
    return unless Feature.enabled?(:onboarding_enhancements, Customer.current)

    configuration = OnboardingsQuery.new
                                    .search
                                    .by_user(user)
                                    .includes(:properties)
                                    .find(params[:onboarding_id])

    properties = configuration.onboardable_properties & user.properties

    {
      type: 'dropdown_selection',
      name: 'property_id',
      default_text: 'Property',
      search: {
        placeholder: 'Search properties...'
      },
      values: properties.pluck(:name, :id)
    }
  end

  def bulk_unassign
    {
      type: 'button',
      text: 'Unassign',
      modal: :confirm_unassign_modal
    }
  end
end
