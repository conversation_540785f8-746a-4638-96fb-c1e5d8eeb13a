class ActionIndex::MemberOnboarding::AssignableMembers < ActionIndex::Base
  include ActionIndex::Shared::TagFiltering

  def base_path
    new_onboarding_assignment_path(params[:onboarding_id])
  end

  def taggable_klass
    'Agreements::SimpleAgreement'
  end

  def supports_overselection?
    true
  end

  def selection_items
    [
      bulk_assign
    ]
  end

  def filter_items
    [
      filter_tags(default_text: 'Membership Tag'),
      filter_property,
      filter_onboarding_assigned
    ]
  end

  private

  def bulk_assign
    {
      type: 'button',
      text: 'Assign',
      modal: :confirm_assign_modal
    }
  end

  def filter_property
    configuration = OnboardingsQuery.new
                                    .search
                                    .by_user(user)
                                    .find(params[:onboarding_id])

    properties = configuration.onboardable_properties & user.properties

    {
      type: 'dropdown_selection',
      name: 'property_id',
      default_text: 'Property',
      search: {
        placeholder: 'Search properties...'
      },
      values: properties.pluck(:name, :id)
    }
  end

  def filter_onboarding_assigned
    {
      type: 'dropdown',
      name: 'onboarding_assigned',
      default_text: 'Active Onboarding',
      values: [
        ['All', ''],
        ['Assigned', 'assigned'],
        ['None', 'none']
      ]
    }
  end
end
