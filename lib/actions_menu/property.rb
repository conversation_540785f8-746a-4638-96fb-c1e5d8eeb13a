class ActionsMenu::Property < ActionsMenu::Base
  include ActionsMenu::Archiving
  include ActionsMenu::Inspectable

  attr_reader :property

  alias inspectable property

  def initialize(property, user)
    @property = property
    super(property, user)
  end

  def items
    [
      add_unit,
      add_floorplan,
      add_room,
      export,
      edit,
      transfer,
      inspection,
      refinance,
      archive,
      unarchive
    ]
  end

  private

  def add_unit
    return if property.single_unit_type?

    {
      text: 'Add Unit',
      icon: 'plus',
      link: new_unit_path(property_id: property.id),
      disabled: house_director?,
      permission: can.create_portfolio_units?
    }
  end

  def add_floorplan
    return unless property.apartment?

    {
      text: 'Add Floorplan',
      icon: 'plus',
      link: new_property_floorplan_path(property),
      disabled: house_director?,
      permission: can.manage_floorplans_for_portfolio_properties?
    }
  end

  def add_room
    return unless Feature.enabled?(:rooms, Customer.current)

    {
      text: 'Add Room',
      icon: 'plus',
      modal: :add_room_modal,
      permission: can.create_portfolio_units?
    }
  end

  def archive
    return unless property.unarchived?

    {
      text: 'Archive',
      icon: 'archive',
      link: prepare_archive_property_path(property),
      disabled: house_director?,
      permission: can.archive_portfolio_properties?
    }
  end

  def edit
    {
      text: 'Edit',
      icon: 'edit',
      link: edit_property_path(property),
      disabled: house_director?,
      permission: can.update_portfolio_properties?
    }
  end

  def transfer
    {
      text: 'Transfer',
      icon: 'exchange',
      link: new_property_transfer_path(property),
      disabled: house_director?,
      permission: can.transfer_portfolio_properties?
    }
  end

  def refinance
    return unless Feature.enabled?(:refinance_opportunities, Customer.current)

    owner = property.company.owners.first

    {
      text: 'Offer Refinance',
      icon: 'envelope outline',
      link: offer_refinance_property_path(property),
      options: {
        method: :post,
        data: { confirm: "Send Offer to #{owner&.email}?" }
      },
      disabled: owner&.email.blank?,
      permission: can.refinance_portfolio_properties?
    }
  end

  def export
    return nil unless Feature.enabled?(:data_exporting, Customer.current)

    {
      text: 'Export',
      icon: 'download',
      link: export_property_path(property),
      options: {
        remote: true
      },
      permission: can.export_portfolio_properties?
    }
  end

  def resource
    property
  end

  def resource_path
    [property]
  end

  def archive_permission?
    can.archive_portfolio_properties?
  end

  def inspection_permission?
    can.manage_inspections_for_portfolio_properties?
  end
end
