module ActionsMenu::Communications
  protected

  def communications_contact
    fail NotImplementedError
  end

  def communications_items
    [send_email, send_sms]
  end

  def send_email
    return nil if communications_contact.email.blank?

    {
      text: 'Send Email',
      icon: 'envelope alternate outline',
      link: '#',
      options: {
        id: 'send_email_action'
      }
    }
  end

  def send_sms
    return nil if communications_contact.phone.blank?

    {
      text: 'Send SMS',
      icon: 'mobile alternate',
      link: '#',
      options: {
        id: 'send_sms_action'
      }
    }
  end
end
