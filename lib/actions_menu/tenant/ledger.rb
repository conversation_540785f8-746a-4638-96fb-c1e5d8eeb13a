module ActionsMenu::Tenant::Ledger
  protected

  def ledger_items
    [add_charge, add_credit, make_payment, prepare_refund, transfer_deposit]
  end

  def add_charge
    {
      text: 'Add Charge',
      icon: 'file invoice dollar',
      link: charge_tenant_path(tenant),
      disabled: house_director?
    }
  end

  def add_credit
    {
      text: 'Add Credit',
      icon: 'gift',
      link: credit_tenant_path(tenant),
      disabled: house_director?
    }
  end

  def prepare_refund
    {
      text: 'Prepare Refund',
      icon: 'money check',
      link: new_tenant_refund_path(tenant),
      disabled: house_director?
    }
  end

  def transfer_deposit
    return unless Feature.enabled?(:transfer_deposits, Customer.current)

    {
      text: 'Transfer Deposit',
      icon: 'exchange alternate',
      link: new_tenant_deposit_transfer_path(tenant)
    }
  end

  def make_payment
    unless Feature.enabled?(:management_pay_tenant_balance, Customer.current)
      return
    end

    {
      text: 'Make Payment',
      icon: 'credit card',
      link: new_accounting_electronic_payment_path(tenant_id: tenant.id)
    }
  end
end
