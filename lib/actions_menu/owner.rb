class ActionsMenu::Owner < ActionsMenu::Base
  include ActionsMenu::Archiving
  include ActionsMenu::Communications
  include ActionsMenu::UserMasquerading

  attr_reader :owner

  alias communications_contact owner

  def initialize(owner)
    @owner = owner
  end

  def items
    [
      edit,
      send_email,
      (send_sms if Feature.enabled?(:sms_inbox, Customer.current)),
      generate_document,
      masquerade,
      archive,
      unarchive
    ]
  end

  private

  def edit
    {
      text: 'Edit',
      icon: 'edit',
      link: edit_owner_path(owner)
    }
  end

  def generate_document
    {
      text: 'Generate Document',
      link: new_owner_document_template_path(owner),
      icon: 'file alternate outline'
    }
  end

  def archive
    return if owner.archived?

    {
      text: 'Archive',
      icon: 'archive',
      link: prepare_archive_owner_path(owner)
    }
  end

  def resource
    owner
  end

  def resource_path
    [owner]
  end
end
