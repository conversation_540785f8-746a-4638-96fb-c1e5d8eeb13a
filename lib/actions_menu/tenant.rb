class ActionsMenu::Tenant < ActionsMenu::Base
  include ActionsMenu::Communications
  include ActionsMenu::Tenant::Ledger
  include ActionsMenu::Tenant::Merge
  include ActionsMenu::UserMasquerading

  attr_reader :tenant

  alias communications_contact tenant

  def initialize(tenant, user)
    @tenant = tenant
    super(tenant, user)
  end

  def items
    [
      *ledger_items,
      email_document, generate_document,
      export,
      *communications_items,
      masquerade,
      merge, edit
    ]
  end

  private

  def edit
    {
      text: 'Edit',
      icon: 'edit',
      link: edit_tenant_path(tenant),
      disabled: house_director?
    }
  end

  def email_document
    items = [document_from_template]

    if Feature.enabled?(:legacy_seven_day_notice, Customer.current)
      items << seven_day_notice
    end

    {
      text: 'Email Document',
      items: items,
      disabled: house_director?
    }
  end

  def generate_document
    {
      text: 'Generate Document',
      items: [renewal_form, preferred_renewal_form],
      disabled: house_director?
    }
  end

  def document_from_template
    {
      text: 'From Template',
      link: new_tenant_document_template_path(tenant),
      icon: 'mail outline',
      disabled: house_director?
    }
  end

  def seven_day_notice
    {
      text: 'Seven Day Notice',
      icon: 'mail outline',
      link: seven_day_notice_tenant_path(tenant),
      disabled: house_director?
    }
  end

  def renewal_form
    return nil unless Customer.current_subdomain == 'shamrock'

    {
      text: 'Renewal Form',
      icon: 'refresh',
      link: tenant_documents_path(tenant),
      options: {
        target: '_blank'
      },
      disabled: house_director?
    }
  end

  def preferred_renewal_form
    return nil unless Customer.current_subdomain == 'shamrock'

    {
      text: 'Preferred Renewal Form',
      icon: 'refresh',
      link: tenant_documents_path(
        tenant, template_type: 'preferred_lease_renewal_form'
      ),
      options: {
        target: '_blank'
      },
      disabled: house_director?
    }
  end

  def export
    {
      text: 'Export',
      icon: 'download',
      link: tenant_path(tenant, format: :zip)
    }
  end
end
