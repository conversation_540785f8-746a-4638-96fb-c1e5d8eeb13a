module DocxFilling
  class ReplaceUno
    attr_reader :initial_file

    def self.build(path_or_io, temp_dir = nil)
      if Feature.enabled?(:interpolate_with_uno, Customer.current)
        new(path_or_io, temp_dir)
      else
        DocxReplace::Doc.new(path_or_io, temp_dir)
      end
    end

    def self.generate_replace_command(
      input_document_file_path, output_document_file_path, replacements_hash
    )
      # need help with this
      []
    end

    def initialize(path_or_io, temp_dir = nil)
      @initial_file = File.new(path_or_io)
      @temp_dir = temp_dir
      @replacements = []
    end

    def replace(pattern, replacement, multiple_occurrences = false)
      @replacements << {
        pattern: if pattern.is_a?(Regexp)
                   pattern.source.sub(/^{/, '\{').sub(/}$/, '\}')
                 else
                   pattern
                 end,
        pattern_type: pattern.is_a?(Regexp) ? 'regex' : 'string',
        replacement: replacement || '',
        multiple_occurrences: multiple_occurrences
      }
    end

    def commit(new_path = nil)
      write_back_to_file(new_path)
    end

    private

    def write_back_to_file(new_path = nil)
      temp_file = if @temp_dir.nil?
                    Tempfile.new('docxedit-')
                  else
                    Tempfile.new('docxedit-', @temp_dir)
                  end

      call_python_replace_script!(temp_file)

      if new_path.nil?
        FileUtils.rm(initial_file)
      else
        path = new_path
      end

      FileUtils.mv(temp_file.path, path)
      File.new(path)
    end

    def call_python_replace_script!(temp_file)
      log = Tempfile.new(%w[replace_text_uno .log])
      begin
        self.class.generate_replace_command(@initial_file.path, temp_file.path, @replacements)

        stdout_str, status = Open3.capture2(*cmd, err: log.path)

        unless status.success?
          # rewind and read only the last line
          log.rewind
          last_line = (stdout_str.presence || '') + "\n" + `tail -n 4 #{log.path}`
          fail DocxFilling::UnoInterpolationError, last_line
        end

        stdout_str
      ensure
        log.close
        log.unlink # deletes the tempfile
      end
    end
  end
end
