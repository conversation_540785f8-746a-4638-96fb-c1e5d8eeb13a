class DocxFilling::ListTags
  def self.tags(docx_file)
    doc_text = get_doc_text(docx_file.path)
    doc_text.scan(/{[^}]+/).map do |tag|
      tag.gsub(/{/, '').gsub(/}/, '').strip
    end
  rescue StandardError => e
    Honeybadger.notify(e)
    []
  end

  def self.get_doc_text(docx_file_path)
    Zip::File.open(docx_file_path) do |zip_file|
      # maybe we should look through headers and footers too

      entry = zip_file.entries.find do |file|
        file.name.ends_with?('document.xml') ||
          file.name.ends_with?('content.xml')
      end

      xml = entry.get_input_stream.read

      Nokogiri::XML(xml).text
    end
  end
end
