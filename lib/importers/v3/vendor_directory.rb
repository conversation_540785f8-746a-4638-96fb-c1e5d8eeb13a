class Importers::V3::VendorDirectory < Importers::V3::Base
  include Importers::V3::DirectoryHelpers

  schema do
    column :vendor_name do
      required
      identifiers 'Vendor Name', 'Name'
      description 'The name of the vendor.'
      sample 'Sample Vendor'
      type :string
    end

    column :line_one do
      identifiers 'Line One'
      description 'The street address of the vendor.'
      sample '6031 Orangegrove St.'
      type :string
    end

    column :line_two do
      identifiers 'Line Two'
      description 'The apartment or suite number of the vendor.'
      sample 'Unit 42'
      type :string
    end

    column :city do
      identifiers 'City'
      description "The vendor's city."
      sample 'Detroit'
      type :string
    end

    column :region do
      identifiers 'State'
      description "The vendor's region or state."
      sample 'Michigan'
      type :string
    end

    column :postal_code do
      identifiers 'Postal Code'
      description 'The postal code or zip code of the vendor.'
      sample '48212'
      type :string
    end

    column :first_name do
      identifiers 'First Name'
      description 'The first name of the contact'
      sample 'John'
      type :string
    end

    column :last_name do
      identifiers 'Last Name'
      description 'The last name of the contact'
      sample 'Smith'
      type :string
    end

    column :phone do
      identifiers 'Phone'
      description 'The phone number of the contact'
      sample '(*************'
      type :phone
    end

    column :email do
      identifiers 'Email'
      description 'The email address of the contact'
      sample 'johns<PERSON>@example.com'
      type :email
    end

    column :tags do
      identifiers 'Tags'
      description 'Comma separated tags for the entity.'
      sample 'Tag One, Tag Two'
      type :array
    end
  end

  protected

  def process_item(item)
    name = item.value(identifiers(:vendor_name))
    vendor = Vendor.find_or_initialize_by(name: name)

    tags = item.value(identifiers(:tags))
    vendor.tags = tags if tags.present?

    email = item.value(identifiers(:email)).presence
    phone = phones(item).first

    first_name, last_name = contact_first_last(item)

    first_name ||= name if email || phone

    if first_name
      contact = vendor.vendor_contacts.find_or_initialize_by(
        first_name: first_name,
        last_name: last_name
      )

      contact.email = email if email.present?
      contact.phone = phone if phone.present?
    end

    address = Address.new(
      line_one: item.value(identifiers(:line_one)),
      line_two: item.value(identifiers(:line_two)),
      city: item.value(identifiers(:city)),
      region: item.value(identifiers(:region)),
      postal_code: item.value(identifiers(:postal_code)),
      country: 'United States'
    )

    vendor.address = address if address.valid?

    vendor.save!

    record_imported(vendor)
  end

  private

  def contact_first_last(item)
    [item.value(identifiers(:first_name)), item.value(identifiers(:last_name))]
  end
end
