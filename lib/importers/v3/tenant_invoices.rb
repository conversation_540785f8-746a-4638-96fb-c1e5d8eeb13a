class Importers::V3::TenantInvoices < Importers::V3::Base
  schema do
    column :property do
      required
      identifiers 'Property', 'Property Name'
      description 'The name of the property of the leased unit'
      sample '1234 Main Street'
    end

    column :unit do
      required
      identifiers 'Unit', 'Unit Name'
      description 'The name of the leased unit'
      sample 'Unit 1'
    end

    column :first_name do
      required
      identifiers 'First Name', 'Tenant First Name'
      description 'The first name of the tenant'
      sample 'John'
    end

    column :last_name do
      required
      identifiers 'Last Name', 'Tenant Last Name'
      description 'The last name of the tenant'
      sample 'Smith'
    end

    column :date do
      required
      identifiers 'Date', 'Post Date'
      description 'The post date of the invoice'
      sample '1/1/2020'
    end

    column :due_date do
      required
      identifiers 'Due Date'
      description 'The due date of the invoice'
      sample '1/1/2020'
    end

    column :description do
      required
      identifiers 'Description', 'Invoice Description'
      description 'The description of the invoice'
      sample 'February Rent'
    end

    column :invoice_number do
      identifiers 'Invoice Number'
      description 'The invoice number'
      sample '1001'
    end

    column :line_item do
      identifiers 'Line Item', 'Line Item Description'
      description 'The description of the line item'
      sample 'Monthly Rent'
    end

    column :account do
      required
      identifiers 'Account', 'GL Code'
      description 'The GL code of the receivable account'
      sample '4100 - Rent'
    end

    column :amount do
      required
      identifiers 'Amount'
      description 'The amount of the line item'
      sample '$100.00'
    end

    column :notes do
      identifiers 'Note', 'Notes'
      description 'Extra notes'
    end
  end

  protected

  def process_item(item)
    property = property(item)

    lease_membership = lease_membership(item, property)

    tenant = if lease_membership
               lease_membership.tenant
             else
               membership = simple_agreement_membership(item, property)
               membership.tenant
             end

    unless tenant
      name = item.value(identifiers(:payer))
      fail "Unable to find tenant '#{name}' at property '#{property.name}'"
    end

    description = item.value(identifiers(:description))

    item_description = item.value(identifiers(:line_item)) || description

    attributes = {
      buyer: tenant,
      buyer_lease_membership: lease_membership,
      seller: property,
      post_date: item.value(identifiers(:date)),
      physical_date: item.value(identifiers(:date)),
      due_date: item.value(identifiers(:due_date)),
      description: description,
      invoice_number: item.value(identifiers(:invoice_number)),
      note: item.value(identifiers(:notes)),
      line_items_attributes: [
        {
          quantity: 1,
          unit_price: item.value(identifiers(:amount)),
          description: item_description,
          receivable_account: account(item, property)
        }
      ]
    }

    invoice = Invoice.create!(attributes)

    record_imported(invoice)
  end

  private

  def property(item)
    name = item.value(identifiers(:property))

    Property.find_by!(name: name)
  rescue ActiveRecord::RecordInvalid
    raise "Unable to find property '#{name}'"
  end

  def lease_membership(item, property)
    LeaseMembership
      .unarchived
      .joins(:tenant, lease: { unit: :property })
      .where(leases: { units: { property: property } })
      .merge(tenant_name_query(item))
      .last
  end

  def simple_agreement_membership(item, property)
    Agreements::SimpleAgreement::Membership
      .joins(:simple_agreement, :tenant)
      .merge(Agreements::SimpleAgreement.where(property: property))
      .merge(tenant_name_query(item))
      .last
  end

  def account(item, property)
    value = item.value(identifiers(:account)).to_s

    return nil if value.blank?

    gl_code = value.split(/[^\d]/).first

    property.company.chart_of_accounts.accounts.find_by!(gl_code: gl_code)
  end

  def tenant_name_query(item)
    first_name = item.value(identifiers(:first_name))
    last_name = item.value(identifiers(:last_name))

    Tenant.where(first_name: first_name, last_name: last_name)
  end

  def max_row_limit
    1000
  end
end
