class Importers::V3::Schema::Column
  attr_accessor :name, :type, :description, :sample
  attr_writer :identifiers, :required, :hidden

  def initialize(name)
    @name = name
  end

  def required?(importer = nil)
    if importer && @required.is_a?(Proc)
      importer.instance_eval(&@required)
    else
      @required || false
    end
  end

  def hidden?
    @hidden || false
  end

  def identifiers
    Array(@identifiers)
  end

  def default_identifier
    identifiers.first
  end
end
