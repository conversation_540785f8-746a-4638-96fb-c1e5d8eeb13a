class Importers::V3::Schema::Builder
  def self.build(importer, &)
    builder = new(importer)
    builder.instance_eval(&)
    builder.schema
  end

  attr_reader :schema

  def initialize(importer)
    @schema = importer.schema || Importers::V3::Schema.new(importer)
  end

  def column(name, &)
    @column = Importers::V3::Schema::Column.new(name)
    instance_eval(&)
    schema.add_column(@column)
  end

  # rubocop:disable Style/OptionalBooleanParameter
  def required(required = true, &block)
    @column.required = block || required
  end

  def hidden(hidden = true)
    @column.hidden = hidden
  end
  # rubocop:enable Style/OptionalBooleanParameter

  def identifiers(*values)
    @column.identifiers = Array(values)
  end

  def description(description)
    @column.description = description
  end

  def sample(sample)
    @column.sample = sample
  end

  def type(dry_type)
    case dry_type
    when :string then type(Dry::Types['coercible.string'])
    when :integer then type(Dry::Types['coercible.integer'])
    when :decimal then type(Dry::Types['coercible.decimal'])
    when :array then type(Importers::V3::Schema::Types::CommaSeparatedArray)
    when :email then type(Importers::V3::Schema::Types::Email)
    when :money then type(Importers::V3::Schema::Types::Money)
    when :phone then type(Importers::V3::Schema::Types::Phone)
    else
      @column.type = dry_type
    end
  end

  def enum(enum_hash)
    type(Importers::V3::Schema::Types::RailsEnumValue.constrained(included_in: enum_hash.keys))
  end
end
