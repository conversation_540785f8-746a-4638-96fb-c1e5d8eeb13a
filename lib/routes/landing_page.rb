##
# Routes for landing and marketing pages
module Routes::LandingPage
  extend Routes::Routing

  route do # rubocop:disable Metrics/BlockLength
    # Proxy certain www pages to the marketing site
    constraints subdomain: 'www' do
      %w[/ /contact /about-us].each do |path|
        get path, to: 'landing_page/marketing_site_proxy#proxy'
      end
    end

    # Landing Page
    root 'landing_page#index'

    %i[security contact company].map do |path|
      get path, to: "landing_page##{path}"
    end

    get '/sitemap.xml.gz' => redirect(
      'https://revela-public.s3.amazonaws.com/sitemap.xml.gz'
    ), as: :sitemap

    get '/privacy' => redirect(
      'https://revela-public.s3.amazonaws.com/legal/privacy-policy.pdf'
    ), as: :privacy

    get '/terms' => redirect(
      'https://revela-public.s3.amazonaws.com/legal/terms-of-use.pdf'
    ), as: :terms

    get '/status' => redirect('https://status.revela.co'), as: :status

    namespace :legal do
      namespace :unit do
        {
          deposit_account_end_user_agreement: 'https://revela-public.s3.amazonaws.com/legal/deposit-account-end-user-agreement.pdf',
          deposit_account_disclosures: 'https://revela-public.s3.amazonaws.com/legal/deposit-account-disclosures.pdf',
          debit_cardholder_agreement: 'https://revela-public.s3.amazonaws.com/legal/debit-cardholder-agreement.pdf',
          electronic_disclosure_consent: 'https://revela-public.s3.amazonaws.com/legal/electronic-disclosure-consent.pdf'
        }.each do |path, url|
          get path.to_s.parameterize => redirect(url), as: path
        end
      end

      constraints format: :pdf do
        get '2024/10/18/p/msa' => redirect('https://revela-public.s3.amazonaws.com/legal/master-services-agreement-property-management.pdf')
        get '2024/10/18/g/msa' => redirect('https://revela-public.s3.amazonaws.com/legal/master-services-agreement-greek.pdf')
      end
    end

    scope module: 'landing_page' do
      get :careers
      get :services
      get :training
      get :research
      get :case_studies
      get :values
      get :multifamily
      get :singlefamily
      get :university
      get :legal
      scope :resources do
        get :management_lead_qualifier, path: :qualifier
      end
    end

    post '/contact' => 'landing_page#submit_contact_form', as: :submit_contact_form
  end
end
