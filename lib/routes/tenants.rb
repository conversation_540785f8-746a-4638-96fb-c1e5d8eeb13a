##
# Routes for /tenants/*
module Routes::Tenants
  extend Routes::Routing

  route do
    devise_scope :tenant do
      patch '/tenants/confirmation' =>
      'tenants/confirmations#update', as:
        :update_tenant_confirmation
    end

    devise_for :tenants,
               path_names: { sign_in: 'login' },
               controllers: {
                 confirmations: 'tenants/confirmations',
                 passwords: 'tenants/passwords',
                 registrations: 'tenants/registrations',
                 sessions: 'tenants/sessions'
               }

    namespace :tenants do
      root 'dashboard#show'
      get '/dashboard' => 'dashboard#show'
      patch '/dashboard' => 'dashboard#update'
      resource :account, only: %i[show update] do
        resources :scheduled_payments, except: :index do
          get :retry, on: :member
        end
        resources :payment_methods, only: %i[create destroy new]
      end
      resources :payment_plans
      resources :maintenance_surveys, only: %i[show update]
      resources :maintenance_tickets, only: %i[create index new show]
      resources :payments, only: %i[new create show] # Pay Balance
      resources :rent_invoices, only: %i[index show] do
        resources :payments, only: %i[new create] # Pay Invoice
      end
      resources :messages, only: %i[index show new create]
      resources :agreements, only: :show, path: 'agreements/:type'
      resources :documents, only: :index
      resources :inspections, only: %i[show update] do
        get :inspect, on: :member
        get :review, on: :member
        get '/inspect/questions/:question_id', to: 'inspections#question', on: :member
        patch :submit, on: :member
      end
      resources :utility_transfers, only: %i[edit update show]
      resource :member_profile, only: %i[show update]

      resources :member_onboarding, only: [], param: :id,
                                    controller: 'member_onboardings' do
        member do
          get :welcome
          post :pass_welcome
          get :member_profile
          post :create_member_profile
          get :guarantor_profile
          post :create_guarantor_profile
          get :risk_management_program
          post :select_risk_management_program
          get :membership_agreement
          get :membership_agreement_document
          post :sign_membership_agreement
          get :lease_agreement
          get :lease_agreement_document
          get :completion_summary
        end
        collection do
          delete :clear
        end
      end
    end
  end
end
