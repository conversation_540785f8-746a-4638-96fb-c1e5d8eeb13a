module Routes::Webhooks
  extend Routes::Routing

  route do # rubocop:disable Metrics/BlockLength
    # Twilio Voice Transcriptions
    namespace :voice do
      resources :transcriptions, only: :create do
        get :dial_hook, on: :collection
        post :voicebase_hook, on: :collection
      end
    end

    # Reference Services
    post '/reference_services_hook',
         to: 'leasing/background_checks#reference_services_hook'

    # SafeRent
    post '/saferent_hook',
         to: 'saferent/webhooks#create'

    # Inbound email processing
    mount_griddler('/inbound_parse')

    # TheClosingDocs webhook
    namespace :the_closing_docs do
      resource :webhook, only: :create
    end

    # Smart Rent
    namespace :smart_rent do
      resource :webhook, only: :create
    end

    # Tunisia
    namespace :tunisia do
      resource :webhook, only: :create
    end

    # Mailgun Webhook
    post '/mailgun/webhook', to: 'mailgun#webhook', as: :mailgun_wehbook

    # PayLease Transaction Notification and/or CashPay callbacks
    scope :paylease, as: :paylease do
      scope :transactions, as: :transactions, controller: :paylease_webhooks do
        post :processed
        post :cancelled
      end
    end

    # Inspectify
    namespace :inspectify do
      resources :webhooks, only: :create
    end

    namespace :reams do
      resources :webhooks, only: :create
    end

    namespace :zillow do
      namespace :webhooks do
        resources :leads, only: :create
      end
    end

    namespace :mercury do
      resources :webhooks, only: :create
    end
  end
end
