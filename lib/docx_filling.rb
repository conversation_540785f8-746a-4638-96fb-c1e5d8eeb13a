module DocxFilling
  def generate_docx(template: docx_template,
                    values: docx_values,
                    output_file: Tempfile.new(%w[docx .docx]))

    doc = DocxFilling::ReplaceUno.build(template.path)

    # Clean braces by removing nonsense tags from things like:
    #
    #   {xyz[12<garbage from M$ Word>3]}
    #
    # The idea of this regex is:
    #
    # { Find a starting {.
    #
    # .* Find anything.
    #
    # \K Reset the match to here, to simulate infinite positive lookbehind.
    #
    # (<[^>]*>)+ Match one or more tags.
    #
    # (?=.*}) Require trailing (unmatched) }.
    #
    # It runs a few times to handle multiple occurances.
    # 8.times { doc.replace(/{.*?\K(<[^>]*>)+(?=.*})/, '', true) }

    pdf_values.each do |key, value|
      doc.replace("{#{key}}", value, true)
    end

    doc.replace(/{[^\s]+(?<!signature|signature_date|signature_name|initials)}/, '', true)

    doc.commit(output_file.path)

    output_file
  end

  def self.remove_signature_tags(template)
    output_file = Tempfile.new(%w[docx .docx])

    doc = DocxFilling::ReplaceUno.build(template)

    doc.replace(/{[^\s]+(signature|signature_date|signature_name|initials)}/, '', true)

    doc.commit(output_file.path)

    output_file
  end
end
