class ActionTable::PayablesBatches < ActionTable::Base
  def columns
    [batch, status, created_at, processed_at, invoices, amount]
  end

  protected

  def frontend_sorting?
    false
  end

  private

  def batch
    {
      title: 'Batch',
      align: :left,
      width: :four,
      sort_key: :name,
      value: ->(batch) { batch.name },
      link: ->(batch) { accounting_payables_batch_path(batch) }
    }
  end

  def status
    {
      title: 'Status',
      width: :two,
      sort_key: :status,
      value: ->(batch) { batch.open? ? 'Open' : 'Processed' },
      label: ->(batch) { batch.open? ? :green : :blue }
    }
  end

  def created_at
    {
      title: 'Created At',
      sort_key: :created_at,
      value: ->(batch) { batch.created_at.to_fs(:short_datetime) }
    }
  end

  def processed_at
    {
      title: 'Processed At',
      sort_key: :closed_at,
      value: ->(batch) { batch.closed_at&.to_fs(:short_datetime) }
    }
  end

  def invoices
    {
      title: 'Invoices',
      width: :two,
      align: :right,
      sort_key: :invoices_count,
      value: ->(batch) { batch.invoices_count },
      sort: ->(batch) { batch.invoices_count }
    }
  end

  def amount
    {
      title: 'Amount',
      width: :two,
      align: :right,
      sort_key: :invoices_total_amount_cents,
      value: ->(batch) { batch.invoices_total_amount.format },
      sort: ->(batch) { batch.invoices_total_amount_cents }
    }
  end
end
