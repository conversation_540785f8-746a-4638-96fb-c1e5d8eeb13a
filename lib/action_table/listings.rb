class ActionTable::Listings < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    false
  end

  def columns
    [
      listing,
      property,
      floorplan,
      bedrooms,
      bathrooms,
      square_feet,
      price,
      availability,
      status,
      (zillow_feed if Feature.enabled?(:direct_zillow_syndication, Customer.current))
    ].compact
  end

  private

  def listing
    {
      title: 'Listing',
      width: :three,
      align: :left,
      sort_key: :listing,
      value: ->(floorplan) { floorplan.listing&.name || 'Add' },
      link: lambda do |floorplan|
        if floorplan.listing
          marketing_listing_path(floorplan.listing)
        else
          new_marketing_listing_path(floorplan_id: floorplan.id)
        end
      end
    }
  end

  def property
    {
      title: 'Property',
      sort_key: :property,
      align: :left,
      value: ->(floorplan) { floorplan.property.name },
      link: ->(floorplan) { floorplan.property.url }
    }
  end

  def floorplan
    {
      title: 'Floorplan',
      sort_key: :name,
      value: ->(floorplan) { floorplan.name }
    }
  end

  def price
    {
      title: 'Price',
      sort_key: :price,
      align: :right,
      value: ->(floorplan) { floorplan.price.format }
    }
  end

  def bedrooms
    {
      title: 'Bedrooms',
      sort_key: :bedrooms,
      value: ->(floorplan) { floorplan.bedrooms_formatted }
    }
  end

  def bathrooms
    {
      title: 'Bathrooms',
      sort_key: :bathrooms,
      value: ->(floorplan) { floorplan.bathrooms_formatted }
    }
  end

  def square_feet
    {
      title: 'Square Feet',
      sort_key: :square_feet,
      align: :right,
      value: ->(floorplan) { floorplan.square_feet_formatted }
    }
  end

  def availability
    {
      title: 'Availability',
      value: ->(floorplan) { floorplan.date_available_formatted }
    }
  end

  def status
    {
      title: 'Status',
      sort_key: :status,
      value: ->(floorplan) { floorplan.listing_status_text },
      label: ->(floorplan) { floorplan.listing_status_label }
    }
  end

  def zillow_feed
    {
      title: 'Zillow Feed',
      sort_key: :zillow_feed,
      value: ->(floorplan) { floorplan.listing&.zillow_feed? },
      view: lambda do |floorplan|
        tag.i(class: 'check circle green icon') if floorplan.listing&.zillow_feed?
      end
    }
  end
end
