class ActionTable::Accounting::Amounts < ActionTable::Base
  def frontend_sorting?
    false
  end

  def columns
    [
      date,
      (journal if view_context.request.path.include?('charts_of_accounts')),
      description,
      contact,
      reference,
      debits,
      credits
    ].compact
  end

  private

  def link
    query = if params.dig(:filters, :basis) == 'cash'
              { basis: 'cash' }
            else
              {}
            end

    lambda do |amount|
      accounting_journal_entry_path(amount.entry.journal_id, amount.entry_id, query)
    end
  end

  def date
    {
      title: 'Date',
      align: :center,
      width: :two,
      sort_key: :date,
      value: ->(amount) { amount.entry.date },
      link: link
    }
  end

  def description
    {
      title: 'Description',
      align: :left,
      width: :four,
      sort_key: :description,
      value: ->(amount) { amount.entry.description },
      link: link
    }
  end

  def journal
    {
      title: 'Journal',
      align: :left,
      sort_key: :journal,
      value: ->(amount) { amount.entry.journal.name },
      link: ->(amount) { accounting_journal_path(amount.entry.journal) }
    }
  end

  def contact
    {
      title: 'Contact',
      sort_key: :contact_name,
      align: :center,
      value: ->(amount) { amount.entry.contact_name }
    }
  end

  def reference
    {
      title: 'Reference',
      sort_key: :reference_number,
      align: :center,
      value: ->(amount) { amount.entry.reference_text }
    }
  end

  def debits
    {
      title: 'Debits',
      width: :two,
      sort_key: :debits,
      align: :right,
      value: ->(amount) { Money.new(amount.amount).format if amount.debit? }
    }
  end

  def credits
    {
      title: 'Credits',
      width: :two,
      sort_key: :credits,
      align: :right,
      value: ->(amount) { Money.new(amount.amount).format if amount.credit? }
    }
  end
end
