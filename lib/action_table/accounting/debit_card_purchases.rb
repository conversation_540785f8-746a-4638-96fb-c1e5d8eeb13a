class ActionTable::Accounting::DebitCardPurchases < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [
      date,
      vendor,
      description,
      paid_by,
      amount,
      status,
      actions
    ]
  end

  def date
    {
      title: 'Date',
      width: :two,
      value: ->(debit_card_purchase) { debit_card_purchase.formatted_date }
    }
  end

  def vendor
    {
      title: 'Vendor',
      value: ->(debit_card_purchase) { debit_card_purchase.vendor_name },
      link: lambda { |debit_card_purchase|
        vendor_path(debit_card_purchase.vendor) if debit_card_purchase.vendor.present?
      }
    }
  end

  def description
    {
      title: 'Description',
      value: ->(debit_card_purchase) { debit_card_purchase.description }
    }
  end

  def paid_by
    {
      title: 'Paid By',
      value: ->(debit_card_purchase) { debit_card_purchase.paid_by }
    }
  end

  def amount
    {
      title: 'Amount',
      width: :two,
      value: ->(debit_card_purchase) { debit_card_purchase.amount.format }
    }
  end

  def status
    {
      title: 'Status',
      value: ->(debit_card_purchase) { debit_card_purchase.formatted_status }
    }
  end

  def actions
    {
      title: 'Actions',
      width: :two,
      value: ->(debit_card_purchase) { debit_card_purchase.action_links }
    }
  end
end
