class ActionTable::Properties < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [
      property,
      entity,
      portfolio,
      region,
      units,
      tenants,
      occupancy,
      custom_status,
      default_onboarding,
      auto_assignment_onboarding
    ].compact
  end

  private

  def portfolio
    return nil if portfolio_mode?

    {
      title: 'Portfolio',
      sort_key: :portfolio,
      align: :left,
      width: :two,
      value: ->(property) { property.portfolio.name },
      link: ->(property) { portfolio_path(property.portfolio) }
    }
  end

  def entity
    {
      title: 'Entity',
      sort_key: :company,
      align: :left,
      width: :two,
      value: ->(property) { property.company.name },
      link: ->(property) { property.company.url }
    }
  end

  def property
    {
      title: 'Property',
      sort_key: :name,
      align: :left,
      width: :two,
      value: lambda do |property|
        if property.setup?
          property.name
        else
          "(Requires Setup) #{property.name}"
        end
      end,
      link: ->(property) { property_path(property) }
    }
  end

  def region
    {
      title: 'Region',
      sort_key: :region,
      align: :left,
      width: :three,
      value: lambda do |property|
        [property.address.region_name, property.address.city].join(' / ')
      end
    }
  end

  def units
    {
      title: 'Units',
      sort_key: :unit_count,
      align: :right,
      value: ->(property) { property.occupancy_count.unit_count }
    }
  end

  def tenants
    {
      title: I18n.t('tenant_term').pluralize,
      sort_key: :tenant_count,
      align: :right,
      value: ->(property) { property.occupancy_count.tenant_count }
    }
  end

  def occupancy
    {
      title: 'Occupancy',
      sort_key: :occupancy,
      align: :right,
      value: lambda { |property|
        (property.occupancy_count.occupancy * 100).round(2)
      }
    }
  end

  def custom_status
    unless Customer.current_subdomain.start_with?(
      'mph-sandbox', 'marketplacehomes'
    )
      return nil
    end

    {
      title: 'Status',
      sort_key: :custom_status,
      value: ->(property) { property.custom_status&.titleize }
    }
  end

  def default_onboarding
    return nil unless Feature.enabled?(:onboarding_setup, Customer.current)

    return nil if Feature.enabled?(:onboarding_enhancements, Customer.current)

    {
      title: 'Default Onboarding',
      width: :three,
      value: ->(property) { property.default_member_onboarding_configuration&.name },
      link: lambda { |property|
              if property&.default_member_onboarding_configuration
                onboarding_path(property.default_member_onboarding_configuration)
              end
            }
    }
  end

  def auto_assignment_onboarding
    return nil unless Feature.enabled?(:onboarding_setup, Customer.current)

    return nil unless Feature.enabled?(:onboarding_enhancements, Customer.current)

    {
      title: 'Auto Assignment Onboarding',
      width: :three,
      value: ->(property) { property.auto_assign_onboarding_configuration&.name },
      link: lambda { |property|
              if property&.auto_assign_onboarding_configuration
                onboarding_path(property.auto_assign_onboarding_configuration)
              end
            }
    }
  end

  def portfolio_mode?
    view_context.controller_name == 'portfolios'
  end
end
