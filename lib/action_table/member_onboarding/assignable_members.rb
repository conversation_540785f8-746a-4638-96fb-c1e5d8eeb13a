class ActionTable::MemberOnboarding::AssignableMembers < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [name, email, property, membership_tags, active_onboarding]
  end

  private

  def name
    {
      title: 'Name',
      align: :left,
      sort_key: :last_name,
      value: ->(member) { member.name },
      link: ->(member) { tenant_path(member) }
    }
  end

  def email
    {
      title: 'Email',
      align: :left,
      sort_key: :email,
      value: ->(member) { member.email }
    }
  end

  def property
    {
      title: 'Property',
      align: :center,
      value: ->(member) { member.onboardable_property&.name },
      link: lambda do |member|
        property = member.onboardable_property
        return if property.blank?

        property_path(property)
      end
    }
  end

  def membership_tags
    {
      title: 'Membership Tags',
      align: :left,
      value: lambda { |member|
        member.simple_agreements.map do |agreement|
          agreement.tags.pluck(:tag)
        end.flatten.uniq.join(', ')
      }
    }
  end

  def active_onboarding
    {
      title: 'Active Onboarding',
      align: :center,
      sort_key: :active_onboarding,
      value: ->(member) { member.active_onboarding_assignment&.configuration&.name },
      link: lambda do |member|
        onboarding = member.active_onboarding_assignment&.configuration
        return if onboarding.blank?

        onboarding_assignments_path(onboarding_id: onboarding.id)
      end
    }
  end
end
