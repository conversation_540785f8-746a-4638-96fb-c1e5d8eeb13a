class ActionTable::MemberOnboarding::Assignments < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [name, property, tags, status, assigned, completed, actions]
  end

  private

  def name
    {
      title: 'Name',
      align: :left,
      sort_key: :last_name,
      value: ->(assignment) { assignment.tenant.name },
      link: ->(assignment) { tenant_path(assignment.tenant) }
    }
  end

  def property
    {
      title: 'Property',
      align: :left,
      value: ->(assignment) { assignment.tenant.onboardable_property&.name },
      link: lambda do |assignment|
        property = assignment.tenant.onboardable_property
        return if property.blank?

        property_path(property)
      end
    }
  end

  def tags
    {
      title: 'Tags',
      value: lambda { |assignment|
        assignment.tenant.simple_agreement_memberships.flat_map do |m|
          m.simple_agreement.tags.pluck(:tag)
        end.uniq.sort.join(', ')
      }
    }
  end

  def status
    {
      title: 'Status',
      align: :left,
      sort_key: :status,
      value: ->(assignment) { assignment.completed? ? 'Completed' : 'Pending' },
      status_color: ->(assignment) { assignment.completed? ? 'green' : 'grey' },
      tooltip: ->(assignment) { assignment.completed? ? 'Completed' : 'Pending' }
    }
  end

  def assigned
    {
      title: 'Assigned',
      align: :right,
      sort_key: :created_at,
      value: ->(assignment) { assignment.created_at.to_fs(:short_date) }
    }
  end

  # TODO: update this
  def completed
    {
      title: 'Completed',
      align: :right,
      sort_key: :completed,
      value: lambda { |assignment|
        assignment.completed_at&.to_fs(:short_date)
      }
    }
  end

  def actions
    {
      title: 'Actions',
      value: ->(assignment) { assignment.completed? ? '' : 'Unassign' },
      link: lambda { |assignment|
        unassign_modal_onboarding_assignment_path(assignment.configuration, assignment)
      },
      class_names: ->(assignment) { assignment.completed? ? '' : 'unassign-modal-link' }
    }
  end
end
