class ActionTable::Collections::NoticeSent < ActionTable::Collections::Late
  def columns
    [tenant, property, region, phone, balance, demand_balance, last_sent,
     expiration_date]
  end

  def demand_balance
    if Customer.current_subdomain == 'gebraelmgmt' &&
       Time.zone.today.before?(Date.new(2022, 10, 1))
      return nil
    end

    {
      title: 'Demand Balance',
      sort_key: :demand_letter_balance,
      value: ->(row) { row.demand_letter_balance.format }
    }
  end

  def last_sent
    {
      title: 'Date Sent',
      sort_key: :demand_letter_last_sent_at,
      value: ->(row) { row.demand_letter_last_sent_at.to_fs(:short_date) }
    }
  end

  def expiration_date
    {
      title: 'Expiration Date',
      sort_key: :demand_letter_expires_at,
      value: ->(row) { row.demand_letter_expires_at.to_fs(:short_date) }
    }
  end
end
