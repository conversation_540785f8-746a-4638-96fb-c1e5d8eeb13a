class ActionTable::Collections::Late < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [
      tenant,
      property,
      region,
      phone,
      balance,
      (tags if mph?),
      actions
    ].compact
  end

  private

  def tenant
    {
      title: I18n.t('tenant_term'),
      sort_key: :tenant,
      align: :left,
      value: ->(row) { row.primary_tenant.name },
      link: ->(row) { tenant_path(row.primary_tenant) }
    }
  end

  def property
    {
      title: 'Property',
      sort_key: :property,
      align: :left,
      value: ->(row) { row.property.name },
      link: ->(row) { property_path(row.property) }
    }
  end

  def region
    {
      title: 'State',
      align: :center,
      value: ->(row) { row.property.address.region_code }
    }
  end

  def phone
    {
      title: 'Phone',
      sort_key: :phone,
      value: ->(row) { row.primary_tenant.formatted_phone }
    }
  end

  def balance
    {
      title: 'Late Balance',
      sort_key: :overdue_resident_balance,
      align: :right,
      value: ->(row) { row.overdue_resident_balance.format },
      link: ->(row) { reports_ledger_path(filters: { lease_id: row.id }) }
    }
  end

  def tags
    {
      title: 'Tags',
      align: :center,
      value: ->(row) { row.tags.map(&:tag).sort.join(', ') }
    }
  end

  def actions
    {
      title: nil,
      value: ->(_) { 'Demand Letter...' },
      link: ->(_) { '#' },
      link_options: lambda do |row|
        {
          data: {
            action: 'click->collections--demand-modal#clickDemandLetterLink',
            id: row.id
          }
        }
      end
    }
  end

  def mph?
    Customer.current_subdomain.start_with?('mph', 'marketplacehomes')
  end
end
