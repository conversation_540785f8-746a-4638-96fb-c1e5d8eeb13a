class ActionTable::Collections::Evicting < ActionTable::Collections::Late
  def columns
    super[...-2] + [eviction_balance, started_at, assigned_vendor, view, packet]
  end

  private

  def eviction_balance
    {
      title: 'Eviction Balance',
      sort_key: :eviction_overdue_balance_cents,
      value: ->(row) { row.eviction.overdue_balance.format },
      align: :right
    }
  end

  def started_at
    {
      title: 'Started At',
      sort_key: :eviction_created_at,
      value: ->(row) { row.eviction.created_at.to_fs(:short_date) }
    }
  end

  def assigned_vendor
    {
      title: 'Assigned Vendor',
      value: ->(row) { row.eviction.assigned_vendor&.name },
      link: ->(row) { row.eviction.assigned_vendor&.url }
    }
  end

  def view
    {
      title: 'Actions',
      colspan: 2,
      value: ->(_) { 'View' },
      link: ->(row) { operations_collections_eviction_path(row.eviction) }
    }
  end

  def packet
    {
      title: nil,
      colspan: 0,
      value: ->(_) { 'Download Packet' },
      link: ->(row) { operations_collections_evicting_packet_path(row.eviction) },
      link_options: ->(_) { { data: { background_download: true } } }
    }
  end
end
