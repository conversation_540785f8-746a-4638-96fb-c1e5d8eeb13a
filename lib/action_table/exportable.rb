module ActionTable::Exportable
  include ::Exportable::XLSX

  def export_title
    self.class.name.demodulize.titleize
  end

  def export_description
    ''
  end

  def as_xlsx
    {
      title: export_title,
      subheader: export_description,
      header: {
        cells: exportable_columns.map { |col| { value: col[:title], type: 'b' } }
      },
      rows: rows
    }
  end

  private

  def exportable_columns
    columns.reject { |column| column[:title] == 'Actions' }
  end

  def rows
    collection.map do |item|
      {
        cells: exportable_columns.map { |col| { value: col[:value].call(item) } }
      }
    end
  end
end
