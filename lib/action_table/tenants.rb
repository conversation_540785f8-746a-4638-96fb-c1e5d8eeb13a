class ActionTable::Tenants < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [name, email, phone, property, tags]
  end

  def name
    {
      title: 'Name',
      align: :left,
      sort_key: :last_name,
      value: ->(tenant) { tenant.name },
      link: ->(tenant) { tenant_path(tenant) }
    }
  end

  def email
    {
      title: 'Email',
      sort_key: :email,
      value: ->(tenant) { tenant.email },
      link: ->(tenant) { "mailto:#{tenant.email}" }
    }
  end

  def phone
    {
      title: 'Phone',
      sort_key: :phone,
      value: ->(tenant) { tenant.formatted_phone }
    }
  end

  def property
    {
      title: 'Property',
      value: ->(tenant) { tenant.current_property.name },
      link: ->(tenant) { property_path(tenant.current_property) }
    }
  end

  def tags
    {
      title: 'Tags',
      value: ->(tenant) { tenant.tags.map(&:tag).join(', ') }
    }
  end
end
