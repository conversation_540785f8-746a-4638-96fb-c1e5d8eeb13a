class ActionTable::Estimates < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    false
  end

  def columns
    [
      estimate,
      created_at,
      service_area,
      prepared_by,
      inspection,
      work_order
    ]
  end

  private

  def estimate
    {
      title: 'Estimate',
      value: ->(estimate) { estimate.amount.format },
      link: ->(estimate) { maintenance_estimate_path(estimate) }
    }
  end

  def created_at
    {
      title: 'Created',
      sort_key: :created_at,
      value: ->(estimate) { estimate.created_at.to_fs(:short_datetime) },
      link: ->(estimate) { maintenance_estimate_path(estimate) }
    }
  end

  def prepared_by
    {
      title: 'Prepared By',
      sort_key: :prepared_by,
      value: ->(estimate) { estimate.prepared_by.name },
      link: ->(estimate) { estimate.prepared_by.url }
    }
  end

  def work_order
    {
      title: 'Work Order',
      value: ->(estimate) { estimate.maintenance_ticket&.subject },
      link: ->(estimate) { estimate.maintenance_ticket&.url }
    }
  end

  def inspection
    {
      title: 'Inspection',
      value: ->(estimate) { estimate.inspection&.name },
      link: lambda do |estimate|
        operations_inspection_path(estimate.inspection) if estimate.inspection
      end
    }
  end

  def service_area
    {
      title: 'Service Area',
      value: lambda do |estimate|
        area = estimate.service_area
        area.try(:qualified_name) || area.name
      end,
      link: ->(estimate) { estimate.service_area.url }
    }
  end
end
