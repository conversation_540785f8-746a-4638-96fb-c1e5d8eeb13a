class ActionTable::Payments < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    false
  end

  def columns
    [date, description, payer, payee, method, reference, status, amount]
  end

  def date
    {
      title: 'Date',
      sort_key: :date,
      value: ->(payment) { payment.date.to_fs(:short_date) },
      link: ->(payment) { payment.url }
    }
  end

  def description
    {
      title: 'Description',
      width: :three,
      sort_key: :description,
      value: ->(payment) { payment.description },
      link: ->(payment) { payment.url }
    }
  end

  def payer
    {
      title: 'Payer',
      width: :three,
      value: ->(payment) { payment.payer.name },
      link: ->(payment) { payment.payer.url }
    }
  end

  def payee
    {
      title: 'Payee',
      width: :three,
      value: ->(payment) { payment.payee.name },
      link: ->(payment) { payment.payee.url }
    }
  end

  def status
    {
      title: 'Status',
      sort_key: :status,
      value: ->(payment) { payment.status_text },
      label: ->(payment) { "small basic #{payment.status_color}" }
    }
  end

  def reference
    {
      title: 'Reference',
      value: lambda do |payment|
        payment.check_number || payment.short_reference_number
      end
    }
  end

  def method
    {
      title: 'Method',
      sort_key: :kind,
      value: ->(payment) { payment.kind_text }
    }
  end

  def amount
    {
      title: 'Amount',
      align: :right,
      sort_key: :amount,
      value: ->(payment) { payment.amount.format },
      link: ->(payment) { payment.url }
    }
  end
end
