# ActionTable for payables & receivables
class ActionTable::Invoices < ActionTable::Base
  def initialize(view_context, collection, accounting_side)
    super(view_context, collection)
    @accounting_side = accounting_side
  end

  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [date, invoice_number, description, buyer, seller, status, amount, balance]
  end

  private

  def invoice_path(invoice)
    if @accounting_side == :payables
      accounting_payables_invoice_path(invoice)
    else
      accounting_receivables_invoice_path(invoice)
    end
  end

  def date
    {
      title: 'Date',
      width: :two,
      sort_key: :physical_date,
      value: ->(invoice) { invoice.physical_date.to_fs(:short_date) },
      link: ->(invoice) { invoice_path(invoice) }
    }
  end

  def invoice_number
    {
      title: 'Invoice #',
      width: :two,
      sort_key: :invoice_number,
      value: ->(invoice) { invoice.invoice_number },
      link: ->(invoice) { invoice_path(invoice) }
    }
  end

  def description
    {
      title: 'Description',
      width: :four,
      sort_key: :description,
      value: ->(invoice) { invoice.description },
      link: ->(invoice) { invoice_path(invoice) }
    }
  end

  def buyer
    {
      title: 'Buyer',
      width: :three,
      value: ->(invoice) { invoice.buyer },
      link: ->(invoice) { invoice.buyer.url }
    }
  end

  def seller
    {
      title: 'Seller',
      width: :three,
      value: ->(invoice) { invoice.seller },
      link: ->(invoice) { invoice.seller.url }
    }
  end

  def status
    {
      title: 'Status',
      width: :two,
      value: ->(invoice) { invoice.status },
      label: ->(invoice) { "small basic #{invoice.status_color}" }
    }
  end

  def amount
    {
      title: 'Amount',
      width: :two,
      align: :right,
      sort_key: :amount_cents,
      value: ->(invoice) { invoice.amount.format },
      link: ->(invoice) { invoice_path(invoice) }
    }
  end

  # TODO: Finish sorting by balance
  def balance
    {
      title: 'Balance',
      width: :two,
      align: :right,
      # sort_key: :balance_cents,
      value: ->(invoice) { invoice.balance.format },
      link: ->(invoice) { invoice_path(invoice) }
    }
  end
end
