class ActionTable::MemberOnboardings < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [
      name,
      created_at,
      portfolio,
      defaults,
      assigned,
      copy
    ]
  end

  def name
    {
      title: 'Onboarding Name',
      sort_key: :name,
      value: ->(onboarding) { onboarding.name },
      link: ->(onboarding) { onboarding_path(onboarding) }
    }
  end

  def created_at
    {
      title: 'Date Created',
      sort_key: :created_at,
      value: ->(onboarding) { onboarding.created_at.to_fs(:short_datetime) }
    }
  end

  def portfolio
    {
      title: 'Portfolio',
      value: ->(onboarding) { onboarding.portfolio }
    }
  end

  def assigned
    {
      title: 'Assigned',
      value: lambda { |onboarding|
        contents = []
        if onboarding.at_least_one_auto_assignment?
          contents << tag.img(src: '/images/auto_assign.svg', style: 'margin-right: 3px;')
        end
        contents << onboarding.assigned_members(user)
        safe_join(contents)
      },
      link: ->(onboarding) { onboarding.assigned_members_path },
      tooltip: ->(onboarding) { 'Auto assignments on' if onboarding.at_least_one_auto_assignment? }
    }
  end

  def defaults
    {
      title: properties_title,
      value: ->(onboarding) { onboarding.default_properties },
      link: ->(onboarding) { default_properties_onboarding_path(onboarding) },
      link_options: lambda { |onboarding|
        {
          class: 'properties-sidebar-button',
          data: {
            onboarding_id: onboarding.id,
            onboarding_name: onboarding.name,
            onboarding_portfolio_id: onboarding.portfolio.id,
            onboarding_portfolio_name: onboarding.portfolio.name,
            onboarding_properties_count: onboarding.default_properties,
            new_version: Feature.enabled?(:onboarding_enhancements, Customer.current)
          }
        }
      }
    }
  end

  def copy
    {
      value: ->(_onboarding) { 'Copy' },
      link: ->(onboarding) { copy_onboarding_path(onboarding) },
      link_options: lambda { |_onboarding|
        {
          data: {
            method: :post
          }
        }
      }
    }
  end

  def properties_title
    if Feature.enabled?(:onboarding_enhancements, Customer.current)
      'Property'
    else
      'Defaults'
    end
  end
end
