class ActionTable::Loans < ActionTable::Agreements
  def columns
    [
      loan_number,
      borrower,
      property,
      first_payment_date,
      maturity_date,
      principal,
      interest_rate,
      tags
    ]
  end

  private

  def loan_number
    value = if CustomerSpecific::Behavior.lending_loans_property_loan_index?
              ->(loan) { "#{loan.loan_number} [Note #{loan.property_loan_index}]" }
            else
              ->(loan) { loan.loan_number }
            end

    {
      title: 'Loan',
      sort_key: :loan_number,
      value:,
      link: ->(loan) { lending_loan_path(loan) },
      width: :three
    }
  end

  def borrower
    {
      title: 'Borrower',
      sort_key: :borrower,
      value: ->(loan) { loan.borrower.name },
      link: ->(loan) { loan.borrower.url }
    }
  end

  def property
    {
      title: 'Property',
      sort_key: :property,
      value: ->(loan) { loan.property.name },
      link: ->(loan) { loan.property.url },
      permission: can.show_portfolio_properties?
    }
  end

  def first_payment_date
    {
      title: 'First Payment Date',
      sort_key: :first_payment_date,
      value: ->(loan) { loan.first_payment_date.to_fs(:short_date) },
      link: ->(loan) { lending_loan_path(loan) }
    }
  end

  def maturity_date
    {
      title: 'Maturity Date',
      sort_key: :maturity_date,
      value: ->(loan) { loan.maturity_date.to_fs(:short_date) },
      link: ->(loan) { lending_loan_path(loan) }
    }
  end

  def principal
    {
      title: 'Principal',
      sort_key: :principal_cents,
      value: ->(loan) { loan.principal.format },
      link: ->(loan) { lending_loan_path(loan) }
    }
  end

  def interest_rate
    {
      title: 'Interest Rate',
      sort_key: :interest_rate,
      value: ->(loan) { loan.interest_rate_percentage },
      link: ->(loan) { lending_loan_path(loan) }
    }
  end
end
