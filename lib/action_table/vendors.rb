class ActionTable::Vendors < ActionTable::Base
  def frontend_sorting?
    false
  end

  def columns
    [name, type, contact, email, phone, tags]
  end

  private

  def name
    {
      title: 'Name',
      align: :left,
      width: :three,
      sort_key: :name,
      value: ->(vendor) { vendor.name },
      link: ->(vendor) { vendor_path(vendor) }
    }
  end

  def type
    {
      title: 'Type',
      align: :center,
      width: :two,
      sort_key: :kind,
      value: ->(vendor) { vendor.kind.titleize }
    }
  end

  def contact
    {
      title: 'Contact',
      align: :left,
      value: ->(vendor) { vendor.primary_contact&.name }
    }
  end

  def email
    {
      title: 'Email',
      align: :left,
      value: ->(vendor) { vendor.email }
    }
  end

  def phone
    {
      title: 'Phone',
      align: :center,
      width: :two,
      value: ->(vendor) { vendor.formatted_phone }
    }
  end

  def tags
    {
      title: 'Tags',
      align: :center,
      value: ->(vendor) { vendor.tags.join(', ') }
    }
  end
end
