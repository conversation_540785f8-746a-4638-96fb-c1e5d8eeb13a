class ActionTable::Taxes::TenNinetyNines::Overview < ActionTable::Base
  def table_class_names
    super.without('fixed') + ['overview_1099']
  end

  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [year, payer, payee, form_name, payments_total, adjustment, status, issue,
     show]
  end

  def year
    {
      title: 'Activity Year',
      value: ->(item) { item.year }
    }
  end

  def payer
    {
      title: 'Payer',
      value: ->(item) { item.payer.name },
      link: ->(item) { item.payer.url }
    }
  end

  def payee
    {
      title: 'Payee',
      value: ->(item) { item.payee.name },
      link: ->(item) { item.payee.url }
    }
  end

  def form_name
    {
      title: 'Form',
      value: ->(item) { item.form_name }
    }
  end

  def payments_total
    {
      title: 'Activity Total',
      value: ->(item) { item.payments_total.format },
      **modal_link
    }
  end

  def adjustment
    {
      title: 'Adjustment',
      value: ->(item) { item.adjustment.format },
      **modal_link
    }
  end

  def status
    {
      title: 'Status',
      value: ->(item) { item.combo_status.titleize },
      **modal_link(link_class: 'status')
    }
  end

  def issue
    {
      title: 'Issue',
      value: ->(item) { item.issues },
      **modal_link(link_class: 'issues')
    }
  end

  def show
    {
      title: 'Actions',
      value: ->(_) { 'View' },
      **modal_link
    }
  end

  def modal_link(link_class: '')
    {
      class: 'view-payee-modal-btn',
      link: ->(item) { item.modal_path },
      link_options: lambda { |_|
        {
          data: { modal: :view_ten_ninety_nine_modal },
          class: link_class
        }
      }
    }
  end
end
