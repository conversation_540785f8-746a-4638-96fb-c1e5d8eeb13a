class ActionTable::Base
  extend Service

  include ActionView::Context
  include ActionView::Helpers
  include Rails.application.routes.url_helpers
  include SortHelper
  include ActionTable::Exportable
  include ActionTable::MultiSelection

  attr_reader :view_context, :collection, :permitted_any_selection_items

  def initialize(view_context, collection)
    @view_context = view_context
    @collection = collection
    @permitted_any_selection_items = permitted_any_selection_items?
  end

  def call
    render_table + render_script
  end

  protected

  def render_table
    tag.table(class: table_class, data: table_data) do
      concat(render_table_header)
      concat(render_table_body)
    end
  end

  def render_table_header
    row = tag.tr do
      concat(master_checkbox) if multi_selection? && permitted_any_selection_items

      columns.each do |column|
        cell = column[:title]

        header_alignment = column[:header_align] || :center
        alignment = "#{header_alignment} aligned"

        width = "#{column[:width]} wide" if column[:width]

        extra = [alignment, width].compact.join(' ')

        klass = if column[:sort_key]
                  css_sorted(params[:sort], column[:sort_key], extra)
                elsif frontend_sorting? && column[:sort] == :nosort
                  "#{extra} no-sort"
                elsif frontend_sorting?
                  extra
                else
                  "#{extra} no-sort"
                end

        colspan = column[:colspan] || 1

        if !frontend_sorting? && column[:sort_key]
          data = {
            sorted_index_target: 'column',
            sort_column: column[:sort_key]
          }
        end

        next unless colspan.positive?

        concat(tag.th(cell, class: klass, colspan: colspan, data: data))
      end
    end

    tag.thead(row)
  end

  def render_table_body
    tag.tbody do
      collection.each do |resource|
        concat(render_table_row(resource))
      end
    end
  end

  def render_table_row(resource)
    tag.tr(class: table_row_class) do
      concat(row_checkbox(resource)) if multi_selection? && permitted_any_selection_items

      columns.each do |column|
        render_table_cell(column, resource)
      end
    end
  end

  def render_table_cell(column, resource)
    value = column[:value][resource]

    permitted = column[:permission] != false

    if column[:permission].respond_to? :call
      permitted = column[:permission][resource] != false
    end

    disabled = column[:disabled] && column[:disabled][resource]

    if column[:icon] && (icon = column[:icon][resource])
      original_value = value

      value = tag.span(class: 'label-content') do
        concat(tag.span(original_value, class: 'text'))

        concat(tag.i(class: class_names('ui', icon, 'icon')))
      end
    end

    if column[:status_color] && (status_color = column[:status_color][resource])
      original_value = value

      value = tag.span(class: 'label-content') do
        concat(tag.i(class: "circle #{status_color} small icon"))
        concat(tag.span(original_value, class: 'text'))
      end
    end

    cell = if column[:link] && !disabled && permitted
             href = column[:link][resource]

             if href
               options = if column[:link_options]
                           column[:link_options][resource]
                         else
                           {}
                         end
               tag.a(value, href: href, **options)
             else
               value
             end
           elsif column[:label] && (label_class = column[:label][resource])
             tag.label(
               value,
               class: "ui compact fluid #{label_class} label"
             )
           elsif column[:view]
             column[:view][resource]
           else
             value
           end

    alignment = "#{column[:align] || 'center'} aligned"

    klass = alignment

    klass += ' disabled' if disabled || !permitted

    data = if column[:data]
             column[:data][resource]
           else
             {}
           end

    klass += " #{column[:class_names][resource]}" if column[:class_names] && column[:class_names][resource]

    if column[:sort] && column[:sort] != :nosort
      data['sort-value'] = column[:sort][resource]
    end

    tooltip = column[:tooltip][resource] if column[:tooltip]

    tooltip ||= value

    concat(tag.td(cell, class: klass, data: data, title: tooltip))
  end

  def render_script
    javascript_tag "$('.ui.sortable.table').tablesort();" if frontend_sorting?
  end

  def table_class
    table_class_names.join(' ')
  end

  def table_class_names
    %w[
      ui
      unstackable
      sortable
      very basic
      striped
      selectable
      compact
      single line
      fixed
      table
      action-table
    ]
  end

  def table_data
    { controller: 'sorted-index' } unless frontend_sorting?
  end

  # Use jquery tablesort to sort on the frontend, instead of URL sorting
  def frontend_sorting?
    true
  end

  def h
    Draper::ViewContext.current
  end

  def permitted_any_selection_items?
    if view_context.instance_variable_defined?(:@permitted_any_selection_items)
      view_context.instance_variable_get(:@permitted_any_selection_items)
    else
      true
    end
  end

  def table_row_class
    table_row_class_names.join(' ')
  end

  def table_row_class_names
    []
  end

  def user
    h.current_property_manager
  end

  delegate :params, to: :view_context
  delegate :can, to: :user
end
