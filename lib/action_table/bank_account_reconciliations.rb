class ActionTable::BankAccountReconciliations < ActionTable::Base
  def columns
    [date, status, beginning, ending, statement]
  end

  def date
    {
      title: 'Statement Date',
      align: :left,
      value: ->(rec) { rec.statement_date.to_fs(:human_date) },
      link: ->(rec) { organization_bank_account_reconciliation_path(rec.bank_account, rec) }
    }
  end

  def status
    {
      title: 'Status',
      value: ->(rec) { rec.submitted? ? 'Submitted' : 'Pending' },
      label: ->(rec) { rec.submitted? ? :green : :blue }
    }
  end

  def beginning
    {
      title: 'Beginning Balance',
      value: ->(rec) { rec.statement_beginning_balance.format },
      sort: ->(rec) { rec.statement_beginning_balance.to_i }
    }
  end

  def ending
    {
      title: 'Ending Balance',
      value: ->(rec) { rec.statement_ending_balance.format },
      sort: ->(rec) { rec.statement_ending_balance.to_i }
    }
  end

  def statement
    {
      title: 'Statement',
      value: ->(rec) { rec.statement.present? ? 'Download' : nil },
      link: lambda do |rec|
        rails_blob_path(rec.statement) if rec.statement.present?
      end
    }
  end
end
