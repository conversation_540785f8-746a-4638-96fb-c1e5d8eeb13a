class ActionTable::Agreements < ActionTable::Base
  def frontend_sorting?
    false
  end

  def multi_selection?
    true
  end

  def columns
    [type, first_name, last_name, property, amount, start_date, end_date, tags]
  end

  private

  def type
    {
      title: 'Type',
      value: lambda do |agreement|
        case agreement
        when Lease then 'Lease'
        when Agreements::SimpleAgreement then agreement.agreement_type.name
        else 'Unknown'
        end
      end,
      link: ->(agreement) { agreement.url }
    }
  end

  def first_name
    {
      title: 'First Name',
      sort_key: :first_name,
      value: ->(agreement) { agreement.primary_tenant.first_name },
      link: ->(agreement) { tenant_path(agreement.primary_tenant) },
      permission: can.show_portfolio_tenants?
    }
  end

  def last_name
    {
      title: 'Last Name',
      sort_key: :last_name,
      value: ->(agreement) { agreement.primary_tenant.last_name },
      link: ->(agreement) { tenant_path(agreement.primary_tenant) },
      permission: can.show_portfolio_tenants?
    }
  end

  def property
    {
      title: 'Property',
      sort_key: :property,
      value: ->(agreement) { agreement.property.name },
      link: ->(agreement) { property_path(agreement.property) },
      permission: can.show_portfolio_properties?
    }
  end

  def amount
    {
      title: 'Amount',
      value: ->(agreement) { agreement.amount.format },
      link: ->(agreement) { agreement.url }
    }
  end

  def start_date
    {
      title: 'Start',
      sort_key: :start_date,
      value: ->(agreement) { agreement.formatted_start_date }
    }
  end

  def end_date
    {
      title: 'End',
      sort_key: :end_date,
      value: ->(agreement) { agreement.formatted_end_date }
    }
  end

  def tags
    {
      title: 'Tags',
      align: :center,
      value: ->(agreement) { agreement.tags.map(&:tag).sort.join(', ') }
    }
  end
end
