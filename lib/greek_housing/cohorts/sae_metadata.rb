class GreekHousing::Cohorts::SaeMetadata
  attr_reader :cohort

  def initialize(cohort:)
    # Because of how lease_installments is overridden in the new member
    # onboarding, this wants to be the legacy cohort.
    @cohort = GreekHousing::Cohort.new(property: cohort.property)
  end

  delegate :property, to: :cohort

  def metadata_attributes
    installments = cohort.lease_installments

    return unless installments

    one_year = installments.length == 2
    two_year = installments.length == 4 || (quarterly? && installments.length == 6)
    three_year = installments.length == 6 || !quarterly?

    year_one_installments = []
    year_two_installments = []
    year_three_installments = []

    if one_year
      year_one_installments = installments
    elsif two_year
      split = installments.length / 2
      splits = installments.each_slice(split).to_a
      year_one_installments = splits.first
      year_two_installments = splits.second
    elsif three_year
      split = installments.length / 3
      splits = installments.each_slice(split).to_a
      year_one_installments = splits.first
      year_two_installments = splits.second
      year_three_installments = splits.third
    else
      fail 'Unrecognized number of installments'
    end

    chapter = property.company

    installment_descriptions = installment_descriptions()

    {
      chapter_name: chapter.name,
      landlord_name: landlord(property).name,
      chapter_state_with_prefix: region_with_prefix(chapter.address),
      chapter_address: chapter.address.simple_address,
      year_one_description: year_description(year_one_installments),
      year_one_amount: cohort.lease_entry_year_amount(year_one_installments).format,
      year_two_description: year_description(year_two_installments),
      year_two_amount: cohort.lease_entry_year_amount(year_two_installments).format,
      year_three_description: year_description(year_three_installments),
      year_three_amount: cohort.lease_entry_year_amount(year_three_installments).format,
      master_lease_start_date: master_lease_start_date(property),
      university_name: university_name(property),
      installment_description_0: installment_descriptions[0],
      installment_description_1: installment_descriptions[1],
      installment_description_2: installment_descriptions[2],
      installment_description_3: installment_descriptions[3],
      installment_description_4: installment_descriptions[4],
      installment_description_5: installment_descriptions[5]
    }
  end

  # FALL/SPRING SEMESTER; Fall Sublease Term commences at 5:00 p.m. on August
  # 28, 2021, and Fall Sublease Term terminates at 5:00 p.m. on December 17,
  # 2021. Spring Sublease Term commences at 5:00 p.m. on January 7, 2022, and
  # Spring Sublease Term terminates at 5:00 p.m. on May 6, 2022.
  def year_description(installments)
    return '' if installments.empty?

    prefix = installments.map { |i| i[:name].upcase }.join('/')

    prefix += case installments.count
              when 2 then ' SEMESTER;'
              when 3 then ' QUARTER;'
              end

    terms = installments.map do |installment|
      name = installment[:name]
      start_date = installment[:start_date].to_fs(:human_date)
      end_date = installment[:end_date].to_fs(:human_date)
      "#{name} Sublease Term commences at 5:00 p.m. on #{start_date}, and #{name} Sublease Term terminates at 12:00 p.m. on #{end_date}."
    end

    [prefix, terms].flatten.join(' ')
  end

  # TODO: Copied from MembershipAgreement::FillDocument
  def region_with_prefix(address)
    name = address.region_name

    if name.start_with?(/A|E|I|O|U/)
      "an #{name}"
    else
      "a #{name}"
    end
  end

  # TODO: Copied from MembershipAgreement::FillDocument
  def landlord(property)
    landlord_name = case property.name
                    when 'Georgia Phi'
                      'Georgia Phi Housing Corporation'
                    when 'Indiana Zeta'
                      'Indiana Zeta Housing Corporation'
                    when 'North Carolina Omega'
                      'North Carolina Omega House Corporation'
                    when 'Iowa Beta'
                      'Sigma Alpha Epsilon Fraternity - Iowa Beta Riverside Corporation'
                    when 'Minnesota Alpha'
                      'Minnesota Alpha Association of Sigma Alpha Epsilon'
                    when 'Texas Tau'
                      'Texas Tau for Sigma Alpha Epsilon Housing Authority'
                    when 'Mississippi Theta'
                      'House Corporation of Mississippi Theta of Sigma Alpha Epsilon'
                    when 'Missouri Alpha'
                      'Sigma Alpha Epsilon Club of Columbia Missouri'
                    when 'Pennsylvania Alpha-Zeta'
                      'Pennsylvania Alpha Zeta Association of the Sigma Alpha Epsilon Fraternity'
                    when 'Wyoming Alpha'
                      'Wyoming Alpha SAE Fraternity House Corporation'
                    else
                      property.company.name.gsub(
                        'Chapter of Sigma Alpha Epsilon Fraternity',
                        'Housing LLC'
                      )
                    end

    Company.find_by!(name: landlord_name)
  end

  def master_lease_start_date(property)
    {
      'Idaho Alpha' => Date.new(2022, 1, 1),
      'Illinois Beta' => Date.new(2020, 8, 24),
      'Iowa Beta' => Date.new(2025, 7, 1),
      'Indiana Delta' => Date.new(2020, 8, 15),
      'Indiana Gamma' => Date.new(2019, 8, 22),
      'Michigan Delta' => Date.new(2025, 7, 1),
      'Michigan Gamma' => Date.new(2019, 8, 13),
      'Mississippi Theta' => Date.new(2022, 7, 1),
      'Missouri Alpha' => Date.new(2023, 8, 18),
      'New Mexico Tau' => Date.new(2022, 1, 1),
      'North Dakota Alpha' => Date.new(2019, 8, 20),
      'New Hampshire Beta' => Date.new(2018, 8, 24),
      'Ohio Gamma' => Date.new(2022, 3, 1),
      'Ohio Tau' => Date.new(2020, 8, 21),
      'Ohio Theta' => Date.new(2021, 8, 4),
      'Oregon Beta' => Date.new(2015, 9, 26),
      'Pennsylvania Alpha-Zeta' => Date.new(2023, 8, 15),
      'Texas Tau' => Date.new(2021, 8, 1),
      'Washington Beta' => Date.new(2018, 8, 10),
      'Wyoming Alpha' => Time.zone.today
    }.fetch(property.name).strftime('%m/%d/%Y')
  end

  def university_name(property)
    property.company.meta(:university).tap do |name|
      fail 'Unable to determine university name' if name.blank?
    end
  end

  def quarterly?
    return @quarterly if defined?(@quarterly)

    quarter_names = %w[Winter Summer]

    @quarterly = cohort.lease_installments.any? do |installment|
      quarter_names.include?(installment[:name])
    end
  end

  def installment_descriptions
    installments = cohort.lease_installments

    designator = if quarterly?
                   'Quarter'
                 else
                   'Semester'
                 end

    due_months = if quarterly?
                   [9, 1, 4]
                 else
                   [8, 1]
                 end

    installments.map do |installment|
      name = installment[:name]
      year = installment[:start_date].year
      due_date = Date.new(year, due_months.first, 1).to_fs(:human_date)
      due_months.rotate!
      amount = Monetize.parse(installment[:amount]).format
      "A #{name} #{year} #{designator} installment of #{amount} is due #{due_date}."
    end
  end
end
