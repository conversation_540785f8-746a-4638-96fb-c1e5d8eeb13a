class GreekHousing::Cohort
  attr_reader :property

  def initialize(property:)
    @property = property
  end

  def needs_guarantor?
    return false if Customer.current_subdomain.start_with?('wrp') && property&.id == 22

    true
  end

  def requires_countersigner?
    # This will default to the existing logic of using the default countersigner
    true
  end

  def membership_requires_guarantor_signature?
    # Legacy logic was to not have guarantor sign the document
    false
  end

  def lease_metadata_attributes
    return unless Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])

    GreekHousing::Cohorts::SaeMetadata.new(cohort: self).metadata_attributes
  end

  def receives_membership_agreement?
    return false if Customer.current_subdomain.start_with?('wrp')

    return false if Customer.current_subdomain.start_with?('chio-eta')

    return false if Customer.current_subdomain.start_with?('barrister')

    true
  end

  def generates_membership_agreement_document?
    return false unless receives_membership_agreement?

    return false if Customer.current_subdomain.start_with?('chio-sigma-beta')

    return false if Customer.current_subdomain.start_with?('chio-psi-delta')

    return false if Customer.current_subdomain.start_with?('trisigma')

    return false if Customer.current_subdomain.start_with?('dphie')

    true
  end

  def signs_membership_agreement?
    return false unless generates_membership_agreement_document?

    true
  end

  def receives_lease?
    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]) && property.name.in?(
      [
        'Alabama Alpha-Mu',
        'California Beta',
        'Colorado Chi',
        'Louisiana Epsilon',
        'Minnesota Alpha',
        'New Mexico Tau',
        'Texas Rho',
        'Washington Beta',
        'Ohio Lambda',
        'Colorado Chi',
        'Colorado Phi',
        'California Lambda',
        'Illinois Delta'
      ]
    )
      return false
    end

    return false if Customer.current_subdomain.start_with?('sigep-azbeta')

    return false if Customer.current_subdomain.start_with?('chio-sigma-beta')

    return false if Customer.current_subdomain.start_with?('chio-psi-delta')

    return false if Customer.current_subdomain.start_with?('chio-mu')

    true
  end

  def should_group_invoices?
    false
  end

  def generates_lease_document?
    return false unless receives_lease?

    return false if Customer.current_subdomain.start_with?('wrp') && property.id == 16

    true
  end

  def signs_lease?
    return false unless generates_lease_document?

    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox]) && property.name.in?(
      ['Washington Beta']
    )
      return false
    end

    true
  end

  def guarantor_signs_lease?
    true
  end

  def membership_document_template_id
    if Customer.current_subdomain.in?(%w[ghm sae])
      if property.id == 6
        return 55_308
      elsif property.id == 57
        return 91_238
      end
    end

    Agreements::AgreementType.first.document_template_id
  end

  def lease_document_template_id
    if Customer.current_subdomain == 'sigep-azbeta'
      688
    elsif Customer.current_subdomain.start_with?('wrp')
      if property.id == 7
        7719
      elsif property.id == 12
        8433
      elsif property.id == 14
        6070
      elsif property.id == 16
        1911
      elsif property.id == 19
        9312
      elsif property.id == 20
        8124
      elsif property.id == 8
        8430
      elsif property.id == 10
        5537
      elsif property.id == 21
        9089
      elsif property.id == 22
        11_272
      elsif property.id == 18
        9946
      elsif property.id == 56
        4928
      elsif property.id == 90
        8250
      elsif property.id == 91
        5511
      elsif property.id == 95
        8982
      elsif property.id == 96
        10_556
      else
        1167
      end
    elsif Customer.current_subdomain.start_with?('dphie')
      case property.id
      when 16 then 6378
      when 7 then 8702
      when 15 then 6377
      when 8 then 6380
      when 10 then 6985
      when 11 then 6379
      when 13 then 8650
      when 12 then 6381
      else
        1838
      end
    elsif Customer.current_subdomain.start_with?('chio-eta')
      2406
    elsif Customer.current_subdomain.in?(%w[ghm sae])
      if property.id == 6
        53_651
      elsif property.id == 12
        77_350
      else
        77_351
      end
    elsif Customer.current_subdomain.start_with?('kappasig-gammarho')
      1303
    elsif Customer.current_subdomain.start_with?('kappasig-gammaomicron')
      8
    elsif Customer.current_subdomain.start_with?('chio-beta-beta')
      681
    elsif Customer.current_subdomain.start_with?('trisigma')
      227
    elsif Customer.current_subdomain.start_with?('barrister')
      case property.id
      when 51 then 172
      when 53 then 408
      when 59 then 431
      else
        1
      end
    end
  end

  def membership_charge_memberships
    []
  end

  def membership_charge_presets
    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
      if property.name.in?(
        [
          'California Lambda',
          'Colorado Chi',
          'Colorado Phi',
          'Illinois Delta',
          'Minnesota Alpha',
          'Texas Rho'
        ]
      )
        []
      elsif property.id == 6 # Mississippi Theta
        [
          ChargePreset.find(248) # Initiation Fee
        ]
      else
        [
          ChargePreset.find(2) # Initiation Fee
        ]
      end
    elsif Customer.current_subdomain.start_with?('chio-sigma-beta')
      [ChargePreset.find(36)]
    elsif Customer.current_subdomain.start_with?('kappasig-gammarho')
      [ChargePreset.find(3)]
    else
      []
    end
  end

  def proration
    if Customer.current_subdomain == 'wrp'
      if property.id == 12
        :prorated_none
      elsif property.id.in?([14, 56, 90, 21, 22, 16, 8, 95])
        :prorated_whole
      else
        :prorated_nearest_month
      end
    else
      property.configuration.proration
    end
  end

  def start_date
    if Customer.current_subdomain == 'sigep-azbeta'
      return Date.new(2023, 8, 15)
    elsif Customer.current_subdomain.start_with?('wrp')
      return Date.new(2024, 8, 1) if property.id == 7

      return Date.new(2024, 6, 15) if property.id == 16

      return Date.new(2024, 8, 3) if property.id == 19

      return Date.new(2024, 8, 1) if property.id == 20

      return Date.new(2024, 8, 15) if property.id == 12

      return Date.new(2024, 8, 1) if property.id == 14

      return Date.new(2024, 9, 1) if property.id == 8

      return Date.new(2024, 8, 1) if property.id == 21

      return Date.new(2024, 8, 15) if property.id == 22

      return Date.new(2024, 7, 1) if property.id == 18

      return Date.new(2024, 8, 1) if property.id == 56

      return Date.new(2024, 8, 16) if property.id == 90

      return Date.new(2024, 8, 10) if property.id == 91

      return Date.new(2024, 8, 10) if property.id == 10

      return Date.new(2024, 8, 15) if property.id == 95

      return Date.new(2024, 8, 17) if property.id == 96

      return Date.new(2023, 8, 10)
    elsif Customer.current_subdomain.start_with?('dphie')
      return Date.new(2024, 8, 1)
    elsif Customer.current_subdomain.start_with?('chio-eta')
      return Date.new(2024, 9, 15)
    elsif Customer.current_subdomain.start_with?('kappasig-gammarho')
      return Date.new(2023, 9, 1)
    elsif Customer.current_subdomain.start_with?('chio-beta-beta')
      return Date.new(2023, 9, 8)
    elsif Customer.current_subdomain.start_with?('trisigma')
      return Date.new(2024, 8, 23)
    elsif Customer.current_subdomain.start_with?('barrister')
      return Date.new(2024, 8, 17) if property.id == 51

      return Date.new(2024, 9, 15) if property.id == 53

      return Date.new(2024, 8, 1) if property.id == 59

      return Date.new(2024, 8, 1)
    end

    lease_installments.first[:start_date]
  end

  def end_date
    if Customer.current_subdomain == 'sigep-azbeta'
      return Date.new(2024, 5, 15)
    elsif Customer.current_subdomain.start_with?('wrp')
      return Date.new(2025, 7, 31) if property.id == 7

      return Date.new(2025, 5, 31) if property.id == 16

      return Date.new(2025, 5, 5) if property.id == 19

      return Date.new(2025, 7, 15) if property.id == 20

      return Date.new(2025, 5, 15) if property.id == 12

      return Date.new(2025, 7, 15) if property.id == 14

      return Date.new(2025, 6, 30) if property.id == 8

      return Date.new(2025, 5, 31) if property.id == 21

      return Date.new(2025, 5, 15) if property.id == 22

      return Date.new(2024, 12, 31) if property.id == 18

      return Date.new(2025, 7, 15) if property.id == 56

      return Date.new(2025, 5, 18) if property.id == 90

      return Date.new(2025, 7, 31) if property.id == 91

      return Date.new(2025, 7, 20) if property.id == 10

      return Date.new(2025, 5, 15) if property.id == 95

      return Date.new(2025, 5, 6) if property.id == 96

      return Date.new(2024, 7, 20)
    elsif Customer.current_subdomain.start_with?('dphie')
      return Date.new(2025, 5, 16)
    elsif Customer.current_subdomain.start_with?('chio-eta')
      return Date.new(2025, 5, 5)
    elsif Customer.current_subdomain.start_with?('kappasig-gammarho')
      return Date.new(2024, 5, 15)
    elsif Customer.current_subdomain.start_with?('chio-beta-beta')
      return Date.new(2024, 5, 5)
    elsif Customer.current_subdomain.start_with?('trisigma')
      return Date.new(2025, 5, 11)
    elsif Customer.current_subdomain.start_with?('barrister')
      return Date.new(2025, 7, 31) if property.id == 51

      return Date.new(2025, 8, 31) if property.id == 53

      return Date.new(2025, 7, 31) if property.id == 59

      return Date.new(2026, 5, 31)
    end

    lease_installments.last[:end_date]
  end

  def lease_installments
    data = {
      'Alabama Alpha-Mu' => [],
      'Idaho Alpha' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 6),
          end_date: Date.new(2025, 12, 12),
          amount: '4,750.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 4),
          end_date: Date.new(2026, 5, 8),
          amount: '4,750.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 12),
          end_date: Date.new(2026, 12, 18),
          amount: '4,900.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 10),
          end_date: Date.new(2027, 5, 14),
          amount: '4,900.00'
        }
      ],
      'Illinois Beta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 19),
          amount: '7,030.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 16),
          end_date: Date.new(2026, 5, 15),
          amount: '7,030.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2026, 12, 18),
          amount: '7,250.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 15),
          end_date: Date.new(2027, 5, 14),
          amount: '7,250.00'
        }
      ],
      'Iowa Beta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 14),
          end_date: Date.new(2025, 12, 19),
          amount: '5,800.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 17),
          end_date: Date.new(2026, 5, 15),
          amount: '5,800.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 14),
          end_date: Date.new(2026, 12, 1),
          amount: '5,925.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 18),
          end_date: Date.new(2027, 5, 14),
          amount: '5,925.00'
        }
      ],
      'Indiana Delta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 15),
          end_date: Date.new(2025, 12, 12),
          amount: '7,575.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 23),
          end_date: Date.new(2026, 5, 15),
          amount: '7,575.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 14),
          end_date: Date.new(2026, 12, 11),
          amount: '7,825.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 22),
          end_date: Date.new(2027, 5, 14),
          amount: '7,825.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2027, 8, 13),
          end_date: Date.new(2027, 12, 10),
          amount: '8,060.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2028, 1, 21),
          end_date: Date.new(2028, 5, 12),
          amount: '8,060.00'
        }
      ],
      'Indiana Gamma' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 19),
          amount: '7,230.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 9),
          end_date: Date.new(2026, 5, 8),
          amount: '7,230.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2026, 12, 18),
          amount: '7,450.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 8),
          end_date: Date.new(2027, 5, 7),
          amount: '7,450.00'
        }
      ],
      'Louisiana Epsilon' => [],
      'Michigan Delta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 24),
          end_date: Date.new(2025, 12, 12),
          amount: '3,750.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 9),
          end_date: Date.new(2026, 5, 1),
          amount: '3,750.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 30),
          end_date: Date.new(2026, 12, 18),
          amount: '3,865.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 8),
          end_date: Date.new(2027, 4, 30),
          amount: '3,865.00'
        }
      ],
      'Michigan Gamma' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 12),
          amount: '6,275.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 9),
          end_date: Date.new(2026, 5, 1),
          amount: '6,275.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 28),
          end_date: Date.new(2026, 12, 18),
          amount: '6,470.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 15),
          end_date: Date.new(2027, 5, 7),
          amount: '6,470.00'
        }
      ],
      'Minnesota Alpha' => [],
      'Mississippi Theta' => [],
      'Missouri Alpha' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 15),
          end_date: Date.new(2025, 12, 12),
          amount: '6,500.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 16),
          end_date: Date.new(2026, 5, 15),
          amount: '6,500.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 14),
          end_date: Date.new(2026, 12, 11),
          amount: '6,700.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 15),
          end_date: Date.new(2027, 5, 14),
          amount: '6,700.00'
        }
      ],
      'New Hampshire Beta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 16),
          amount: '6,815.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 16),
          end_date: Date.new(2026, 5, 13),
          amount: '6,815.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2026, 12, 18),
          amount: '7,020.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 15),
          end_date: Date.new(2027, 5, 14),
          amount: '7,020.00'
        }
      ],
      'New Mexico Tau' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 15),
          end_date: Date.new(2025, 12, 13),
          amount: '5,150.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 16),
          end_date: Date.new(2026, 5, 16),
          amount: '5,150.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 14),
          end_date: Date.new(2026, 12, 12),
          amount: '5,325.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 15),
          end_date: Date.new(2027, 5, 15),
          amount: '5,325.00'
        }
      ],
      'North Dakota Alpha' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 19),
          amount: '4,870.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 9),
          end_date: Date.new(2026, 5, 15),
          amount: '4,870.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2025, 12, 18),
          amount: '5,020.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 8),
          end_date: Date.new(2027, 5, 14),
          amount: '5,020.00'
        }
      ],
      'Ohio Gamma' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 13),
          amount: '6,500.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 9),
          end_date: Date.new(2026, 5, 1),
          amount: '6,500.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2026, 12, 12),
          amount: '6,700.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 8),
          end_date: Date.new(2027, 4, 30),
          amount: '6,700.00'
        }
      ],
      'Ohio Tau' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 12),
          amount: '8,200.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 23),
          end_date: Date.new(2026, 5, 15),
          amount: '8,200.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2026, 12, 11),
          amount: '8,450.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 22),
          end_date: Date.new(2027, 5, 14),
          amount: '8,450.00'
        }
      ],
      'Ohio Theta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 19),
          amount: '7,465.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 9),
          end_date: Date.new(2026, 5, 6),
          amount: '7,465.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2026, 12, 18),
          amount: '7,690.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 8),
          end_date: Date.new(2027, 5, 5),
          amount: '7,690.00'
        }
      ],
      'Oregon Beta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 9, 26),
          end_date: Date.new(2025, 12, 12),
          amount: '5,110.00'
        },
        {
          name: 'Winter',
          start_date: Date.new(2026, 1, 2),
          end_date: Date.new(2026, 3, 20),
          amount: '5,110.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 3, 27),
          end_date: Date.new(2026, 6, 12),
          amount: '5,110.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 9, 25),
          end_date: Date.new(2026, 12, 11),
          amount: '5,270.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 1),
          end_date: Date.new(2027, 3, 19),
          amount: '5,270.00'
        },
        {
          name: 'Winter',
          start_date: Date.new(2027, 3, 26),
          end_date: Date.new(2027, 6, 11),
          amount: '5,270.00'
        }
      ],
      'Pennsylvania Alpha-Zeta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 23),
          end_date: Date.new(2025, 12, 19),
          amount: '6,365.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 11),
          end_date: Date.new(2026, 5, 8),
          amount: '6,365.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 21),
          end_date: Date.new(2026, 12, 18),
          amount: '6,550.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 8),
          end_date: Date.new(2027, 5, 7),
          amount: '6,550.00'
        }
      ],
      'Texas Rho' => [],
      'Texas Tau' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 15),
          end_date: Date.new(2025, 12, 12),
          amount: '4,450.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 16),
          end_date: Date.new(2026, 5, 15),
          amount: '4,450.00'
        },
        {
          name: 'Summer',
          start_date: Date.new(2026, 5, 16),
          end_date: Date.new(2026, 8, 13),
          amount: '3,300.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 14),
          end_date: Date.new(2026, 12, 11),
          amount: '4,585.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 15),
          end_date: Date.new(2027, 5, 14),
          amount: '4,585.00'
        },
        {
          name: 'Summer',
          start_date: Date.new(2027, 5, 15),
          end_date: Date.new(2027, 8, 12),
          amount: '3,400.00'
        }
      ],
      'Washington Beta' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 8),
          end_date: Date.new(2025, 12, 12),
          amount: '6,175.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 9),
          end_date: Date.new(2026, 5, 1),
          amount: '6,100.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 14),
          end_date: Date.new(2026, 12, 11),
          amount: '6,375.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 8),
          end_date: Date.new(2027, 5, 7),
          amount: '6,300.00'
        }
      ],
      'Wyoming Alpha' => [
        {
          name: 'Fall',
          start_date: Date.new(2025, 8, 22),
          end_date: Date.new(2025, 12, 12),
          amount: '4,450.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2026, 1, 17),
          end_date: Date.new(2026, 5, 15),
          amount: '4,450.00'
        },
        {
          name: 'Summer',
          start_date: Date.new(2026, 6, 1),
          end_date: Date.new(2026, 7, 31),
          amount: '650.00'
        },
        {
          name: 'Fall',
          start_date: Date.new(2026, 8, 28),
          end_date: Date.new(2026, 12, 18),
          amount: '4,600.00'
        },
        {
          name: 'Spring',
          start_date: Date.new(2027, 1, 16),
          end_date: Date.new(2027, 5, 14),
          amount: '4,600.00'
        },
        {
          name: 'Summer',
          start_date: Date.new(2027, 6, 1),
          end_date: Date.new(2027, 7, 31),
          amount: '675.00'
        }
      ]
    }

    data.fetch(property.name)
  end

  def lease_schedule_entries_attrs
    lease_charge_presets.map do |lease_preset|
      schedule_entry_attrs lease_preset
    end
  end

  def bill_one_time_charges_immediately?
    Customer.current_subdomain.start_with?('dphie', 'wrp', 'barrister')
  end

  def schedule_entry_attrs(preset)
    if rental_charge_preset?(preset)
      attrs = {
        amount: lease_entry_amount, # || preset.amount (weird: not used as a fallback)
        name: 'Room and Board',
        recurring: false,
        account: preset.account,
        charge_preset: preset.persisted? ? preset : nil,
        start_date: nil,
        end_date: nil
      }

      attrs[:recurring] = true if Customer.current_subdomain.start_with?('wrp')
      attrs
    else
      attrs = {
        amount: preset.amount,
        name: preset.name,
        recurring: false,
        account: preset.account,
        charge_preset: preset.persisted? ? preset : nil,
        start_date: nil,
        end_date: nil
      }
      if Customer.current_subdomain.start_with?('wrp')
        attrs[:recurring] = preset.account.type == 'Plutus::Revenue'

        if property.id == 19
          attrs[:start_date] = Date.new(2024, 9, 1)
          attrs[:end_date] = Date.new(2025, 4, 30)
        elsif property.id == 22
          attrs[:start_date] = Date.new(2024, 9, 1)
          attrs[:end_date] = Date.new(2025, 5, 30)
        end
      end
      attrs
    end
  end

  def rental_charge_preset?(charge_preset)
    charge_preset.new_record? || charge_preset == property.configuration.rent_preset
  end

  # This still needs immediate charge presets
  def lease_charge_presets
    if Customer.current_subdomain.start_with?('wrp')
      return ChargePreset.find([23, 27]) if property.id == 19
      return ChargePreset.find([92, 95]) if property.id == 20
      return ChargePreset.find([33, 29]) if property.id == 16
      return ChargePreset.find([9]) if property.id == 12
      return ChargePreset.find([32, 30]) if property.id == 7
      return ChargePreset.find([6, 31]) if property.id == 10
      return ChargePreset.find([35, 38]) if property.id == 14
      return ChargePreset.find([41, 39]) if property.id == 8
      return ChargePreset.find([42, 43]) if property.id == 21
      return ChargePreset.find([79, 81]) if property.id == 22
      return ChargePreset.find([82, 83]) if property.id == 18
      return ChargePreset.find([84, 85]) if property.id == 56
      return ChargePreset.find([90, 91]) if property.id == 90
      return ChargePreset.find([98, 100]) if property.id == 95
      return ChargePreset.find([101]) if property.id == 96
      return ChargePreset.find([88]) if property.id == 91
    elsif Customer.current_subdomain.start_with?('dphie')
      return ChargePreset.find([2]) if property.id == 12

      return ChargePreset.find([2, 5])
    elsif Customer.current_subdomain.start_with?('trisigma')
      return []
    elsif Customer.current_subdomain.start_with?('barrister')
      return ChargePreset.find([2]) if property.id == 58
      return ChargePreset.find([38]) if property.id == 53
      return ChargePreset.find([48]) if property.id == 59
    end

    if property.configuration.rent_preset.present?
      [property.configuration.rent_preset]
    else
      [
        ChargePreset.new(
          name: 'Room and Board',
          account: property.configuration.chart_of_accounts.rent_income_account
        )
      ]
    end
  end

  def lease_entry_year_amount(installments)
    Money.sum(installments.map { |i| Monetize.parse(i[:amount]) })
  end

  def lease_entry_amount
    if Customer.current_subdomain == 'sigep-azbeta'
      return Monetize.parse('$8,100.00')
    elsif Customer.current_subdomain.start_with?('wrp')
      return Monetize.parse('$560.00') if property.id == 7

      return Monetize.parse('$745.00') if property.id == 10

      return Monetize.parse('$1,505.00') if property.id == 16

      return Monetize.parse('$665.00') if property.id == 20

      return Monetize.parse('$575.00')
    elsif Customer.current_subdomain.start_with?('dphie')
      return Money.zero
    elsif Customer.current_subdomain.start_with?('chio-eta')
      return Money.zero
    elsif Customer.current_subdomain.start_with?('kappasig-gammarho')
      return Money.zero
    elsif Customer.current_subdomain.start_with?('chio-beta-beta')
      return Money.zero
    elsif Customer.current_subdomain.start_with?('trisigma')
      return Money.zero
    elsif Customer.current_subdomain.start_with?('barrister')
      return Money.zero
    end

    lease_entry_year_amount(lease_installments)
  end
end
