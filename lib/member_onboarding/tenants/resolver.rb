class MemberOnboarding::Tenants::Resolver
  extend Service
  attr_reader :tenant, :onboarding, :property

  def initialize(tenant)
    @tenant = tenant
  end

  def found?
    @property.present? && @onboarding.present?
  end

  def onboardable_property
    tenant.lead_property ||
      Tenant::LeaseMembershipsQuery.current_lease_membership(tenant: tenant)&.property ||
      Tenant::SimpleAgreementsQuery.current_simple_agreement(tenant: tenant)&.property
  end

  def tenant_assignment
    tenant.active_onboarding_assignment
  end

  # This method can be removed once we migrate to auto assignments
  def latest_property_assignment
    return nil if Feature.enabled?(:onboarding_enhancements, Customer.current)

    MemberOnboarding::PropertyMembership
      .where(
        property: onboardable_property,
        enhanced: false
      ).reorder('created_at DESC')
      .first
  end

  def call
    @property = onboardable_property
    @onboarding = tenant_assignment&.configuration || latest_property_assignment&.configuration
  end

  def cohort
    @cohort ||= MemberOnboarding::Tenants::ConfiguredCohort.new(onboarding, property)
  end
end
