class MemberOnboarding::Tenants::Wizard
  ALL_STEP_KLASSES = [
    MemberOnboarding::Tenants::Wizard::Steps::Welcome,
    MemberOnboarding::Tenants::Wizard::Steps::MemberProfile,
    MemberOnboarding::Tenants::Wizard::Steps::Guarantor<PERSON><PERSON>file,
    MemberOnboarding::Tenants::Wizard::Steps::RiskManagementProgram,
    MemberOnboarding::Tenants::Wizard::Steps::MembershipAgreement,
    MemberOnboarding::Tenants::Wizard::Steps::LeaseAgreement,
    MemberOnboarding::Tenants::Wizard::Steps::CompletionSummary
  ].freeze

  attr_accessor :cohort, :current_step_id, :steps, :member, :submitted

  delegate :onboarding, :property, to: :cohort

  def initialize(cohort, member, submitted: false)
    @cohort = cohort
    @member = member
    @submitted = submitted
  end

  def build_steps!
    @steps = ALL_STEP_KLASSES
             .map { |klass| klass.new(self) }
             .select(&:applicable?)
             .index_by(&:id)
             .with_indifferent_access
  end

  def self.find_or_create(tenant)
    resolver = MemberOnboarding::Tenants::Resolver.new(tenant).tap(&:call)
    return nil unless resolver.found?

    if (found_wizard = MemberOnboarding::Tenants::Wizard.hydrate(resolver.cohort, tenant)).present?
      found_wizard
    else
      MemberOnboarding::Tenants::Wizard.create(resolver.cohort, tenant)
    end
  end

  def submitted?(check_redis: false)
    if check_redis
      @submitted || self.class.hydrate(cohort, member).submitted?
    else
      @submitted
    end
  end

  def perform_submit(params, request)
    MemberOnboarding::Tenants::Submit.call(
      params: params,
      request: request,
      wizard: self
    ) do |result|
      if result.successful?
        @submitted = true
        steps['membership_agreement']&.membership_agreement_id = result.try(:membership)&.id
        steps['lease_agreement']&.lease_id = result.try(:lease)&.id
        persist!
      end
    end
  end

  def existing_membership
    @existing_membership ||=
      if cohort.receives_membership_agreement?
        member.simple_agreements.current.find_by(property: cohort.property)
      end
  end

  module Persisting
    extend ActiveSupport::Concern

    # rubocop:disable Metrics/ BlockLength
    class_methods do
      def create(cohort, member)
        new(cohort, member).tap do |instance|
          instance.build_steps!
          instance.current_step_id = instance.steps.keys.first
          instance.persist!
        end
      end

      def from_attributes(hash)
        cohort = MemberOnboarding::Tenants::ConfiguredCohort.hydrate(hash[:cohort])
        member = Tenant.find(hash['member_id'])
        new(cohort, member, submitted: hash['submitted']).tap do |instance|
          instance.current_step_id = hash['current_step_id']
          instance.steps = hash['steps'].map do |(k, step_hash)| # rubocop:disable Style/MapToHash
            step_klass = ALL_STEP_KLASSES.find { |klass| klass.id.to_sym == k.to_sym }
            step = step_klass.hydrate(step_hash, wizard: instance)
            [k, step]
          end.to_h.with_indifferent_access
        end
      end

      def hydrate(cohort, tenant)
        Redis.instance do |redis|
          value = redis.get(redis_key(cohort, tenant)).presence ||
                  redis.get(legacy_redis_key(cohort, tenant))
          from_attributes(JSON.parse(value).with_indifferent_access) if value.present?
        end
      end

      def clear!(cohort, tenant)
        Redis.instance do |redis|
          redis.del(redis_key(cohort, tenant))
          redis.del(legacy_redis_key(cohort, tenant))
        end
      end

      def redis_key(cohort, tenant)
        Customer.current_subdomain +
          ':' +
          ([tenant, cohort.property, cohort.onboarding].map(&:to_gid) +
          ['member_onboarding_wizard']).join(':')
      end

      def legacy_redis_key(cohort, tenant)
        ([tenant, cohort.property, cohort.onboarding].map(&:to_gid) +
          ['member_onboarding_wizard']).join(':')
      end
    end
    # rubocop:enable Metrics/ BlockLength

    def attributes
      {
        'cohort' => cohort.attributes,
        'member_id' => member.id,
        'current_step_id' => current_step_id,
        'steps' => steps.transform_values(&:attributes),
        'submitted' => @submitted
      }
    end

    def persist!(expires: 90.days.to_i)
      Redis.instance do |redis|
        redis.set(redis_key, attributes.to_json, ex: expires)
        redis.del(legacy_redis_key)
      end
    end

    def redis_key
      self.class.redis_key(cohort, member)
    end

    def legacy_redis_key
      self.class.legacy_redis_key(cohort, member)
    end
  end
  include Persisting

  module StepNavigation
    def current_step
      steps[current_step_id]
    end

    def advance!
      @current_step_id = next_step.visitable? ? next_step.id : :welcome
      persist!
    end

    def next_step
      nil if current_step_index == (steps.count - 1)
      steps[steps.keys[current_step_index + 1].to_s]
    end

    def step_index(id)
      steps.keys.index(id.to_s)
    end

    def step_by_index(index)
      steps[steps.keys[index]]
    end

    def current_step_index
      step_index(current_step_id.to_s)
    end

    def steps_before(index)
      [] if index.zero?
      steps.to_a[..(index - 1)].to_h.values
    end

    def previous_step
      steps_before(current_step_index).last
    end
  end
  include StepNavigation
end
