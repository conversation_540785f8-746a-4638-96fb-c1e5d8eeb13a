class MemberOnboarding::Tenants::Wizard::Steps::MembershipAgreement <
  MemberOnboarding::Tenants::Wizard::Steps::Step
  attr_accessor :membership_agreement_id

  def self.hydrate(hsh, wizard:)
    super.tap do |instance|
      instance.membership_agreement_id = hsh['membership_agreement_id']
    end
  end

  # rubocop:disable Style/EndlessMethod

  def attributes = super.merge('membership_agreement_id' => @membership_agreement_id)

  def name = 'Membership Agreement'

  def welcome_summary = 'Sign a Membership Agreement'

  def completed? = super # rubocop:disable Lint/UselessMethodDefinition

  def applicable? = cohort.signs_membership_agreement?

  def completed? = membership_agreement_id.present?

  def visitable? = super && preceding_steps.all?(&:completed?) && !wizard.submitted?

  def requires_submission_before_visit? = false

  def show_path = membership_agreement_tenants_member_onboarding_path(onboarding)

  def form_id = 'electronic_signature_form'
  # rubocop:enable Style/EndlessMethod
end
