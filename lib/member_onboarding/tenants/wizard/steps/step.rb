class MemberOnboarding::Tenants::Wizard::Steps::Step
  include Rails.application.routes.url_helpers

  attr_accessor :wizard
  attr_writer :completed

  delegate :cohort, :onboarding, to: :wizard

  # rubocop:disable Style/EndlessMethod

  def self.hydrate(hsh, wizard:) = new(wizard, completed: hsh['completed'])

  def self.id = name.demodulize.underscore.to_sym

  def initialize(wizard, completed: false)
    @wizard = wizard
    @completed = completed
  end

  def id = self.class.id

  def attributes = { 'completed' => @completed }

  def completed? = @completed

  def visitable? = !requires_submission_before_visit? || wizard.submitted?

  def submit_after_completed? = proceeding_step&.requires_submission_before_visit? &&
    !wizard.submitted?

  def active? = wizard.current_step_id == id

  def proceeding_step = wizard.step_by_index(wizard.step_index(id) + 1)

  def preceding_steps = wizard.steps_before(wizard.step_index(id))
  # rubocop:enable Style/EndlessMethod
end
