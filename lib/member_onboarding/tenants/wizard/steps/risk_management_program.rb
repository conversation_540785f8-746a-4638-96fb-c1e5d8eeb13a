class MemberOnboarding::Tenants::Wizard::Steps::RiskManagementProgram <
  MemberOnboarding::Tenants::Wizard::Steps::Step
  attr_accessor :accepted

  def self.hydrate(hsh, wizard:)
    super.tap do |instance|
      instance.accepted = hsh['accepted'].nil? ? true : hsh['accepted']
    end
  end

  # rubocop:disable Style/EndlessMethod

  def attributes = super.merge('accepted' => @accepted)

  def name = 'Risk Release'

  def welcome_summary = 'Choose to Enroll in the Risk Release program'

  def applicable? = cohort.offer_damage_waiver?

  def completed? = super # rubocop:disable Lint/UselessMethodDefinition

  def visitable? = !wizard.submitted?

  def requires_submission_before_visit? = false

  def show_path = risk_management_program_tenants_member_onboarding_path(onboarding)

  def form_id = 'risk_management_program_form'
  # rubocop:enable Style/EndlessMethod

  def handle(params:, request:) # rubocop:disable Metrics/AbcSize
    accepting = ActiveModel::Type::Boolean.new.cast params.dig :risk_management_program, :accepting
    selected = [true, false].include?(accepting)
    result =
      if cohort.require_damage_waiver? && accepting != true
        self.completed = false
        self.accepted = nil
        OpenStruct.new(successful?: false,
                       errors: ['Must accept Risk Release when onboarding'])

      elsif cohort.offer_damage_waiver? && !selected
        self.completed = false
        self.accepted = nil
        OpenStruct.new(successful?: false,
                       errors: ['Must choose whether or not to enroll in Risk Release'])
      else
        self.completed = true
        self.accepted = accepting
        if wizard.current_step.submit_after_completed?
          wizard.perform_submit(params, request)
        else
          OpenStruct.new(successful?: true)
        end
      end
    wizard.persist!
    result
  end
end
