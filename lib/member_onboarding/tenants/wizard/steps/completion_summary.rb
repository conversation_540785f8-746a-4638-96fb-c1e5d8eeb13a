class MemberOnboarding::Tenants::Wizard::Steps::CompletionSummary <
  MemberOnboarding::Tenants::Wizard::Steps::Step
  # rubocop:disable Style/EndlessMethod
  attr_accessor :completion_id

  def self.hydrate(hsh, wizard:)
    super.tap do |instance|
      instance.completion_id = hsh['completion_id']
    end
  end

  def attributes = super.merge('completion_id' => completion_id)

  def name = 'Completion Summary'

  def welcome_summary = 'Review any steps required after this onboarding is complete'

  def applicable? = true

  def completed? = completion_id.present?

  def visitable? = super && preceding_steps.all?(&:completed?)

  def submit_after_completed? = false

  def requires_submission_before_visit? = true

  def show_path = completion_summary_tenants_member_onboarding_path(onboarding)

  def form_id = nil
  # rubocop:enable Style/EndlessMethod

  def after_completion_path
    if onboarding.charge.present?
      tenants_rent_invoices_path
    else
      tenants_dashboard_path
    end
  end

  def follow_up_items
    items = []
    if cohort.accepts_guarantor? &&
       !wizard.steps['guarantor_profile']&.skip_guarantor
      items << :ensure_guarantor_account
    end

    if cohort.guarantor_signs_lease? || cohort.guarantor_membership_signs_agreement?
      items << :ensure_guarantor_signatures
    end

    items << :check_charges if onboarding.charge

    items
  end
end
