class MemberOnboarding::Tenants::Wizard::Steps::Welcome <
  MemberOnboarding::Tenants::Wizard::Steps::Step
  # rubocop:disable Style/EndlessMethod

  def name = 'Welcome'

  def welcome_summary = nil

  def applicable? = true

  def completed? = true

  def visitable? = true

  def submit_after_completed? = super # rubocop:disable Lint/UselessMethodDefinition

  def requires_submission_before_visit? = false

  def show_path = welcome_tenants_member_onboarding_path(onboarding)

  def form_id = 'pass_welcome_form'

  # rubocop:enable Style/EndlessMethod
  def handle(params:, request:)
    if submit_after_completed?
      wizard.perform_submit(params, request)
    elsif completed?
      OpenStruct.new(successful?: true)
    end
  end
end
