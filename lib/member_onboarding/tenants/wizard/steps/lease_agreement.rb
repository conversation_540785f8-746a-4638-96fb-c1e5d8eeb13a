class MemberOnboarding::Tenants::Wizard::Steps::LeaseAgreement < MemberOnboarding::Tenants::Wizard::Steps::Step # rubocop:disable Layout/LineLength
  attr_accessor :lease_id

  # rubocop:disable Style/EndlessMethod
  def self.hydrate(hsh, wizard:)
    super.tap do |instance|
      instance.lease_id = hsh['lease_id']
    end
  end

  def name = I18n.t('lease_agreement_term')

  def welcome_summary = 'Sign a Sublease Agreement'

  def applicable? = cohort.signs_lease?

  def attributes = super.merge('lease_id' => @lease_id)

  def completed?
    return lease_id.present? unless cohort.signs_lease?

    lease_id && Lease.find(lease_id).electronic_signatures.signed.primary_signer.any?
  end

  def visitable? = super && preceding_steps.all?(&:completed?) && !completed?

  def submit_after_completed? = false

  def requires_submission_before_visit? = true

  def show_path = lease_agreement_tenants_member_onboarding_path(onboarding)

  def form_id = 'electronic_signature_form'
  # rubocop:enable Style/EndlessMethod
end
