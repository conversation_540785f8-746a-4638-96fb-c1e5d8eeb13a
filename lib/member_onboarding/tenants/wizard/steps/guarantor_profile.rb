class MemberOnboarding::Tenants::Wizard::Steps::GuarantorProfile <
  MemberOnboarding::Tenants::Wizard::Steps::Step
  attr_accessor :profile_attributes, :skip_guarantor

  def self.hydrate(hsh, wizard:)
    super.tap do |instance|
      instance.skip_guarantor = hsh['skip_guarantor']
      instance.profile_attributes =
        hsh['profile_attributes'].presence ||
        (if instance.skip_guarantor
           nil
         elsif (previous_guarantor = instance.previous_guarantor)
           MemberOnboarding::Tenants::RequiredGuarantorInformation
                     .attributes_from_guarantor(previous_guarantor)
         end)
    end
  end

  def attributes
    super.merge(
      'profile_attributes' => @profile_attributes,
      'skip_guarantor' => @skip_guarantor
    )
  end

  def name = 'Guarantor Profile'

  def welcome_summary = 'Create a Guarantor Profile'

  def completed? = super # rubocop:disable Lint/UselessMethodDefinition

  def applicable? = cohort.onboarding.guarantor.present?

  def visitable? = super && !wizard.submitted?

  def requires_submission_before_visit? = false

  def show_path = guarantor_profile_tenants_member_onboarding_path(onboarding)

  def form_id = 'member_onboarding_guarantor_profile'

  def find_or_build_guarantor(params: nil)
    guarantor_params = required_information.profile_params(params)
    email = guarantor_params[:email] || profile_attributes.presence&.dig(:email)
    if email.blank?
      return Tenant.resident.new((profile_attributes || {}).deep_merge(guarantor_params || {}))
    end

    Tenant.find_or_initialize_by(email:).tap do |t|
      t.assign_attributes((profile_attributes || {}).deep_merge(guarantor_params || {}))
    end
  end

  def previous_guarantor
    Agreements::SimpleAgreement::Membership.where(
      simple_agreement_id: Agreements::SimpleAgreement::Membership
        .where(tenant: wizard.member, role: :primary)
        .select('simple_agreement_id'),
      role: :guardian
    ).order(created_at: :desc).select('tenant_id, created_at').union_all(
      LeaseMembership.where(
        lease_id: LeaseMembership.where(tenant: wizard.member, role: :primary_tenant)
          .select('lease_id'),
        role: :guarantor
      ).order(created_at: :desc).select('tenant_id, created_at')
    ).order(created_at: :desc).first&.tenant
  end

  def handle(params:, request:)
    skipping = params.dig(:guarantor, :skip_guarantor) == '1'

    result = skipping ? handle_skip!(params:, request:) : handle_save!(params:, request:)

    wizard.persist!
    result
  end

  def handle_skip!(params:, request:)
    self.skip_guarantor = true
    self.profile_attributes = nil
    handle_submit!(params:, request:)
  end

  def handle_save!(params:, request:)
    guarantor = find_or_build_guarantor(params: params)
    self.profile_attributes = MemberOnboarding::Tenants::RequiredGuarantorInformation
                              .attributes_from_guarantor(guarantor)
    self.skip_guarantor = false
    required_information.validate(guarantor, wizard.member)
    if guarantor.errors.none?
      handle_submit!(params:, request:)
    else
      OpenStruct.new(successful?: false, errors: guarantor.errors.full_messages)
    end
  end

  def handle_submit!(params:, request:)
    self.completed = true
    if wizard.current_step.submit_after_completed?
      wizard.perform_submit(params, request)
    else
      OpenStruct.new(successful?: true)
    end
  end

  def required_information
    @required_information ||=
      MemberOnboarding::Tenants::RequiredGuarantorInformation.new(onboarding)
  end
end
