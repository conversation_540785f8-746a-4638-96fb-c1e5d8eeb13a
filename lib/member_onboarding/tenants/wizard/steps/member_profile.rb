class MemberOnboarding::Tenants::Wizard::Steps::Member<PERSON>rofile <
  MemberOnboarding::Tenants::Wizard::Steps::Step
  attr_accessor :profile_attributes

  def self.hydrate(hsh, wizard:)
    super.tap { |instance| instance.profile_attributes = hsh['profile_attributes'] }
  end

  # rubocop:disable Style/EndlessMethod

  def attributes = super.merge('profile_attributes' => @profile_attributes)

  def name = 'Member Profile'

  def welcome_summary = 'Fill out your membership profile'

  def completed? = super # rubocop:disable Lint/UselessMethodDefinition

  def applicable? = cohort.onboarding.information_collection.present?

  def visitable? = super && !wizard.submitted?

  def requires_submission_before_visit? = false

  def show_path = member_profile_tenants_member_onboarding_path(onboarding)

  def form_id = 'member_onboarding_member_profile'

  delegate :validate, to: :required_information

  def required_information
    @required_information ||= MemberOnboarding::Tenants::RequiredInformation.new(onboarding)
  end

  def handle(params:, request:)
    tenant = wizard.member
    permitted_tenant_attributes = required_information.profile_params(params.require(:member))
    tenant.assign_attributes(permitted_tenant_attributes)
    validate(tenant)
    result =
      if tenant.errors.none?
        self.completed = true
        self.profile_attributes = permitted_tenant_attributes
        if wizard.current_step.submit_after_completed?
          wizard.perform_submit(params, request)
        else
          OpenStruct.new(successful?: true)
        end

      else
        OpenStruct.new(successful?: false, errors: tenant.errors.full_messages)
      end
    wizard.persist!
    result
  end
end
