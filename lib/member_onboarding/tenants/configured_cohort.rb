class MemberOnboarding::Tenants::ConfiguredCohort
  attr_reader :onboarding, :property

  def initialize(onboarding, property)
    @onboarding = onboarding
    @property = property
  end

  def self.hydrate(hsh)
    onboarding = MemberOnboarding::Configuration.find(hsh[:onboarding_id])
    property = Property.find(hsh[:property_id])
    new(onboarding, property)
  end

  def attributes
    { onboarding_id: @onboarding.id, property_id: @property.id }
  end

  def accepts_guarantor?
    onboarding.guarantor.present?
  end

  def requires_guarantor?
    accepts_guarantor? && !onboarding.guarantor.optional?
  end

  def lease_metadata_attributes
    return nil unless Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])

    GreekHousing::Cohorts::SaeMetadata.new(cohort: self).metadata_attributes
  end

  def receives_membership_agreement?
    true
  end

  def should_group_invoices?
    return false unless onboarding.charge.present?

    onboarding.charge.group_invoices?
  end

  def generates_membership_agreement_document?
    return false unless onboarding.membership_agreement.present?

    onboarding.membership_agreement.membership_template.present?
  end

  def signs_membership_agreement?
    return false unless generates_membership_agreement_document?

    onboarding.membership_agreement.require_member_signature?
  end

  def requires_countersigner?
    onboarding.membership_agreement&.require_countersigner?
  end

  def membership_requires_guarantor_signature?
    onboarding.membership_agreement&.require_guarantor_signature?
  end

  def guarantor_membership_signs_agreement?
    false
  end

  def receives_lease?
    onboarding.lease_agreement.present?
  end

  def generates_lease_document?
    return false unless receives_lease?

    onboarding.lease_agreement.lease_template.present?
  end

  def signs_lease?
    return false unless generates_lease_document?

    onboarding.lease_agreement.require_member_signature?
  end

  def guarantor_signs_lease?
    onboarding.lease_agreement&.require_guarantor_signature
  end

  def membership_document_template_id
    onboarding.membership_agreement&.membership_template&.id
  end

  def lease_document_template_id
    onboarding.lease_agreement&.lease_template&.id
  end

  def bill_one_time_charges_immediately?
    true
  end

  def membership_charge_presets
    return [] unless onboarding.charge

    ChargePreset.where(
      id: onboarding.charge.charge_memberships.membership.select(:charge_preset_id)
    )
  end

  def membership_charge_memberships
    onboarding.charge&.charge_memberships&.includes(:charge_preset)&.membership || []
  end

  def proration
    onboarding.lease_agreement&.proration
  end

  def start_date
    onboarding.lease_agreement&.lease_start_date
  end

  def end_date
    onboarding.lease_agreement&.lease_end_date
  end

  def lease_entry_year_amount(installments)
    Money.sum(installments.map { |i| Monetize.parse(i[:amount]) })
  end

  def lease_installments
    lease_schedule_entries_attrs.sort_by { |installment| installment[:start_date] }
  end

  def lease_schedule_entries_attrs
    lease_charge_memberships.map do |charge_membership|
      {
        recurring: charge_membership.recurring,
        start_date: charge_membership.start_date,
        end_date: charge_membership.end_date,
        charge_preset: charge_membership.charge_preset,
        amount: charge_membership.charge_preset.amount,
        name: charge_membership.charge_preset.name,
        account: charge_membership.charge_preset.account
      }
    end
  end

  def offer_damage_waiver?
    onboarding.risk_release
  end

  def require_damage_waiver?
    offer_damage_waiver? && onboarding.risk_release&.enrollment_required
  end

  def lease_charge_memberships
    onboarding.charge&.charge_memberships&.includes(:charge_preset)&.lease || []
  end
end
