class MemberOnboarding::Tenants::RequiredGuarantorInformation
  GUARANTOR_FIELDS_ARGS = %i[first_name last_name email phone date_of_birth].freeze

  GUARANTOR_FIELDS_KWARGS = {
    taxpayer_identification_attributes: %i[tin_type tin],
    forwarding_address_attributes: %i[line_one line_two city region postal_code country],
    metadata_attributes: [{ data: [:drivers_license_number] }]
  }.freeze

  GUARANTOR_ATTRIBUTES_AS_JSON = {
    only: GUARANTOR_FIELDS_ARGS,
    include: {
      taxpayer_identification: { only: %i[tin_type], methods: [:tin] },
      forwarding_address: { only: %i[line_one line_two city region postal_code country] },
      metadata: { only: [:data] }
    }
  }.freeze

  CONVERT_GUARANTOR_ATTRIBUTE_KEYS = {
    taxpayer_identification: :taxpayer_identification_attributes,
    forwarding_address: :forwarding_address_attributes,
    metadata: :metadata_attributes
  }.freeze

  def self.attributes_from_guarantor(guarantor)
    deep_compact_blank(
      guarantor.as_json(GUARANTOR_ATTRIBUTES_AS_JSON).transform_keys do |k|
        CONVERT_GUARANTOR_ATTRIBUTE_KEYS[k.to_sym] || k
      end
    )
  end

  attr_reader :onboarding

  def initialize(onboarding)
    @onboarding = onboarding
  end

  def gathering_address?
    onboarding.guarantor.address
  end

  def gathering_collections_information?
    onboarding.guarantor.collections_information
  end

  def gathering_drivers_license_number?
    onboarding.guarantor.drivers_license_number
  end

  def optional?
    onboarding.guarantor.optional
  end

  def validate(guarantor, member)
    MemberOnboarding::Tenants::Validator::RequiredGuarantorInformation.new(
      guarantor: guarantor, requirements: self, member:
    ).call
  end

  def profile_params(params)
    return {} if params.presence&.to_unsafe_h&.dig(:guarantor).blank?

    guarantor_params = params.require(:guarantor).permit(*GUARANTOR_FIELDS_ARGS,
                                                         **GUARANTOR_FIELDS_KWARGS).to_h

    guarantor_params = process_taxpayer_identification(guarantor_params)
    deep_compact_blank(guarantor_params)
  end

  delegate :deep_compact_blank, to: :class


  def self.deep_compact_blank(guarantor_params)
    guarantor_params.each.with_object({}) do |(k, v), acc|
      compacted_value = case v
                        when Hash then deep_compact_blank(v)
                        when Array then v.map { |item| deep_compact_blank(item) }.compact_blank
                        else v
                        end

      acc[k] = compacted_value if compacted_value.present?
    end.with_indifferent_access
  end

  private

  def process_taxpayer_identification(guarantor_params)
    if gathering_collections_information? &&
       guarantor_params[:taxpayer_identification_attributes].present?
      begin
        if (encrypted_tin = guarantor_params.dig(:taxpayer_identification_attributes,
                                                 :encrypted_tin).presence)
          guarantor_params[:taxpayer_identification_attributes][:tin] = TaxpayerIdentification.new(
            encrypted_tin: encrypted_tin,
            encrypted_tin_iv: guarantor_params.dig(:taxpayer_identification_attributes,
                                                   :encrypted_tin_iv).force_encoding('UTF-8')
          ).tin
          guarantor_params[:taxpayer_identification_attributes].delete :encrypted_tin
          guarantor_params[:taxpayer_identification_attributes].delete :encrypted_tin_iv
        end
      rescue StandardError => _e
        guarantor_params.delete :taxpayer_identification_attributes
      end
    end
    guarantor_params
  end
end
