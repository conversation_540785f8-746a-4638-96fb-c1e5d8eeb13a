class MemberOnboarding::Wizard # rubocop:disable Metrics/ClassLength
  include Rails.application.routes.url_helpers

  MODULE_NAME_TO_MODEL = {
    information_collection: MemberOnboarding::InformationCollection,
    lease_agreement: MemberOnboarding::LeaseAgreement,
    charge: MemberOnboarding::Charge,
    guarantor: MemberOnboarding::Guarantor,
    membership_agreement: MemberOnboarding::MembershipAgreement,
    risk_release: MemberOnboarding::RiskRelease
  }.with_indifferent_access.freeze

  def initialize(token:, current_module: nil)
    @token = token
    @json = json
    @current_module = current_module
  end

  attr_reader :current_module, :token

  def valid_token?
    json.present?
  end

  def name
    @json.dig('configuration', 'name')
  end

  def portfolio_id
    @json.dig('configuration', 'portfolio_id')
  end

  def onboardable_property_ids
    @json.dig('configuration', 'onboardable_property_ids')
  end

  def model(module_id = @current_module)
    MODULE_NAME_TO_MODEL[module_id]
  end

  def insert_configuration(configuration)
    @json = {
      configuration: {
        name: configuration.name,
        portfolio_id: configuration.portfolio_id,
        onboardable_property_ids: configuration.onboardable_property_ids,
        message: configuration.message
      }
    }

    cache
  end

  def update_configuration(configuration)
    @json['configuration']['name'] = configuration.name
    @json['configuration']['message'] = configuration.message

    cache
  end
  
  def set_properties_filtered_flag
    @json ||= {}
    @json['properties_filtered'] = true
    cache
  end
  
  def properties_filtered?
    return false if @json.nil?

    @json['properties_filtered'] == true
  end

  def build_configuration_from_attributes
    MemberOnboarding::Configuration.new(@json['configuration'])
  end

  def upsert_selected_modules(module_ids)
    @json['module_ids'] = module_ids
    @json['module_locations'] = module_ids.each_with_index.to_h

    if @json['lease_agreement'].present? && !guarantor_will_be_there_on_submit?
      @json['lease_agreement']['require_guarantor_signature'] = false
    elsif @json['guarantor'].present? && @json['lease_agreement'].present? && @json['lease_agreement']['require_guarantor_signature']
      @json['guarantor']['optional'] = false
    end

    if @json['membership_agreement'].present? && !guarantor_will_be_there_on_submit?
      @json['membership_agreement']['require_guarantor_signature'] = false
    elsif @json['guarantor'].present? && @json['membership_agreement'].present? && @json['membership_agreement']['require_guarantor_signature']
      @json['guarantor']['optional'] = false
    end

    cache
  end

  def guarantor_will_be_there_on_submit?
    return false unless module_ids.include?('guarantor')
    
    if module_data('guarantor')
      return !(module_data('guarantor')['optional'])
    end
    
    true
  end

  def build_record(attributes, module_id = @current_module)
    record = model(module_id).new(attributes)

    if ['lease_agreement', 'membership_agreement'].include?(module_id)
      # This is to set an attribute on the lease agreement model to
      # decide whether to validate for guarantor signature or not
      record.guarantor_assured_at_submit = guarantor_will_be_there_on_submit?
    elsif module_id == 'guarantor'
      # This is to set an attribute on the guarantor model to
      # decide whether to validate for whether it can be optional
      record.lease_module_requires_guarantor =
        @json.dig('lease_agreement', 'require_guarantor_signature')
    end

    record
  end

  def module_ids
    @json['module_ids'] || []
  end

  def module_data(module_id = @current_module)
    @json[module_id]
  end

  def upsert_module_data(record, module_id = @current_module)
    @json[module_id] = record.wizard_attributes

    cache
  end

  def module_form_partial
    Management::Onboardings::ModuleDetails.module_form_partial(@current_module)
  end

  def previous_path
    return onboarding_module_selection_path(token) if first_module?

    module_path(@json['module_ids'][current_module_index - 1])
  end

  def next_path
    if last_module?
      incomplete_module = first_incomplete_module
      return new_onboarding_assignment_path(@persisted_configuration) if incomplete_module.nil?

      return module_path(incomplete_module)
    end

    module_path(@json['module_ids'][current_module_index + 1])
  end

  def module_path(module_id = @current_module)
    edit_onboarding_module_path(token, module_id)
  end

  def submit_module_path(module_id = @current_module)
    onboarding_module_path(token, module_id)
  end

  def save # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity
    errors = []
    records = []

    configuration = build_configuration_from_attributes

    module_ids.each do |module_id|
      attributes = module_data(module_id).merge(configuration: configuration)
      record = build_record(attributes, module_id)
      records << record

      next if record.valid?

      module_name = Management::Onboardings::ModuleDetails.modules[module_id][:name]

      record.errors.full_messages.each do |message|
        errors << ("#{module_name} - #{message}")
      end
    end

    properties = Property.where(id: configuration.onboardable_property_ids)

    properties.each do |property|
      configuration.onboardable_property_memberships.build(
        property:,
        enhanced: true
      )
    end

    unless configuration.valid?
      configuration.errors.full_messages.each do |message|
        errors << ("Configuration - #{message}")
      end
    end

    return errors if errors.present?

    ActiveRecord::Base.transaction do
      configuration.save!
      records.each(&:save!)
    end

    @persisted_configuration = configuration

    nil
  end

  def submit_wizard?
    last_module? && first_incomplete_module.nil?
  end

  def continue_text
    last_module? ? 'Complete' : 'Next'
  end

  def clear_redis_value
    Redis.instance do |redis|
      redis.del(redis_key)
    end
  end

  def last_module?
    current_module_index == @json['module_ids'].size - 1
  end

  private

  def first_module?
    current_module_index.zero?
  end

  def current_module_index
    @json['module_locations'][@current_module]
  end

  def first_incomplete_module
    @json['module_ids'].find { |module_id| @json[module_id].blank? }
  end

  def json
    Redis.instance do |redis|
      value = redis.get(redis_key)

      JSON.parse(value) if value.present?
    end
  end

  def cache
    Redis.instance do |redis|
      redis.set(redis_key, @json.to_json, ex: 2.days.to_i)
    end
  end

  def redis_key
    "member_onboarding:#{@token}"
  end
end
