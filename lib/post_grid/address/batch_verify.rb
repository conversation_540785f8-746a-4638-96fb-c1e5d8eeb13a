class PostGrid::Address::BatchVerify < PostGrid::Address::Service
  ENDPOINT = '/v1/addver/verifications/batch'.freeze

  def initialize(addresses:)
    super()
    @addresses = Array(addresses)
  end

  def call
    res = post(ENDPOINT, payload)

    results = res.body.dig('data', 'results')

    OpenStruct.new(successful?: true, response: detailed_response(results))
  rescue Faraday::Error => e
    Honeybadger.notify(e)
    OpenStruct.new(successful?: false, error: e.message)
  end

  private

  attr_reader :addresses

  def payload
    {
      addresses: addresses.map { |address| serialize_address(address) }
    }
  end

  def detailed_response(results)
    results.map.with_index do |address, index|
      detailed_address_verification(address['verifiedAddress'], index)
    end
  end

  def detailed_address_verification(address, index)
    original_address = @addresses[index]

    {
      id: original_address.id,
      original: deserialize_address(serialize_address(original_address).with_indifferent_access),
      corrected: deserialize_address(address),
      status: address['status'],
      errors: address['errors']
    }
  end

  def serialize_address(address)
    {
      line1: address.line_one,
      line2: address.line_two,
      city: address.city,
      provinceOrState: address.region,
      postalOrZip: address.postal_code,
      country: address.country
    }
  end

  def deserialize_address(address)
    OpenStruct.new(
      line_one: address['line1'],
      line_two: address['line2'],
      city: address['city'],
      region: address['provinceOrState'],
      postal_code: address['postalOrZip'],
      country: address['country']
    )
  end
end
