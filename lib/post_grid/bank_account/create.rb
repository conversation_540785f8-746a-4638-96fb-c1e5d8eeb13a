class PostGrid::BankAccount::Create < PostGrid::Service
  ENDPOINT = '/print-mail/v1/bank_accounts'.freeze

  def initialize(bank_account:, region: 'US')
    super()
    @bank_account = bank_account
    @region = region
  end

  def call
    if bank_account.postgrid_account_id.present?
      return OpenStruct.new(successful?: true, id: bank_account.postgrid_account_id)
    end

    response = post(ENDPOINT) do |req|
      req.headers['Content-Type'] = 'application/x-www-form-urlencoded'
      req.body = URI.encode_www_form(body)
    end

    unless (postgrid_account_id = response.body['id'])
      fail PostGrid::Service::Error, 'No account ID returned from PostGrid'
    end

    bank_account.update!(postgrid_account_id:)

    OpenStruct.new(successful?: true, id: postgrid_account_id)
  rescue PostGrid::Service::Error => e
    OpenStruct.new(successful?: false, errors: [e.message])
  end

  private

  attr_reader :bank_account, :region

  def signature
    signature = bank_account.owner.representative&.signature_attachment

    fail PostGrid::Service::Error, 'No signature available' unless signature

    bank_account.owner.representative.signature_attachment_url
  end

  def body
    body_hash = {
      bankName: bank_account.name,
      accountNumber: bank_account.account_number,
      bankCountryCode: region,
      signatureImage: signature,
      bankPrimaryLine: bank_account.owner.address.line_one,
      bankSecondaryLine: bank_account.owner.address.line_two
    }

    if region == 'US'
      body_hash[:routingNumber] = bank_account.routing_number
    elsif region == 'CA'
      fail PostGrid::Service::Error, 'Canadian bank accounts not supported'
      # TODO: We don't handle Canadian bank accounts yet
      # body_hash[:transitNumber] = bank_account.routing_number
      # body_hash[:routeNumber] = bank_account.route_number
    else
      fail PostGrid::Service::Error, "Unsupported region: #{region}"
    end

    body_hash
  end
end
