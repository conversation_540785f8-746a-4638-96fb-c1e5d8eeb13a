class PostGrid::Service
  extend Service

  class Error < StandardError; end

  private

  def connection
    Faraday.new(url: 'https://api.postgrid.com') do |faraday|
      faraday.adapter :net_http
      faraday.headers['Content-Type'] = 'application/json'
      faraday.headers['X-API-KEY'] = api_key
      faraday.options.timeout = 25
      faraday.request :json
      faraday.response :json
      faraday.response :raise_error
    end
  end

  def api_key
    ENV.fetch('POSTGRID_API_KEY')
  end

  delegate :post, to: :connection
end
