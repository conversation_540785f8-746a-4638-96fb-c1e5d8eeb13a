class Reports::V3::AgreementExecution < Reports::V3::Report
  def title
    "#{super} Status"
  end

  def self.available_filters
    %i[property date_range type status]
  end

  def self.default_filter_values
    { start_date: 1.month.ago.to_date, end_date: Time.zone.today.to_date }
  end

  def self.status_values
    %w[executed unexecuted]
  end

  def self.type_values
    [
      %w[Lease lease],
      %w[Addendum lease_addendum]
    ] + Agreements::AgreementType.pluck(:name, :slug)
  end

  def mapping
    {
      created_at: 'Created',
      contact: 'Contact',
      property: 'Property',
      document: 'Document',
      signer_one: 'Signer One',
      signer_two: 'Signer Two',
      countersigner: 'Countersigner',
      signatures_last_sent_at: 'Last Sent At',
      executed_at: 'Executed',
      balance: ('Balance' if Customer.current_subdomain.start_with?('chio'))
    }.compact
  end

  def statistics
    agreements = 0
    executed = 0
    pending = 0

    executed_index = mapping.keys.index(:executed_at)

    rows.each do |row|
      agreements += 1

      if row.dig(:cells, executed_index, :value).present?
        executed += 1
      else
        pending += 1
      end
    end

    [
      { value: agreements, label: 'Agreements' },
      { value: executed, label: 'Executed' },
      { value: pending, label: 'Pending' }
    ]
  end

  def basis
    Reports::V3::Basis::Agreement
  end
end
