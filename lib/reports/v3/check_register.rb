class Reports::V3::CheckRegister < Reports::V3::Report
  def self.available_filters
    %i[bank_account status date_range]
  end

  def self.default_filter_values
    {
      payment_method: %i[check],
      start_date: 1.month.ago,
      end_date: Time.zone.today
    }
  end

  def self.status_values
    %w[reconciled unreconciled]
  end

  def grouping
    :bank_account
  end

  def basis
    Reports::V3::Basis::CustomerPayment
  end

  # Unused for this report
  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end

  def mapping
    {
      date: 'Date',
      check_number: 'Number',
      description: 'Description',
      payer: 'Payer',
      payee: 'Payee',
      property: 'Property',
      reconciliation: 'Reconciled',
      amount: 'Amount'
    }
  end

  def footer
    totals_row([7])
  end
end
