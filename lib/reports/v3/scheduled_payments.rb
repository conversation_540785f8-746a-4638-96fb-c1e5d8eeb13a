class Reports::V3::ScheduledPayments < Reports::V3::Report
  def self.available_filters
    if Feature.enabled?(:reports_scope_portfolios, Customer.current)
      %i[portfolio_accounting_context status]
    else
      %i[accounting_context status]
    end
  end

  def self.status_values
    %i[pending failed]
  end

  def basis
    Reports::V3::Basis::ScheduledPayment
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end

  def mapping
    {
      property: 'Property',
      tenant: I18n.t('tenant_term'),
      lease_chain_status: ('Lease Status' if include_lease_status?),
      amount: 'Amount',
      payment_method: 'Payment Method',
      recurring: 'Recurring',
      last_ran: 'Last Ran',
      next_run: 'Next Run'
    }.compact
  end

  private

  def include_lease_status?
    !Customer.current.greek_housing? && !Customer.current.lending?
  end
end
