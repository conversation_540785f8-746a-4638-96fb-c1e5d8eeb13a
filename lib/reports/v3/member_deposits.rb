class Reports::V3::MemberDeposits < Reports::V3::Report
  def self.available_filters
    %i[property tag]
  end

  def self.possible_tags
    Tag
      .joins(:taggings)
      .where(taggings: { taggable_type: 'Agreements::SimpleAgreement' })
      .distinct
  end

  def mapping
    {
      chapter: 'Chapter',
      member: 'Member',
      tags: 'Tags',
      start_date: 'Start Date',
      end_date: 'End Date',
      charged: 'Charged',
      deposit: 'Deposit'
    }
  end

  def rows
    Plutus.in_cash_basis(journal_cache_hint: Company.all) do
      agreements = Agreements::SimpleAgreement
                   .joins(:agreement_type)
                   .includes(:property, :primary_tenant)
                   .references(:property, :primary_tenant)
                   .where(agreements_agreement_types: { name: 'Membership' })
                   .merge(Property.order(name: :asc))
                   .merge(Tenant.order(first_name: :asc))

      if filters.property_id.present?
        agreements = agreements.where(property_id: filters.property_id)
      elsif !user.top_level?
        agreements = agreements.where(property: user.properties)
      end

      if filters.tag.present?
        agreements = agreements
                     .joins(:taggings)
                     .where(taggings: { tag_id: filters.tag })
      end

      accounts = Plutus
                 .account_class
                 .where(id: ChartOfAccounts.select(:security_deposit_account_id))

      agreements.filter_map do |agreement|
        property = agreement.property
        tenant = agreement.primary_tenant

        next unless tenant

        tenants = agreement.tenants

        invoices = Invoice.where(buyer: tenants).or(
          Invoice.where(seller: tenants)
        )

        payments = Payment.where(payer: tenants).or(
          Payment.where(payee: tenants)
        )

        amounts = Plutus
                  .amount_class
                  .joins(:account, :entry)
                  .where(account: accounts)
                  .merge(Plutus.entry_basis_filter)
                  .merge(Plutus.entry_class.where(date: ..Time.zone.today))
                  .merge(Plutus.entry_class.where(property_id: property.id))

        amounts = amounts.merge(
          Plutus.entry_class.where(commercial_document: invoices).or(
            Plutus.entry_class.where(commercial_document: payments)
          )
        )

        debits = amounts.where(type: 'Plutus::DebitAmount').sum(:amount)
        credits = amounts.where(type: 'Plutus::CreditAmount').sum(:amount)

        balance = Money.new(credits - debits)

        charge_items = LineItem.where(
          invoice: invoices,
          receivable_account: accounts
        )

        charged = Money.sum(charge_items.map(&:amount))

        {
          cells: [
            { value: property.name, link: property.url },
            { value: tenant.name, link: tenant.url },
            { value: agreement.tags.map(&:tag).join(', ') },
            { value: agreement.start_date },
            { value: agreement.end_date },
            {
              value: charged,
              link: routes.reports_ledger_path(filters: { tenant_id: tenant.id })
            },
            {
              value: balance,
              link: routes.reports_ledger_path(filters: { tenant_id: tenant.id })
            }
          ]
        }
      end
    end
  end
end
