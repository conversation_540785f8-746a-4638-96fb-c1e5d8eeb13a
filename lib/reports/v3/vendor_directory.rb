class Reports::V3::VendorDirectory < Reports::V3::Report
  def basis
    Reports::V3::Basis::Vendor
  end

  def self.available_filters
    %i[vendor_kind tag eligible_for_1099]
  end

  def subheader
    Array.wrap(filters.describe).compact.join(', ')
  end

  def mapping
    {
      name: 'Name',
      first_name: 'First Name',
      last_name: 'Last Name',
      email: 'Email',
      phone: 'Phone',
      street_address: 'Street Address',
      city: 'City',
      region: 'State',
      postal_code: 'Zip Code',
      kind: 'Type',
      tags: 'Tags',
      preferred_remittance: 'Preferred Remittance',
      deposit_last_four: 'Deposit Last Four',
      business_type: 'Entity Type',
      eligible_for_1099: '1099 Eligible'
    }.tap do |mapping|
      mapping[:tin] = 'TIN' if user.role.can_see_eins?
    end
  end
end
