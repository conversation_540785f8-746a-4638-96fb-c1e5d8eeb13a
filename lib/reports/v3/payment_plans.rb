class Reports::V3::PaymentPlans < Reports::V3::Report
  def self.available_filters
    %i[property date status]
  end

  def self.status_values
    %i[failed]
  end

  def self.default_filter_values
    { date: Time.zone.today }
  end

  def mapping
    {
      contact: 'Contact',
      property: 'Property',
      amount: 'Amount',
      installment_count: 'Installments',
      first_installment: 'First Installment',
      next_installment: 'Next Installment',
      last_installment: 'Last Installment',
      payment_method: 'Payment Method',
      created_at: 'Agreed',
      ip_address: 'IP Address'
    }
  end

  def basis
    Reports::V3::Basis::PaymentPlan
  end
end
