class Reports::V3::VendorContracts < Reports::V3::Report
  def self.available_filters
    %i[vendor status]
  end

  def self.status_values
    %i[current upcoming previous]
  end

  def mapping
    {
      vendor: 'Vendor',
      entity: 'Enti<PERSON>',
      name: 'Name',
      start_date: 'Start Date',
      end_date: 'End Date',
      notes: 'Notes'
    }
  end

  def basis
    Reports::V3::Basis::VendorContract
  end
end
