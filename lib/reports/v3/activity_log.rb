class Reports::V3::ActivityLog < Reports::V3::Report
  def self.available_filters
    %i[property employee activity_type date_range]
  end

  def self.default_filter_values
    {
      start_date: 7.days.ago.to_date,
      end_date: Time.zone.today
    }
  end

  def self.type_values
    %w[
      comment
      executed_lease
      guest_card
      maintenance_ticket
      maintenance_ticket_closed
      maintenance_ticket_comment
      prepared_lease
      sent_application
      submitted_application
      tenant_payment
      timeline_entry
      tour
    ]
  end

  def mapping
    {
      date: 'Date',
      time: 'Time',
      property: 'Property',
      tenant: 'Tenant',
      employee: 'Employee',
      type: 'Type',
      description: 'Description'
    }
  end

  def basis
    Reports::V3::Basis::ActivityLogEntry
  end
end
