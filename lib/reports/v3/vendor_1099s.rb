class Reports::V3::Vendor1099s < Reports::V3::TenNinetyNines
  def self.available_filters
    %i[entity year type zero_amounts]
  end

  def self.type_values
    [['Eligible', 'eligible']]
  end

  def self.default_filter_values
    super.merge(entity_id: Customer.current.client_entity.id)
  end

  def name
    'Vendor 1099s'
  end

  def basis
    Class.new(super) do
      def self.columns
        superclass.columns
      end

      def entity
        @entity ||= scope.company
      end

      def payees
        @payees ||= begin
          query = case filters.type
                  when 'eligible'
                    Vendor.where(
                      business_type: %i[
                        llc
                        sole_proprietorship
                        single_member_llc
                        partnership
                      ]
                    )
                  else
                    Vendor.all
                  end

          query.order(name: :asc).unarchived(date_range.last.end_of_day)
        end
      end

      def source_rows
        amounts_hash = payees.pluck(:id).index_with(Money.zero)

        grouped_sums.each do |item|
          amounts_hash[item.payee_id] = Money.new(item.total_paid_cents)
        end

        query = payees.includes(:address,
                                :taxpayer_identification,
                                :vendor_contacts)

        case filters.zero_amounts
        when 'only'
          amounts_hash.select! { |_, v| v.zero? }
          query = query.where(id: amounts_hash.keys)
        when 'show'
          # Pass
        else # 'hide', blank
          amounts_hash.reject! { |_, v| v.zero? }
          query = query.where(id: amounts_hash.keys)
        end

        query.map do |payee|
          Reports::V3::TenNinetyNines::Entry.new(
            entity: entity.decorate,
            payee: payee.decorate,
            amount: amounts_hash[payee.id]
          )
        end
      end

      def grouped_sums
        payments =
          Payment
          .where(
            payer: [entity, entity.properties],
            payee: payees,
            date: date_range,
            reversed_at: nil
          ).where.not(kind: :credit_note)

        payments
          .select('
            payments.payee_id, SUM(payments.amount_cents) AS total_paid_cents
          ')
          .group(:payee_id)
      end
    end
  end
end
