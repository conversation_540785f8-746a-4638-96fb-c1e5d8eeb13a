class Reports::V3::Basis::Vendor < Reports::V3::Basis::Base
  def relation
    ::Vendor.all
  end

  def ordered(query)
    query.reorder(name: :asc)
  end

  def joined(query)
    query.includes(
      :vendor_contacts,
      :address,
      :tags,
      :taxpayer_identification,
      :default_electronic_deposit_account
    )
  end

  def filtered(query)
    query = query.where(kind: filters.kind) if filters.kind.present?

    query = query.where(tags: { id: filters.tag }) if filters.tag.present?

    case filters.eligible_for_1099
    when 'eligible' then query.eligible_for_1099
    when 'ineligible' then query.ineligible_for_1099
    else query
    end
  end

  column :name do
    {
      value: name,
      link: routes.vendor_path(self)
    }
  end

  column :first_name do
    {
      value: first_name
    }
  end

  column :last_name do
    {
      value: last_name
    }
  end

  column :kind do
    {
      value: kind.humanize,
      justify: :center
    }
  end

  column :email do
    {
      value: email
    }
  end

  column :phone do
    {
      value: formatted_phone
    }
  end

  column :street_address do
    {
      value: address&.street_address
    }
  end

  column :city do
    {
      value: address&.city
    }
  end

  column :region do
    {
      value: address&.region
    }
  end

  column :postal_code do
    {
      value: address&.postal_code
    }
  end

  column :tags do
    {
      value: tags.map(&:to_s).join(', ')
    }
  end

  column :preferred_remittance do
    {
      value: decorate.preferred_disbursement_text,
      justify: :center
    }
  end

  column :deposit_last_four do
    account = default_electronic_deposit_account

    if account
      last_four = account.account_number.to_s[-4..]

      { value: last_four, justify: :center }
    end
  end

  column :business_type do
    { value: decorate.business_type_text }
  end

  column :tin do
    { value: tin }
  end

  column :eligible_for_1099 do
    { value: eligible_for_1099? ? 'Yes' : 'No', justify: :center }
  end
end
