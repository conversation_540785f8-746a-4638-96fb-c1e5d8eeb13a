class Reports::V3::Basis::VendorContract < Reports::V3::Basis::Base
  def relation
    ::Vendor::Contract.unarchived
  end

  def filtered(query)
    if filters.vendor_id.present?
      query = query.where(vendor_id: filters.vendor_id)
    end

    case filters.status
    when 'current' then query.current
    when 'upcoming' then query.upcoming
    when 'previous' then query.previous
    else
      query
    end
  end

  column :vendor do
    { value: vendor.name, link: vendor.url }
  end

  column :entity do
    { value: entity.name, link: entity.url }
  end

  column :name do
    { value: name }
  end

  column :start_date do
    { value: start_date }
  end

  column :end_date do
    { value: end_date }
  end

  column :notes do
    { value: notes }
  end
end
