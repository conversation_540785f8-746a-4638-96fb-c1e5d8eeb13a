class Reports::V3::UtilityDirectory < Reports::V3::Report
  def self.available_filters
    %i[portfolio_accounting_context vendor utility_type utility_responsibility]
  end

  def mapping
    {
      property: 'Property',
      utility_type: 'Utility',
      vendor: 'Vendor',
      responsibility: 'Responsibility'
    }
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end

  def basis
    Class.new(Reports::V3::Basis::Base) do
      def relation
        types = Utilities::UTILITY_TYPES

        utility_ids = types.map do |type|
          "#{type}_utility_id"
        end.join(',')

        utility_responsibilities = types.map do |type|
          "#{type}_utility_responsibility"
        end.join(',')

        utility_types = types.map do |type|
          "'#{type}'"
        end.join(',')

        Property.from(
          Property
            .unarchived
            .select(
              :id,
              :name,
              "UNNEST(ARRAY[#{utility_ids}]) AS vendor_id",
              "UNNEST(ARRAY[#{utility_responsibilities}]) AS responsibility",
              "UNNEST(ARRAY[#{utility_types}]) AS utility_type"
            ),
          :properties
        ).select('properties.*')
      end

      def scoped(query)
        scope.apply(query) { |s| where(id: s.properties) }
      end

      def filtered(query)
        if filters.vendor_id.present?
          query = query.where(vendor_id: filters.vendor_id)
        end

        if filters.utility_type.present?
          query = query.where(utility_type: filters.utility_type)
        end

        if filters.utility_responsibility.present?
          query = query.where(responsibility: filters.utility_responsibility)
        end

        query
      end

      column :property do
        { value: name, link: property_path(self) }
      end

      column :utility_type do
        { value: utility_type.titleize, justify: :center }
      end

      column :vendor do
        Thread.current[:vendor_names] ||= Vendor.pluck(:id, :name).to_h

        if vendor_id
          {
            value: Thread.current[:vendor_names][vendor_id],
            link: vendor_path(vendor_id),
            justify: :center
          }
        else
          {
            value: '-',
            link: edit_property_path(self, anchor: 'utilities'),
            justify: :center
          }
        end
      end

      column :responsibility do
        value = case responsibility
                when 0 then 'Owner'
                when 1 then 'Tenant'
                else '-'
                end

        {
          value: value,
          link: edit_property_path(self, anchor: 'utilities'),
          justify: :center
        }
      end
    end
  end
end
