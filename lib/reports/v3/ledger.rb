class Reports::V3::Ledger < Reports::V3::Report
  def ledger
    return nil unless entity

    entity.ledger.decorate
  end

  def title
    return 'Ledger' unless entity

    ledger.report_title
  end

  def subheader
    return '' unless entity

    ledger.report_subheader
  end

  def brand
    entity.try(:property)&.brand || super
  end

  def self.available_filters
    return [] if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])

    %i[tenant_search]
  end

  def self.default_filter_values
    {}
  end

  def as_json(options = {})
    super(options).merge(table_class: table_class)
  end

  # TODO: use super except sortable
  def table_class
    'ui very basic selectable single line compact unstackable table'
  end

  def statistics
    return nil unless entity

    the_balance = balance
    the_balance *= -1 if flip_direction?

    [
      {
        value: the_balance.format,
        label: 'Current Balance'
      },
      {
        value: charged_security_deposit.format,
        label: 'Charged Deposit'
      },
      {
        value: held_security_deposit.format,
        label: 'Held Deposit'
      }
    ]
  end

  def rows
    return [] unless entity

    entries.map do |entry|
      balance = Money.new(entry[:balance])
      debit = Money.new(entry[:debit])
      credit = Money.new(entry[:credit])

      if flip_direction?
        debit, credit = credit, debit
        balance *= -1
      end

      {
        cells: [
          { value: entry[:date] },
          { value: entry[:description], link: entry[:url] },
          { value: entry[:reference], link: entry[:url] },
          {
            value: debit.nonzero? ? debit : nil,
            sort: debit.cents,
            justify: :right
          },
          {
            value: credit.nonzero? ? credit : nil,
            sort: credit.cents,
            justify: :right
          },
          {
            value: balance.zero? ? '$0.00' : balance.format,
            sort: balance.cents,
            justify: :right
          }
        ]
      }
    end
  end

  def header_values
    %w[Date Description Reference Debit Credit Balance]
  end

  # TODO: configure normal direction on ledger
  def flip_direction?
    false
  end

  private

  def entity
    @entity ||= if filters.lease_id
                  Lease.find(filters.lease_id)
                elsif filters.simple_agreement_id
                  Agreements::SimpleAgreement.find(filters.simple_agreement_id)
                else
                  id = filters.tenant_id.presence
                  available = TenantsQuery.for_user(user).order(:last_name)
                  available.find_by(id: id) || available.first
                end
  end

  delegate :address,
           :journal_entries,
           :invoices, :payments, :balance,
           :charged_security_deposit, :held_security_deposit,
           to: :ledger

  def check_class(entry, klass)
    entry.is_a?(klass) ? entry.amount.cents : 0
  end

  def entries
    balance = 0

    ledger.amounts.decorate.sort_by(&:date).map do |amount|
      debit = amount.debit? ? amount.money : Money.zero
      credit = amount.credit? ? amount.money : Money.zero

      balance += debit - credit

      {
        date: amount.date,
        description: amount.description,
        reference: amount.reference_number,
        debit: debit,
        credit: credit,
        balance: balance,
        url: amount.commercial_document&.url
      }
    end.reverse
  end
end
