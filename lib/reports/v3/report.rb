class Reports::V3::Report
  include Exportable::XLSX
  include Reports::V3::RejectChanges

  attr_reader :scope, :filters, :grouping, :user, :request

  def initialize(scope: default_scope,
                 filters: default_filters,
                 grouping: default_grouping,
                 user: nil,
                 request: nil)
    @scope = scope
    @filters = filters
    @grouping = grouping
    @user = user
    @request = request
  end

  def name
    self.class.name.demodulize.titleize
  end

  def title
    name
  end

  def self.available_filters
    []
  end

  def self.default_filter_values
    {}
  end

  def self.possible_tags
    Tag.all
  end

  def mapping
    {}
  end

  def default_scope
    OpenStruct.new
  end

  def default_filters
    OpenStruct.new
  end

  def default_grouping
    nil
  end

  def available_filters
    self.class.available_filters
  end

  def as_json(options = {})
    values = {
      version: 2,
      title: title,
      subheader: subheader,
      header: header_row,
      footer: footer,
      statistics: statistics
    }

    if grouping
      values.merge(sections: sections)
    else
      values.merge(rows: rows)
    end
  end

  def as_html(options = {})
    as_json(options)
  end

  def as_xlsx(options = {})
    as_json(options)
  end

  def filename(extension)
    name = [
      title.parameterize,
      Time.zone.now.to_fs(:export).dasherize
    ].join('-')

    ActiveStorage::Filename.new("#{name}.#{extension}").sanitized
  end

  def empty?
    return false unless try(:basis)

    if grouping
      sections.flat_map { _1[:rows] }.none?
    else
      rows.none?
    end
  end

  def brand
    if scope.is_a?(Reports::V3::Scope)
      scope.context.try(:brand) || Customer.current.brand
    else
      Customer.current.brand
    end
  end

  delegate :rows, :sections, to: :basis_instance

  protected

  def subheader
    [scope.describe, filters.describe].flatten.compact_blank.uniq.join(', ')
  end

  def statistics
    []
  end

  # Helper for the common header row format.
  def header_row
    {
      cells: header_values.map.with_index do |text, index|
        alignment = index.zero? ? 'left' : 'center'
        { type: 'b', justify: alignment, value: text }
      end
    }
  end

  def header_values
    mapping.values
  end

  def footer
    nil
  end

  # Helper for common sum footer format.
  def totals_row(column_indices)
    {
      cells: Array.new(column_count).map.with_index do |_, index|
        if index.zero?
          { value: 'Total', type: 'b' }
        elsif index.in?(column_indices)
          { value: sum_column[index], type: 'b' }
        else
          {}
        end
      end
    }
  end

  # Include the ability to user Rails path helpers for links.
  def routes
    Rails.application.routes.url_helpers
  end

  def request_canceled?
    request&.reload&.canceled?
  end

  private

  def basis_instance
    @basis_instance ||= basis.new(
      mapping: mapping,
      scope: scope,
      filters: filters,
      grouping: grouping,
      user: user,
      request: request
    )
  end

  def column_count
    header_values.length
  end

  def sum_column(target_rows = rows)
    lambda do |index|
      Money.sum(
        target_rows.filter_map do |row|
          row.dig(:cells, index, :value)
        end
      )
    end
  end

  def use_account_matrix?
    return @use_account_matrix if defined?(@use_account_matrix)

    @use_account_matrix = Feature.enabled?(
      :account_balance_matrix,
      Customer.current
    )
  end
end
