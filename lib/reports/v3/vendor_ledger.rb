class Reports::V3::VendorLedger < Reports::V3::Ledger
  def self.available_filters
    %i[entity vendor]
  end

  def statistics
    super.take(1)
  end

  def entity
    if filters.entity_id
      user.companies.find(filters.entity_id)
    else
      user.companies.first
    end
  end

  def vendor
    if filters.vendor_id
      Vendor.find(filters.vendor_id)
    else
      Vendor.first
    end
  end

  def ledger
    return nil unless entity

    Accounting::Ledger::Vendor.new(vendor: vendor, entity: entity).decorate
  end

  def flip_direction?
    true
  end
end
