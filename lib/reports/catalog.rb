class Reports::Catalog
  extend Reports::Catalog::FromSlug

  HISTORY_SIZE = 100

  GLOBAL_REPORT_USAGE_STREAM = 'property_manager_report_usage_stream'.freeze

  COLLECTIONS_SUBDOMAINS = %w[
    adpi adpi-sandbox
    agneshazel-hayes
    barrister
    chio-mu
    chio-psi
    ghm ghm-sandbox
    kcpmc
    kpm
    sae sae-sandbox
    sampm
    tym
    zetasigma
    zzzeta
  ].freeze

  SECOND_NATURE_SUBDOMAINS = %w[
    pmi-utah pmi-utah-sandbox
    mph-sandbox marketplacehomes
    sampm
  ].freeze

  attr_reader :user

  def initialize(user:, &block)
    @user = user
    @link_from_slug = block
  end

  def columns(max)
    title_offset = 3 # Extra padding for column title

    Array.new(max) { [] }.tap do |columns|
      categories.each do |category|
        slot = columns.min_by do |column|
          column.sum { |category| category.size + title_offset }
        end
        slot << category
      end
    end
  end

  def categories
    available_reports.group_by { |r| r[:category] }.sort_by(&:first).map do |category, reports|
      report_objects = reports.map do |report|
        OpenStruct.new(
          name: report.fetch(:name, report[:slug].titleize),
          slug: report[:slug],
          link: link(report[:slug]),
          favorite?: favorites.include?(report[:slug])
        )
      end.sort_by(&:name)

      OpenStruct.new(
        name: category,
        reports: report_objects,
        size: report_objects.size
      )
    end
  end

  def favorites
    user.try(:favorite_reports) || []
  end

  def favorite(report_name)
    user.favorite_reports.append(report_name)
    user.favorite_reports.uniq!
    user.favorite_reports.sort!
    user.save!
  end

  def unfavorite(report_name)
    user.favorite_reports.delete(report_name)
    user.save!
  end

  def recent
    report_history.uniq
  end

  def frequent
    report_history.group_by { |n| n }.sort_by { |_, a| -a.count }.map(&:first)
  end

  # Record a visit from clicking on a report in the report index,
  # but not subsequent reloads or filter changes
  def record_visit(report_name)
    Redis.instance do |r|
      r.lpush(report_history_key, report_name)
      r.ltrim(report_history_key, 0, HISTORY_SIZE - 1)
    end
  end

  # Record a report load, including reloads and filter changes
  def record_load(report_name)
    Redis.instance do |r|
      queue = Sidekiq::Queue['reports']

      payload = {
        timestamp: Time.zone.now.to_f,
        customer: Customer.current_subdomain,
        user_id: user.id,
        report: report_name,
        queue_latency: queue.latency,
        queue_busy: queue.busy,
        queue_size: queue.size,
        queue_limit: reports_queue_limit
      }

      r.xadd(GLOBAL_REPORT_USAGE_STREAM, payload, maxlen: 4096)
    end
  end

  # rubocop:disable Metrics/MethodLength
  def all
    [
      {
        slug: 'damage-waivers',
        category: 'Accounting',
        customer_whitelist: %w[ghm ghm-sandbox sae sae-sandbox]
      },
      {
        slug: 'general-ledger-balances',
        category: 'Accounting',
        customer_whitelist: %w[marketplacehomes]
      },
      {
        slug: 'bank-balances',
        category: 'Accounting',
        customer_whitelist: %w[adpi adpi-sandbox ghm ghm-sandbox jlgardel sae sae-sandbox staging]
      },
      {
        slug: 'trailing-twelve',
        category: 'Accounting',
        customer_whitelist: []
      },
      {
        slug: 'balance-sheet',
        category: 'Accounting'
      },
      {
        slug: 'cash-flow-statement',
        category: 'Accounting'
      },
      {
        slug: 'profit-and-loss',
        category: 'Accounting'
      },
      {
        slug: 'occupancy',
        category: 'Portfolio',
        customer_whitelist: []
      },
      {
        slug: 'semester-gross-potential-rent',
        category: 'Custom Reports',
        customer_whitelist: ['brown']
      },
      {
        slug: 'rent-schedule',
        category: tenants_category,
        customer_whitelist: ['brown']
      },
      {
        slug: 'general-ledger',
        category: 'Accounting'
      },
      {
        slug: 'assistance-payments',
        category: tenants_category
      },
      {
        slug: 'resident-insurance-compliance',
        category: tenants_category
      },
      {
        slug: 'expiring-resident-insurance',
        category: tenants_category
      },
      {
        slug: 'waitlist',
        category: 'Leasing'
      },
      {
        slug: 'parking',
        category: 'Parking'
      },
      {
        slug: 'lease-expiration',
        category: 'Leasing'
      },
      {
        slug: 'commercial-parking',
        category: 'Parking',
        customer_whitelist: ['brown']
      },
      {
        slug: 'move-in-move-out',
        category: 'Leasing'
      },
      {
        slug: 'work-orders',
        category: 'Maintenance'
      },
      {
        slug: 'rent-roll',
        category: 'Leasing'
      },
      {
        slug: 'rent-roll-analysis',
        category: 'Leasing'
      },
      {
        slug: 'rent-roll-detail',
        category: 'Leasing'
      },
      {
        slug: 'rent-roll-with-applicants',
        category: 'Leasing',
        customer_whitelist: []
      },
      {
        slug: 'rent-roll-with-charges',
        category: 'Leasing'
      },
      {
        slug: 'prospects',
        category: 'Marketing'
      },
      {
        slug: 'prospect-traffic',
        category: 'Marketing'
      },
      {
        slug: 'prospect-activity',
        category: 'Marketing'
      },
      {
        slug: 'lease-execution',
        category: 'Leasing'
      },
      {
        slug: 'electronic-payments',
        category: tenants_category
      },
      {
        slug: 'utility-transfers',
        category: 'Leasing'
      },
      {
        slug: 'utility-directory',
        category: 'Portfolio'
      },
      {
        slug: 'security-deposits',
        category: tenants_category
      },
      {
        slug: 'ledger',
        category: 'Accounting'
      },
      {
        slug: 'residency',
        category: 'Portfolio'
      },
      {
        slug: 'tenant-balances',
        category: tenants_category
      },
      {
        slug: 'delinquency',
        category: tenants_category
      },
      {
        slug: 'prepayment',
        category: 'Accounting'
      },
      {
        slug: 'tenant-portal-adoption',
        category: 'System'
      },
      {
        slug: 'owner-portal-adoption',
        category: 'System'
      },
      {
        slug: 'scheduled-payments',
        category: tenants_category
      },
      {
        slug: 'resident-birthdays',
        category: tenants_category
      },
      {
        slug: 'inspections',
        name: I18n.t('activerecord.models.inspection/report').pluralize,
        category: 'Maintenance'
      },
      {
        slug: 'inspection-templates',
        name: I18n.t('activerecord.models.inspection/template').pluralize,
        category: 'System'
      },
      {
        slug: 'tours',
        category: 'Marketing'
      },
      {
        slug: 'lease-applications',
        category: 'Leasing'
      },
      {
        slug: 'unit-directory',
        category: 'Portfolio'
      },
      {
        slug: 'activity-log',
        category: 'System'
      },
      {
        slug: 'check-register',
        category: 'Accounting'
      },
      {
        slug: 'deposit-register',
        category: 'Accounting'
      },
      {
        slug: 'leasing-commission',
        category: 'Organization'
      },
      {
        slug: 'trial-balance',
        category: 'Accounting'
      },
      {
        slug: 'comparative-trial-balance',
        category: 'Accounting',
        customer_whitelist: %w[ghm ghm-sandbox sae sae-sandbox]
      },
      {
        slug: 'comparative-income-statement',
        category: 'Accounting',
        customer_whitelist: %w[staging marketplacehomes mph-sandbox]
      },
      {
        slug: 'comparative-cash-flow-statement',
        category: 'Accounting',
        customer_whitelist: %w[staging marketplacehomes mph-sandbox]
      },
      {
        slug: 'journal-allocation',
        category: 'Accounting',
        customer_whitelist: []
      },
      {
        slug: 'applicant-statistics',
        category: 'Marketing'
      },
      {
        slug: 'aging-payables',
        category: 'Accounting'
      },
      {
        slug: 'aging-receivables',
        category: 'Accounting'
      },
      {
        slug: 'lease-aging-receivables',
        category: 'Accounting',
        customer_whitelist: %w[demo mph-sandbox marketplacehomes wrp]
      },
      {
        slug: 'budget-variance',
        category: 'Accounting'
      },
      {
        slug: 'owner-statement',
        category: 'Fee Management'
      },
      {
        slug: 'owner-balances',
        category: 'Fee Management'
      },
      {
        slug: 'owner-property-balances',
        category: 'Fee Management'
      },
      {
        slug: 'unapplied-payments',
        category: 'Accounting'
      },
      {
        slug: 'unapplied-tenant-payments',
        category: 'Accounting'
      },
      {
        slug: 'unapplied-vendor-payments',
        category: 'Accounting'
      },
      {
        slug: 'vendor-directory',
        category: 'Maintenance'
      },
      {
        slug: 'vendor-contracts',
        category: 'Maintenance'
      },
      {
        slug: 'management-contracts',
        category: 'Fee Management'
      },
      {
        slug: 'property-contract-settings',
        category: 'Fee Management'
      },
      {
        slug: 'tenancy-schedule',
        category: 'Leasing'
      },
      {
        slug: 'vacancy-schedule',
        category: 'Leasing'
      },
      {
        slug: 'audit-log',
        category: 'System'
      },
      {
        slug: 'tenant-payments',
        category: tenants_category
      },
      {
        slug: 'entity-directory',
        category: 'Fee Management'
      },
      {
        slug: 'owner-directory',
        category: 'Fee Management'
      },
      {
        slug: 'property-directory',
        category: 'Portfolio'
      },
      {
        slug: 'income-statement',
        category: 'Accounting'
      },
      {
        slug: 'maintenance-income',
        category: 'Maintenance'
      },
      {
        slug: 'account-ledger',
        category: 'Accounting'
      },
      {
        slug: 'management-fees',
        category: 'Fee Management'
      },
      {
        slug: 'receivables-summary',
        category: 'Accounting'
      },
      {
        slug: 'charge-register',
        category: 'Accounting'
      },
      {
        slug: 'expense-register',
        category: 'Accounting'
      },
      {
        slug: 'unit-status',
        category: 'Leasing'
      },
      {
        slug: 'ten-ninety-nines',
        category: 'Accounting',
        customer_whitelist: []
      },
      {
        slug: 'owner-1099s',
        category: 'Tax Preparation'
      },
      {
        slug: 'vendor-1099s',
        category: 'Tax Preparation'
      },
      {
        slug: 'journal-integrity',
        category: 'Accounting',
        customer_whitelist: %w[ccp ghm ghm-sandbox sae sae-sandbox]
      },
      {
        slug: 'unbalanced-forwards',
        category: 'Accounting',
        customer_whitelist: %w[ghm ghm-sandbox sae sae-sandbox]
      },
      {
        slug: 'control-adjustments',
        category: 'Accounting',
        customer_whitelist: ['ccp']
      },
      {
        slug: 'customer-ledger',
        category: 'Fee Management',
        customer_whitelist: ['ccp']
      },
      {
        slug: 'vendor-ledger',
        category: 'Accounting'
      },
      {
        slug: 'period-change',
        category: 'Accounting'
      },
      {
        slug: 'period-change-balance-sheet',
        category: 'Accounting'
      },
      {
        slug: 'itemized-damages',
        category: 'Leasing'
      },
      {
        slug: 'electronic-bill-payments',
        category: 'Accounting'
      },
      {
        slug: 'unit-availability',
        category: 'Marketing'
      },
      {
        slug: 'commercial-rent-roll',
        category: 'Commercial',
        customer_whitelist: %w[encoredev alever staging]
      },
      {
        slug: 'mulberry-farms-rent-roll',
        category: 'Custom Reports',
        customer_whitelist: %w[marketplacehomes alever staging]
      },
      {
        slug: 'payment-plans',
        category: tenants_category,
        customer_kind_whitelist: %w[greek_housing]
      },
      {
        slug: 'member-portal-adoption',
        category: tenants_category,
        customer_kind_whitelist: %w[greek_housing]
      },
      {
        slug: 'member-directory',
        category: tenants_category,
        customer_kind_whitelist: %w[greek_housing]
      },
      {
        slug: 'guardian-directory',
        category: tenants_category,
        customer_kind_whitelist: %w[greek_housing]
      },
      {
        slug: 'member-deposits',
        category: tenants_category,
        customer_kind_whitelist: %w[greek_housing]
      },
      {
        slug: 'maintenance-feedback',
        category: 'Maintenance'
      },
      {
        slug: 'employee-directory',
        category: 'Organization'
      },
      {
        slug: 'refinance-opportunities',
        category: 'Opportunities',
        customer_whitelist: %w[demo gebrael gebraelmgmt]
      },
      {
        slug: 'consolidated-trial-balance',
        category: 'Accounting',
        customer_whitelist: %w[ghm ghm-sandbox sae sae-sandbox]
      },
      {
        slug: 'consolidated-balance-sheet',
        category: 'Accounting',
        customer_whitelist: %w[ghm ghm-sandbox sae sae-sandbox 1827-properties 1872-sandbox]
      },
      {
        slug: 'consolidated-income-statement',
        category: 'Accounting',
        customer_whitelist: %w[ghm ghm-sandbox sae sae-sandbox 1827-properties 1872-sandbox]
      },
      {
        slug: 'consolidated-property-income-statement',
        category: 'Accounting',
        customer_whitelist: %w[]
      },
      {
        slug: 'consolidated-budget-variance',
        category: 'Accounting',
        customer_whitelist: %w[ghm ghm-sandbox sae sae-sandbox]
      },
      {
        slug: 'consolidated-property-budget-variance',
        category: 'Accounting',
        customer_whitelist: %w[1827-properties 1872-sandbox]
      },
      {
        slug: 'investor-income-statement',
        category: 'Custom Reports',
        customer_whitelist: %w[marketplacehomes mph-sandbox]
      },
      {
        slug: 'subdivision-income-statement',
        category: 'Custom Reports',
        customer_whitelist: %w[marketplacehomes mph-sandbox]
      },
      {
        slug: 'subdivision-trial-balance',
        category: 'Custom Reports',
        customer_whitelist: %w[marketplacehomes mph-sandbox]
      },
      {
        slug: 'data-tape-rent-roll',
        category: 'Custom Reports',
        customer_whitelist: %w[marketplacehomes mph-sandbox]
      },
      {
        slug: 'lease-audit',
        category: 'Leasing',
        customer_whitelist: %w[alever mph-sandbox marketplacehomes staging]
      },
      {
        slug: 'electronic-signatures',
        category: 'System'
      },
      {
        slug: 'agreement-execution',
        category: 'System'
      },
      {
        slug: 'custom-data',
        category: 'System'
      },
      {
        slug: 'tenant-reporting-services',
        category: 'Portfolio',
        customer_whitelist: %w[pmi-utah pmi-utah-sandbox]
      },
      {
        slug: 'reservations',
        category: 'Leasing'
      },
      {
        slug: 'management-revenue',
        category: 'Fee Management',
        customer_whitelist: %w[gebraelmgmt staging]
      },
      {
        slug: 'unrecovered-expenses',
        category: 'Fee Management',
        customer_whitelist: %w[gebraelmgmt marketplacehomes staging]
      },
      {
        slug: 'management-income-statement',
        category: 'Fee Management',
        customer_whitelist: %w[gebraelmgmt marketplacehomes evergreen staging]
      },
      {
        slug: 'consolidated-management-income-statement',
        category: 'Fee Management',
        customer_whitelist: %w[gebraelmgmt marketplacehomes evergreen alever]
      },
      {
        slug: 'management-general-ledger',
        category: 'Fee Management',
        customer_whitelist: []
      },
      {
        slug: 'expense-report',
        category: 'Accounting'
      },
      {
        slug: 'landlord-verification',
        category: 'Leasing'
      },
      {
        slug: 'second-nature',
        category: 'System',
        customer_whitelist: SECOND_NATURE_SUBDOMAINS
      },
      {
        slug: 'second-nature-concierge',
        category: 'System',
        customer_whitelist: SECOND_NATURE_SUBDOMAINS
      },
      {
        slug: 'second-nature-monthly-verification',
        category: 'System',
        customer_whitelist: SECOND_NATURE_SUBDOMAINS
      },
      {
        slug: 'second-nature-monthly-verification-detail',
        category: 'System',
        customer_whitelist: SECOND_NATURE_SUBDOMAINS
      },
      {
        slug: 'tenant-directory',
        category: tenants_category,
        customer_whitelist: %w[wrp sigep-azbeta jlgardel]
      },
      {
        slug: 'transaction-batches',
        category: 'Accounting'
      },
      {
        slug: 'collections',
        category: 'Collections',
        customer_whitelist: COLLECTIONS_SUBDOMAINS
      },
      {
        slug: 'loan-directory',
        category: 'Lending',
        customer_whitelist: %w[cod]
      },
      {
        slug: 'loan-summary',
        category: 'Lending',
        customer_whitelist: %w[cod]
      },
      {
        slug: 'amortization-schedule',
        category: 'Lending',
        customer_whitelist: %w[cod]
      },
      {
        slug: 'loan-ledger',
        category: 'Lending',
        customer_whitelist: %w[cod]
      },
      {
        slug: 'loan-payments',
        category: 'Lending',
        customer_whitelist: %w[cod lending]
      },
      {
        slug: 'lease-directory',
        category: 'Leasing'
      },
      {
        slug: 'listings',
        category: 'Marketing'
      },
      {
        slug: 'evictions',
        category: 'Leasing'
      },
      {
        slug: 'investor-home-data',
        category: 'Custom Reports',
        customer_whitelist: %w[mph-sandbox marketplacehomes]
      },
      {
        slug: 'conservice-template',
        category: 'Custom Reports',
        customer_whitelist: %w[mph-sandbox marketplacehomes]
      },
      {
        slug: 'recurring-invoices',
        category: 'Accounting'
      },
      {
        slug: 'recurring-journal-entries',
        category: 'Accounting'
      },
      {
        slug: 'maintenance-appointments',
        category: 'Maintenance'
      },
      {
        slug: 'unpaid-lease-charges',
        category: 'Leasing'
      },
      {
        slug: 'account-balance',
        category: 'Accounting'
      },
      {
        slug: 'collections-communications',
        category: 'Collections',
        customer_whitelist: COLLECTIONS_SUBDOMAINS
      },
      {
        slug: 'collections-opt-outs',
        category: 'Collections',
        customer_whitelist: COLLECTIONS_SUBDOMAINS
      },
      {
        slug: 'tenant_addresses',
        category: 'Fee Management',
        customer_whitelist: %w[jlgardel]
      },
      {
        slug: 'ar-for-adpi-awards',
        category: 'Custom Reports',
        customer_whitelist: %w[adpi]
      },
      {
        slug: 'alpha-initiation-payments',
        category: 'Custom Reports',
        customer_whitelist: %w[adpi]
      }
    ]
  end
  # rubocop:enable Metrics/MethodLength

  def available_reports
    all.select do |report|
      report_available_to_customer?(report) && report_available_to_user?(report)
    end + Report::SQL.all.map do |report|
      {
        slug: report.slug,
        category: report.category.titleize
      }
    end
  end

  private

  def tenants_category
    @tenants_category ||= I18n.t('tenant_term').pluralize
  end

  def report_history_key
    @report_history_key ||= begin
      klass = user.class.name.parameterize.underscore
      "report_history/#{current_subdomain}/#{klass}/#{user.id}"
    end
  end

  def report_history
    @report_history ||= Redis.instance do |r|
      r.lrange(report_history_key, 0, HISTORY_SIZE - 1)
    end
  end

  def lending?
    defined?(@lending) ? @lending : @lending = Customer.current.lending?
  end

  def report_available_to_customer?(report)
    if lending? && report[:category].in?(
      [
        'Fee Management',
        'Leasing',
        'Maintenance',
        'Marketing',
        'Parking',
        tenants_category
      ]
    )
      return false
    end

    if report[:category] == 'Lending' && CustomerSpecific::Behavior.lending_loans_enabled?
      return true
    end

    if report.key?(:customer_whitelist)
      return report[:customer_whitelist].include?(current_subdomain)
    elsif report.key?(:customer_kind_whitelist)
      return report[:customer_kind_whitelist].include?(customer_kind)
    end

    true
  end

  def report_available_to_user?(report)
    return true unless user.is_a?(PropertyManager)

    !user.role.report_whitelist&.exclude?(report[:slug])
  end

  def link(slug)
    if @link_from_slug
      @link_from_slug[slug]
    else
      "/reports/#{slug}"
    end
  end

  def current_subdomain
    @current_subdomain ||= Customer.current_subdomain
  end

  def customer_kind
    @customer_kind ||= Customer.current.kind
  end

  def reports_queue_limit
    queue = Sidekiq::Queue['reports']

    return queue.limit if queue.limit

    process_limit = queue.process_limit || 0

    processes = Sidekiq::ProcessSet.new.count || 0

    processes * process_limit
  end
end
