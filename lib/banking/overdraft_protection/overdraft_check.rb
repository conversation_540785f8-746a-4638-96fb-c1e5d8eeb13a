class Banking::OverdraftProtection::OverdraftCheck
  def initialize(bank_account:, transaction_amount:)
    @bank_account = bank_account
    @transaction_amount = transaction_amount
  end

  def overdraft_warning?
    return false unless overdraft_protection_enabled?

    estimated_overdraft_amount.positive?
  end

  def estimated_available_balance
    return Money.zero unless overdraft_protection_enabled?

    @estimated_available_balance ||=
      Banking::OverdraftProtection::AvailableBalance.new(bank_account:).balance
  end

  def estimated_overdraft_amount
    return Money.zero unless overdraft_protection_enabled?

    (transaction_amount - estimated_available_balance).clamp(Money.zero..)
  end

  private

  attr_reader :bank_account, :transaction_amount

  def overdraft_protection_enabled?
    bank_account.tunisia_account?
  end
end
