require 'rails_helper'

require_relative 'shared'

RSpec.describe Importers::V3::TenantInvoices do
  it_behaves_like 'a v3 importer'

  describe '#import' do
    let(:file) { File.new('spec/fixtures/importing/tenant_invoices.xlsx') }

    let(:result) { described_class.call(file: file) }

    let!(:membership) { create(:lease_membership) }

    let(:tenant) { membership.tenant }

    let(:property) { membership.property }

    let!(:account) do
      create(:revenue_account,
             gl_code: 4100,
             tenant: property.company.chart_of_accounts)
    end

    before do
      membership.unit.update!(name: 'Unit 1')
      property.update!(name: '1234 Main Street')
      tenant.update!(first_name: '<PERSON>', last_name: '<PERSON>')
    end

    describe 'the result' do
      subject { result }

      it { is_expected.to be_successful }

      describe 'an invoice' do
        subject(:invoice) { result.invoices.first }

        its(:seller) { is_expected.to eq(property) }

        its(:buyer) { is_expected.to eq(tenant) }

        its(:buyer_lease_membership) { is_expected.to eq(membership) }

        its(:post_date) { is_expected.to eq(Date.new(2023, 2, 22)) }

        its(:due_date) { is_expected.to eq(Date.new(2023, 3, 1)) }

        its(:description) { is_expected.to eq('Utilities') }

        its(:invoice_number) { is_expected.to eq('123') }

        its(:note) { is_expected.to eq('A Note') }

        describe 'the line item' do
          subject(:line_item) { invoice.line_items.first }

          its(:description) { is_expected.to eq('Electric') }

          its(:amount) { is_expected.to eq(Monetize.parse('$324.00')) }

          its(:receivable_account) { is_expected.to eq(account) }
        end
      end
    end
  end
end
