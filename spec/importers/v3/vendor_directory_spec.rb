require 'rails_helper'

require_relative 'shared'

RSpec.describe Importers::V3::VendorDirectory do
  # it_behaves_like 'a v3 importer'

  it 'imports a vendor' do
    file = File.new(absolute_fixture('importing/vendors.xlsx'))

    result = described_class.call(file: file)

    expect(result).to be_successful

    vendor = Vendor.last!

    expect(vendor.tags.map(&:tag)).to eq(['Tag One', 'Tag Two'])
  end
end
