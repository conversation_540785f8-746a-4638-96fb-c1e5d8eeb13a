require_relative 'action_index_helpers'

module CapybaraHelper
  include ActionIndexHelpers

  def open_dropdown(selector, wait: true)
    find_all('.item', text: text, visible: :all) if wait
    dropdown = find('label', text: selector)
               .first(:xpath, './/..')
               .first('div.dropdown')

    dropdown.click

    dropdown
  end

  def select_dropdown(selector, text, wait: true)
    dropdown = open_dropdown(selector, wait: wait)
    dropdown.find('.menu.visible .item', text: text).click
  end

  def menu_dropdown(name, text, wait: true)
    find_all('.menu .dropdown .item', text: text, visible: :all) if wait
    dropdown = find('.menu .dropdown', text: name)
    dropdown.click
    dropdown.find('.menu.visible .item', text: text).click
  end

  def pick_date(selector, text)
    execute_script(%{$('input[name="#{selector}"]').val('#{text}')})
  end

  def fill_in_calendar(selector, with:)
    input = "$('#{selector}')"
    page.execute_script("#{input}.val('#{with}')")

    # Manually trigger an input event because React
    event = "new Event('input', { bubbles: true })"
    page.execute_script("#{input}[0].dispatchEvent(#{event})")
  end

  def within_action_sidebar(&)
    within('.action-sidebar', &)
  end

  def click_actions
    find('.dropdown.button', text: 'Actions').click
  end

  def within_actions_menu(&)
    within('.dropdown.button .menu.visible', &)
  end

  def open_actions_submenu(text)
    within_actions_menu do
      find('span.text', text: text).hover
    end
  end

  def click_action_item(text)
    within_actions_menu do
      find('a.item', text: text).click
    end
  end

  def toggle_checkbox(text)
    find('label', text: text).click
  end

  def check_checkbox(text)
    set_checkbox(text, true)
  end

  def uncheck_checkbox(text)
    set_checkbox(text, false)
  end

  def find_radio(text, value)
    label = find('label', text: text)
    id = label[:for] + "_" + value
    find("input##{id}", visible: nil)
  end

  def click_radio(text, value)
    input = find_radio(text, value)
    execute_script("$('##{input[:id]}').parent().checkbox('check');")
  end

  def find_checkbox(text)
    label = find('label', text: text)
    id = label[:for]
    find("input##{id}", visible: nil)
  end

  def set_checkbox(text, checked)
    input = find_checkbox(text)
    value = checked ? 'check' : 'uncheck'
    execute_script("$('##{input[:id]}').parent().checkbox('#{value}');")
  end

  def pick_search_dropdown(label:, text:)
    selector = 'div.results.transition.visible'
    find("input[placeholder='#{label}']").click
    expect(page).to have_selector(selector)
    find('a.result', text: text).click
    expect(page).to have_no_selector(selector)
  end

  # Attaches a file to a dropzone
  def drop(attachment_path, into:)
    page.execute_script("$('#{into} input').attr('id', 'dropzone_input');")
    attach_file('dropzone_input',
                absolute_fixture(attachment_path),
                visible: :any)
  end

  def chill(&)
    using_wait_time(15, &)
  end

  def wait_for_ajax
    Timeout.timeout(Capybara.default_max_wait_time) do
      loop until finished_all_ajax_requests?
    end
  end

  def finished_all_ajax_requests?
    page.evaluate_script('jQuery.active').zero?
  end

  def fill_in_address(
    line_one: '42 Wallaby Way', line_two: 'Apt 1.', city: 'Sydney',
    region: 'New South Wales', postal_code: '2127'
  )
    fill_in 'Line One', with: line_one
    fill_in 'Line Two', with: line_two
    fill_in 'City', with: city
    fill_in 'Zip Code', with: postal_code
    fill_in 'State', with: region
  end

  def fill_in_tags(name = 'Tags', with:)
    field = find(:xpath, ".//label[contains(., '#{name}')]")
            .first(:xpath, './/..')
    input = field.find_all('input', visible: false).first
    script = %{$('input[name="#{input['name']}"]').val('#{with}');}
    page.execute_script(script)
  end

  def open_new_tab
    browser = page.driver.browser
    windows = browser.window_handles
    browser.switch_to.window(windows.last)
  end

  def close_new_tab
    browser = page.driver.browser
    windows = browser.window_handles
    sleep 0.5
    browser.switch_to.window(windows.last)
    browser.close
    browser.switch_to.window(windows.first)
    sleep 0.5
  end

  def fill_in_signature(name = nil)
    signature = 'data:image/png;base64,1234'
    page.execute_script("$('input#signature').val('#{signature}');")
  end

  def close_dropdown
    find('body').send_keys :escape # Close multi select dropdown
  end

  # Use a semantic ui search input
  # search_for 'Ellen', in: 'Tenant'
  def search_for(name, **args)
    finder = args[:in]

    input = begin
      selector = %(input[placeholder="#{finder}"])
      find(selector)
    rescue Capybara::ElementNotFound
      label = find('label', text: finder)
      label.first(:xpath, '..').find('input', class: 'prompt')
    end

    input.set(name)
    find('.title', text: name).click
  end

  def fill_in_rich_text(name = nil, with:)
    find('trix-editor').click.set(with)
  end

  def within_modal(&)
    within('.ui.visible.modal', &)
  end

  def wait_for_modal_to_close
    expect(page).to have_no_css('.ui.visible.modal')
  end

  def fill_in_additions_dropdown(label, with:)
    input = find('label', text: label).first(:xpath, './/..').first('input')

    input.set(with)
  end

  def fill_in_two_factor(with:)
    digits = with.chars

    digits.each.with_index do |digit, index|
      find("input[name='two_factor_digit_#{index}']").set(digit)
    end
  end

  def select_tab(label)
    find('.item', text: label).click
  end
end
