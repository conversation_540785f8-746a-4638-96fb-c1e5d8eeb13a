require 'rails_helper'

RSpec.describe 'operations/activity_log', :js,
               capybara_login: :property_manager do
  let!(:open_ticket) { create(:maintenance_ticket) }
  let!(:tenant_payment) { create(:payment, :tenant_payment) }
  let!(:executed_lease) { create(:lease, executed_at: Time.zone.now) }
  let!(:guest_card) { create(:guest_card) }
  let!(:prepared_lease) { create(:upcoming_lease) }
  let!(:sent_application) { create(:lease_application, lead: create(:tenant)) }
  let!(:submitted_application) { create(:lease_application, :submitted) }
  let!(:submitted_application_no_unit) do
    create(:lease_application, :submitted).tap { |la| la.update!(unit: nil) }
  end
  let!(:tenant_comment) do
    create(:message, author: manager, chat_room: create(:tenant),
                     body: 'short body')
  end
  let!(:email_entry) do
    create(:contact_timeline_entry, kind: :email, body: 'small body')
  end
  let!(:seven_day_without_tenant) do
    create(:contact_timeline_entry, kind: :demand_for_possession,
                                    author: manager)
  end
  let!(:seven_day) do
    create(:contact_timeline_entry,
           kind: :demand_for_possession,
           unit: executed_lease.unit,
           author: manager,
           regarding: tenant_payment.payer)
  end
  let!(:notice) do
    create(:contact_timeline_entry,
           kind: :notice_to_quit,
           unit: executed_lease.unit,
           author: manager,
           regarding: tenant_payment.payer)
  end
  let!(:tour) { create(:tour, time: Time.zone.now) }

  before { visit operations_activity_log_path }

  it 'has a ticket opening' do
    expect(page).to have_content("#{open_ticket.opened_by.name} opened the " \
                                 "work order #{open_ticket.subject} for " \
                                 "#{open_ticket.property.name}")
  end

  it 'has a tenant payment' do
    expect(page).to have_content("#{tenant_payment.payer.name} submitted a " \
                                 "payment #{tenant_payment.description} of " \
                                 "#{tenant_payment.amount.format}")
  end

  it 'has an executed lease' do
    expect(page).to have_content(
      "Executed #{executed_lease.primary_tenant.name}'s lease for unit " \
      "#{executed_lease.unit.name} in #{executed_lease.property.name}"
    )
  end

  it 'has a guest card' do
    expect(page).to have_content("#{guest_card.tenant.name} submitted a " \
                                 'guest card')
  end

  it 'has a prepared lease' do
    expect(page).to have_content(
      "Prepared #{prepared_lease.primary_tenant.name}'s lease for unit " \
      "#{prepared_lease.unit.name} in #{prepared_lease.property.name}"
    )
  end

  it 'has a sent application' do
    expect(page).to have_content(
      "Sent an application to #{sent_application.lead.name} for unit " \
      "#{sent_application.unit.name} in #{sent_application.property.name}"
    )
  end

  it 'has a submitted application' do
    expect(page).to have_content(
      "#{submitted_application.primary_tenant.name} submitted an " \
      "application for unit #{submitted_application.unit.name} in " \
      "#{submitted_application.property.name}"
    )
  end

  it 'has a submitted application for no unit' do
    expect(page).to have_content(
      "#{submitted_application_no_unit.primary_tenant.name} submitted an " \
      "application for #{submitted_application_no_unit.property.name}"
    )
  end

  it 'has a tenant comment' do
    expect(page).to have_content(
      "#{manager.name} added a comment regarding " \
      "#{tenant_comment.chat_room.name}: short body"
    )
  end

  it 'has a tenant event' do
    expect(page).to have_content("#{email_entry.author.name} added a email " \
                                 "regarding #{email_entry.regarding.name}: " \
                                 'small body')
  end

  it 'has a tour' do
    expect(page).to have_content("#{tour.lead.name} took a tour of " \
                                 "#{tour.property.name} with " \
                                 "#{tour.tour_guide.name}")
  end

  it 'has a 7-day' do
    expect(page).to have_content(/demand for possession for [a-z0-9\s]+ in/i)
  end

  it 'has a notice to quit' do
    expect(page).to have_content(/notice to quit for [a-z0-9\s]+ in/i)
  end
end
