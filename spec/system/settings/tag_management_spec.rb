require 'rails_helper'

RSpec.describe 'tag management', :js, capybara_login: :property_manager do
  let!(:electrical) { create(:tag, tag: 'Electrical') }
  let!(:plumber) { create(:tag, tag: 'Plumber') }
  let!(:plumbing) { create(:tag, tag: 'Plumbing') }

  describe 'filtering and sorting' do
    scenario 'a user sees tags grouped by taggable' do
      plumber.update!(taggable_type: 'Vendor')
      create(:tagging, tag: plumber, taggable: create(:vendor))
      create(:tagging, tag: plumbing, taggable: create(:maintenance_ticket))

      visit settings_tags_path

      click_on 'Work Orders'
      expect(page).to have_no_content(plumber)
      expect(page).to have_content(plumbing)

      click_on 'Vendors'
      expect(page).to have_content(plumber)
      expect(page).to have_no_content(plumbing)
    end

    scenario 'a user searches for tags' do
      visit settings_tags_path

      expect(page).to have_content(electrical)
      expect(page).to have_content(plumbing)

      fill_in 'Search...', with: plumbing

      expect(page).to have_no_content(electrical)
      expect(page).to have_content(plumbing)
    end

    scenario 'a user sorts tags' do
      one = create(:tag, tag: 'alfa')
      two = create(:tag, tag: 'Beta')

      visit settings_tags_path

      sort_index('Tag', direction: :descending)
      expect(one).to appear_after(two)

      sort_index('Tag', direction: :ascending)
      expect(one).to appear_before(two)
    end
  end

  scenario 'a user imports a list of tags'

  scenario 'a user adds a tag' do
    visit settings_tags_path

    click_on 'Add Tag'

    within_modal do
      fill_in 'Tag', with: 'HVAC'

      click_on 'Save'
    end

    expect(page).to have_content('HVAC')
  end

  scenario 'a user removes a tag' do
    visit settings_tags_path

    row = find('tr', text: plumbing)

    within row do
      accept_confirm do
        click_on 'Remove'
      end
    end

    expect(page).to have_no_content(plumbing)
  end

  scenario 'a user edits a tag' do
    visit settings_tags_path

    row = find('tr', text: plumbing)

    within row do
      click_on 'Edit'
    end

    within_modal do
      fill_in 'Tag', with: 'Updated Value'

      click_on 'Update'
    end

    expect(page).to have_no_content(plumbing)
    plumbing.reload
    expect(page).to have_content(plumbing)
  end

  scenario 'a user exports a list of tags' do
    visit settings_tags_path

    click_on 'Export'

    wait_for_download

    download_path = downloads.last

    workbook = RubyXL::Parser.parse(download_path)

    cell = workbook[0].cell_at('B5')

    expect(cell.value).to eq(electrical.to_s)
  end

  describe 'batch actions' do
    before do
      visit settings_tags_path

      select_row(plumber)
      select_row(plumbing)
    end

    scenario 'a user removes multiple tags' do
      expect do
        accept_confirm do
          click_on_batch_action_button 'Remove'
        end

        expect(page).to have_no_content(plumber)
        expect(page).to have_no_content(plumbing)
      end.to change { Tag.count }.by(-2)
    end

    scenario 'a user merges tags' do
      expect do
        accept_confirm do
          click_on_batch_action_button 'Merge'
        end

        expect(page).to have_no_content(plumbing)
        expect(page).to have_content(plumber)
      end.to change { Tag.count }.by(-1)
    end
  end

  describe 'CustomerSpecific::Behavior.property_scoped_tags?' do
    before do
      allow(CustomerSpecific::Behavior).to receive(:property_scoped_tags?).and_return(true)
    end

    let!(:test_property) { create(:property, name: 'Test Property') }

    it 'shows property column for only agreements tab' do
      visit settings_tags_path(type: 'agreements_simple_agreements')

      expect(page).to have_content('Property')

      select_tab 'Companies'

      expect(page).to have_no_content('Property')
    end

    it 'allows adding a tag with a property' do
      visit settings_tags_path(type: 'agreements_simple_agreements')

      click_on 'Add Tag'

      within_modal do
        fill_in 'Tag', with: 'New Agreement Tag'
        select_dropdown('Property', test_property.name)

        click_on 'Save'
      end

      expect(page).to have_content('New Agreement Tag')
      expect(page).to have_content(test_property.name)
    end

    it 'displays an error when adding a tag with the same name and property' do
      create(:tag, tag: 'Existing Agreement Tag', property: test_property,
                   taggable_type: 'Agreements::SimpleAgreement')
      visit settings_tags_path(type: 'agreements_simple_agreements')

      click_on 'Add Tag'

      within_modal do
        fill_in 'Tag', with: 'Existing Agreement Tag'
        select_dropdown('Property', test_property.name)

        click_on 'Save'
      end

      expect(page).to have_content('Tag has already been taken')
    end

    it 'displays an error when trying to change the property of a used tag' do
      existing_tag = create(:tag, tag: 'Used Agreement Tag', property: test_property,
                                  taggable_type: 'Agreements::SimpleAgreement')
      create(:tagging, tag: existing_tag, taggable: create(:simple_agreement))
      other_property = create(:property, name: 'Another Property')

      visit settings_tags_path(type: 'agreements_simple_agreements')

      row = find('tr', text: existing_tag.tag)

      within row do
        click_on 'Edit'
      end

      within_modal do
        select_dropdown('Property', other_property.name)

        click_on 'Update'
      end

      expect(page).to have_content("The property on this tag cannot be edited because it's currently in use by one or more membership agreements")
    end

    it 'displays an error when trying to merge two tags with different properties' do
      existing_tag = create(:tag, tag: 'Used Agreement Tag', property: test_property,
                                  taggable_type: 'Agreements::SimpleAgreement')
      other_property = create(:property, name: 'Another Property')
      other_tag = create(:tag, tag: 'Other Agreement Tag', property: other_property,
                               taggable_type: 'Agreements::SimpleAgreement')

      visit settings_tags_path(type: 'agreements_simple_agreements')

      select_row(existing_tag)
      select_row(other_tag)

      accept_confirm do
        click_on_batch_action_button 'Merge'
      end

      expect(page).to have_content('The selected tags cannot be merged because they are associated with different properties')
      expect(page).to have_content(existing_tag.tag)
      expect(page).to have_content(other_tag.tag)
    end
  end
end
