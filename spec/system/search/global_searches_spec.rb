require 'rails_helper'

RSpec.describe 'global search', :elasticsearch, :js, capybara_login: :property_manager do
  def search(term)
    visit properties_path
    fill_in :search, with: term
  end

  describe 'tenant search' do
    it 'finds a tenant' do
      perform_enqueued_jobs do
        create(:tenant, first_name: '<PERSON><PERSON>', last_name: '<PERSON><PERSON><PERSON>')
        create(:tenant, first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON>')
      end

      Tenant.__elasticsearch__.refresh_index!

      search('Anakin')
      within('#search_results') do
        expect(page).to have_content 'Anakin Skywalker'
      end

      search('Skywalker')
      within('#search_results') do
        expect(page).to have_content 'Anakin Skywalker'
        expect(page).to have_content 'Luke Skywalker'
      end
    end
  end

  describe 'unit search' do
    it 'finds a unit' do
      unit = perform_enqueued_jobs do
        create(:unit, name: '1100-101')
      end

      Unit.__elasticsearch__.refresh_index!

      search('1100')

      within('#search_results') do
        expect(page).to have_content unit.name
      end
    end
  end

  describe 'property search' do
    it 'finds a property' do
      perform_enqueued_jobs do
        create(:property, name: '42 Wallaby Way')
      end

      Property.__elasticsearch__.refresh_index!

      search('Wallaby')

      within('#search_results') do
        expect(page).to have_content '42 Wallaby Way'
      end
    end
  end

  describe 'report search' do
    it 'finds a report' do
      perform_enqueued_jobs do
        create(:report, name: 'Dogs vs Cats')
      end

      Report.__elasticsearch__.refresh_index!

      search('Dogs vs Cats')

      within('#search_results') do
        expect(page).to have_content('Dogs vs Cats')
      end
    end
  end

  scenario 'a user searches for a vehicle' do
    res = perform_enqueued_jobs do
      create(:parking_reservation)
    end
    vehicle = res.vehicle

    Vehicle.__elasticsearch__.refresh_index!

    search(vehicle.license_plate)

    within('#search_results') do
      expect(page).to have_content(vehicle.make)

      href = parking_lot_parking_reservation_path(res.parking_lot, res)
      expect(page).to have_link(vehicle.to_s, href: href)
    end
  end

  scenario 'a user searches for a vehicle that is unowned' do
    vehicle = perform_enqueued_jobs do
      create(:vehicle)
    end

    expect(vehicle.owner).to be_nil

    Vehicle.__elasticsearch__.refresh_index!

    search(vehicle.license_plate)

    within('#search_results') do
      expect(page).to have_content(vehicle.make)
      expect(page).to have_link(vehicle.to_s, href: '#')
    end
  end

  scenario 'escapes search characters' do
    perform_enqueued_jobs do
      create(:property, name: 'Annoying && Property .\/ Name$')
    end

    Property.__elasticsearch__.refresh_index!

    search('Annoying && Property .\/ Name$')

    within('#search_results') do
      expect(page).to have_content 'Annoying && Property .\/ Name$'
    end
  end
end
