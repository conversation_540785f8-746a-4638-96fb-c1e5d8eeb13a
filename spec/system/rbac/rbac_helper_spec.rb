require 'rails_helper'

RSpec.describe 'rbac helper', :js,
               capybara_login: :property_manager do
  let!(:chart) { create(:chart_of_accounts) }
  let!(:account) { create(:revenue_account, tenant: chart, name: 'Rental Income') }

  describe 'require_permission' do
    scenario 'has permission' do
      with_permission manager, :export_organization_charts_of_accounts? do
        visit organization_chart_of_accounts_account_path(chart, account)

        expect(page).to have_content('Export')
      end
    end

    scenario 'does not have permission' do
      without_permission manager, :export_organization_charts_of_accounts? do
        visit organization_chart_of_accounts_account_path(chart, account)

        expect(page).to have_no_content('Export')
      end
    end
  end

  describe 'rbac enabled or if' do
    include RBACHelper

    describe 'rbac enabled' do
      before do
        allow(Feature).to receive(:enabled?).and_return(true)
      end

      after do
        allow(Feature).to receive(:enabled?).and_call_original
      end

      scenario 'returns true if true value passed in' do
        expect(rbac_enabled_or_if(true)).to be_truthy
      end

      scenario 'returns true if false value passed in' do
        expect(rbac_enabled_or_if(false)).to be_truthy
      end
    end

    describe 'rbac disabled' do
      before do
        allow(Feature).to receive(:enabled?).and_return(false)
      end

      after do
        allow(Feature).to receive(:enabled?).and_call_original
      end

      scenario 'returns true if true value passed in' do
        expect(rbac_enabled_or_if(true)).to be_truthy
      end

      scenario 'returns false if false value passed in' do
        expect(rbac_enabled_or_if(false)).to be_falsey
      end
    end
  end

  describe 'action menu permissions' do
    scenario 'has permission' do
      with_permission manager, :update_gl_accounts_in_organization_charts_of_accounts? do
        visit organization_chart_of_accounts_account_path(chart, account)

        click_actions
        within_actions_menu do
          expect(page).to have_css('.item', text: 'Edit')
          expect(page).to have_no_css('.item.disabled', text: 'Edit')
        end
      end
    end

    scenario 'does not have permission' do
      without_permission manager, :update_gl_accounts_in_organization_charts_of_accounts? do
        visit organization_chart_of_accounts_account_path(chart, account)

        click_actions
        within_actions_menu do
          expect(page).to have_css('.item.disabled', text: 'Edit')
        end
      end
    end
  end

  describe 'action index permissions' do
    before do
      create(:rent_invoice)
    end

    scenario 'has permission' do
      with_permission manager, :destroy_accounting_receivables?, :export_accounting_receivables? do
        visit accounting_receivables_path

        expect(page).to have_css '.with-selection', visible: :hidden
        expect(page).to have_css '.item', text: 'Delete', visible: :hidden
        expect(page).to have_css '.item', text: 'Download', visible: :hidden

        within('thead') do
          checkbox = page.find('.checkbox')
          checkbox.click
        end

        expect(page).to have_css '.with-selection'
        expect(page).to have_css '.item', text: 'Delete'
        expect(page).to have_css '.item', text: 'Download'
      end
    end

    scenario 'does not have permission' do
      without_permission manager, :destroy_accounting_receivables?,
                         :export_accounting_receivables? do
        visit accounting_receivables_path

        expect(page).to have_no_css '.with-selection', visible: :all
        expect(page).to have_no_css '.item', text: 'Delete', visible: :all
        expect(page).to have_no_css '.item', text: 'Download', visible: :all

        within('table') do
          expect(page).to have_no_css '.checkbox'
        end
      end
    end
  end

  describe 'action table permissions' do
    before do
      create(:custom_forms_form, published_at: Time.current)
    end

    scenario 'has permission' do
      with_permission manager, :send_invite_for_organization_forms? do
        visit organization_forms_path

        expect(page).to have_css 'td.center.aligned', text: 'Send Invite'
        expect(page).to have_no_css 'td.center.aligned.disabled', text: 'Send Invite'
      end
    end

    scenario 'does not have permission' do
      without_permission manager, :send_invite_for_organization_forms? do
        visit organization_forms_path

        expect(page).to have_css 'td.center.aligned.disabled', text: 'Send Invite'
      end
    end
  end
end
