require 'rails_helper'

require_relative '../../shared/payment_methods_context'

RSpec.describe 'portfolio vendors', :js,
               capybara_login: :property_manager do
  include_context 'payment methods'

  def fill_in_contact
    fill_in 'First Name', with: '<PERSON>'
    fill_in 'Last Name', with: '<PERSON><PERSON><PERSON><PERSON>'
    fill_in 'Email', with: '<EMAIL>'
    fill_in 'Phone', with: '************'
  end

  scenario 'a user creates a new vendor' do
    tag = create(:tag, taggable_type: 'Vendor')

    visit vendors_path

    click_on 'Add Vendor'

    fill_in 'Name', with: 'Pied Piper'
    select_dropdown(/\AType\Z/, 'Supplier')

    fill_in 'Website', with: 'http://www.piedpiper.com'
    fill_in 'Notes', with: 'this vendor is great to work with'

    fill_in_address \
      line_one: '44439 White Pine Circle East',
      line_two: 'Room 1A',
      city: 'Northville',
      postal_code: '48168',
      region: 'Michigan'

    fill_in_contact
    select_dropdown 'Tags', tag

    select_dropdown 'Preferred Payment Method', 'ACH'

    click_on 'Create Vendor'

    expect(page).to have_content 'Vendor Created Successfully'

    vendor = Vendor.unscoped.last!
    expect(vendor).to be_supplier
    expect(vendor.name).to eq('Pied Piper')
    expect(vendor.website).to eq('http://www.piedpiper.com')
    expect(vendor.notes).to eq('this vendor is great to work with')

    address = vendor.address
    expect(address.line_one).to eq('44439 White Pine Circle East')
    expect(address.line_two).to eq('Room 1A')
    expect(address.city).to eq('Northville')
    expect(address.postal_code).to eq('48168')
    expect(address.region).to eq('Michigan')

    contact = vendor.vendor_contacts.first
    expect(contact.first_name).to eq('John')
    expect(contact.last_name).to eq('DeSilva')
    expect(contact.email).to eq('<EMAIL>')
    expect(contact.phone).to eq('+12482482480')

    expect(vendor.tags).to contain_exactly(tag)

    expect(vendor.preferred_disbursement).to eq('prefers_electronic_disbursements')
  end

  context 'existing vendor' do
    let(:vendor) { create(:vendor) }

    scenario 'a user updates a vendor contact' do
      visit vendor_path(vendor)

      original_email = vendor.vendor_contacts.first.email

      click_actions
      click_action_item 'Edit'

      fill_in 'First Name', with: 'Ellen'
      fill_in 'Last Name', with: 'Ripley'

      click_on 'Update'

      contact = vendor.vendor_contacts.reload.first
      expect(contact.first_name).to eq('Ellen')
      expect(contact.last_name).to eq('Ripley')
      expect(contact.email).to eq(original_email)
    end

    scenario 'a user archives a vendor' do
      visit vendor_path(vendor)

      click_actions
      expect(page).not_to have_action_item('Unarchive')
      accept_confirm { click_action_item 'Archive' }

      expect(page).to have_content(/archived/i)

      expect(vendor.reload).to be_archived
    end

    scenario 'a user unarchives a vendor' do
      vendor.archive!

      visit vendor_path(vendor)

      click_actions
      expect(page).not_to have_action_item('Archive')
      click_action_item 'Unarchive'

      expect(page).to have_content(/unarchived/i)

      expect(vendor.reload).not_to be_archived
    end

    scenario 'a user sets request unconsolidated checks' do
      visit edit_vendor_path(vendor)

      check_checkbox('Unconsolidated')

      click_on 'Update'

      expect(page).to have_content(/updated/i)

      expect(vendor.reload).to be_unconsolidated_checks
    end

    describe 'adding a deposit account' do
      let!(:merchant_account) do
        create(:merchant_account, :zeamster, :ach, :ach_credit)
      end

      scenario 'a user adds an ach deposit account to a vendor', vcr: {
        cassette_name: 'zeamster/vendor/add_ach_successfully'
      } do
        visit vendor_path(vendor)

        first('a', text: 'Configure').click

        expect(page).to have_css('.visible.modal')

        add_bank_account

        account = vendor.payment_methods.last
        expect(account).to be_present
      end
    end
  end

  context 'a user comes with a pre-set merchant_name' do
    let(:prefilled_merchant_name) { Faker::JapaneseMedia::StudioGhibli.character }

    it 'prefills the vendor name field' do
      visit new_vendor_path(prefilled: { name: prefilled_merchant_name })

      expect(find_field('vendor[name]').value).to eq prefilled_merchant_name
    end
  end
end
