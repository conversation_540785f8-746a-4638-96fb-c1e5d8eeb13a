require 'rails_helper'

RSpec.describe 'single family floorplan setup', :js,
               capybara_login: :property_manager do
  let!(:property) { create(:property, kind: :singlefamily, setup: false) }

  scenario 'a user configures floorplan information for a singlefamily home' do
    bedrooms =  2
    bathrooms = 1.5
    square_feet = 1234

    visit property_setup_path(property)

    click_on 'Floorplan'

    fill_in 'Bedrooms', with: bedrooms
    fill_in 'Bathrooms', with: bathrooms
    fill_in 'Square Feet', with: square_feet

    click_on 'Next'

    floorplan = property.floorplans.first!
    expect(floorplan.bedrooms).to eq(bedrooms)
    expect(floorplan.bathrooms).to eq(bathrooms)
    expect(floorplan.square_feet).to eq(square_feet)
  end

  describe 'occupancy tab' do
    before do
      visit property_setup_path(property)
      click_on 'Occupancy'
    end

    scenario 'a user demarcates a unit as down' do
      select_dropdown 'Unit Status', 'Down'

      fill_in 'Note', with: 'Note'

      click_on 'Next'

      expect(page).to have_no_content('Downtime')

      downtime = property.units.first.downtimes.first!
      expect(downtime.start_date).to eq(Time.zone.today)
      expect(downtime.end_date).to be_nil
      expect(downtime.note).to eq('Note')
      expect(downtime).to be_down
    end

    scenario 'a user adds a current lease' do
      property.configuration.chart_of_accounts.update!(
        security_deposit_account: create(:liability_account)
      )

      start_date = 5.months.ago.to_date
      end_date = start_date + 1.year
      execution_date = start_date - 5.days
      move_in_date = start_date + 1.day
      held_deposit = Monetize.parse('$900.00')
      monthly_rent = Monetize.parse('$1134.57')
      first_name = 'Ellen'
      last_name = 'Ripley'
      email = '<EMAIL>'
      phone = '(*************'
      tag = 'Section 8'

      select_dropdown 'Unit Status', 'Occupied'

      fill_in 'Start Date', with: start_date
      fill_in 'End Date', with: end_date
      fill_in 'Execution Date', with: execution_date
      fill_in 'Move-In Date', with: move_in_date
      fill_in 'Held Deposit', with: held_deposit.format
      fill_in 'Monthly Rent', with: monthly_rent.format

      fill_in 'First Name', with: first_name
      fill_in 'Last Name', with: last_name
      fill_in 'Email', with: email
      fill_in 'Phone', with: phone

      fill_in_tags 'Tenant Tags', with: tag

      click_on 'Next'

      expect(page).to have_content('Finish')

      # Lease
      lease = Lease.last!
      expect(lease.start_date).to eq(start_date)
      expect(lease.end_date).to eq(end_date)
      expect(lease.executed_at.to_date).to eq(execution_date)
      expect(lease.move_in_date).to eq(move_in_date)
      expect(lease.amount).to eq(monthly_rent)
      expect(lease.held_security_deposit).to eq(held_deposit)

      # Primary Tenant
      tenant = lease.tenants.first
      expect(tenant.first_name).to eq(first_name)
      expect(tenant.last_name).to eq(last_name)
      expect(tenant.email).to eq(email)
      expect(tenant.formatted_phone).to eq(phone)
      expect(tenant.tags.pluck(:tag)).to eq(['Section 8'])
    end
  end
end
