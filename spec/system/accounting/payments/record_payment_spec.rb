require 'rails_helper'

RSpec.describe 'recording payments', :elasticsearch, :js,
               capybara_login: :property_manager do
  scenario 'a user records a tenant payment' do
    payer, payee = nil

    perform_enqueued_jobs do
      payer = create(:resident)
      payee = payer.current_property
    end

    lease_membership = payer.current_lease_membership
    unit = lease_membership.lease.unit
    company = payee.company

    ledger_account = create(:asset_account, tenant: company.chart_of_accounts)
    bank_account = create(:bank_account, owner: payee.company,
                                         ledger_account: ledger_account)

    Tenant.__elasticsearch__.refresh_index!
    Property.__elasticsearch__.refresh_index!

    visit new_accounting_payment_path

    search_for payer.name, in: 'Payer'
    search_for payee.name, in: 'Payee'
    wait_for_ajax
    select_dropdown 'Lease', lease_membership.lease.to_s
    wait_for_ajax
    select_dropdown 'Unit', unit.name
    select_dropdown 'Deposit Account', bank_account.name

    fill_in 'Date', with: (date = 3.days.ago.to_date)
    fill_in 'Amount', with: (amount = Monetize.parse('$120.25')).format

    fill_in 'Memo', with: (description = 'My Memo')

    select_dropdown 'Payment Method', 'Check'
    fill_in 'Check Number', with: (check_number = '1234')

    fill_in 'Notes', with: (note = 'My Notes')

    click_on 'Save'

    expect(page).to have_content(/created successfully/i)

    payment = Payment.last!
    expect(payment.payer).to eq(payer)
    expect(payment.payee).to eq(payee)
    expect(payment.date).to eq(date)
    expect(payment.description).to eq(description)
    expect(payment.amount).to eq(amount)
    expect(payment).to be_check
    expect(payment.check_number).to eq(check_number)
    expect(payment.note).to eq(note)
    expect(payment.credit_bank_account).to eq(bank_account)

    entry = payment.accrual_journal_entries.last!
    expect(entry.journal).to eq(payee.company)
    expect(entry.property).to eq(payee)
    expect(entry.unit).to eq(unit)
    expect(entry.tenant).to eq(payer)
    expect(entry.lease_membership).to eq(lease_membership)
    expect(entry.date).to eq(date)
    expect(entry.description).to eq(description)

    chart = payee.company.chart_of_accounts

    debit = entry.debit_amounts.first!
    expect(debit.account).to eq(ledger_account)
    expect(debit.amount).to eq(amount.cents)

    credit = entry.credit_amounts.first!
    expect(credit.account).to eq(chart.prepaid_revenue_account)
    expect(credit.amount).to eq(amount.cents)
  end

  scenario 'a user records a payment to a vendor' do
    property, vendor = nil

    perform_enqueued_jobs do
      property = create(:property)
      vendor = create(:vendor)
    end

    company = property.company
    unit = create(:unit, property: property)

    ledger_account = create(:asset_account, tenant: company.chart_of_accounts)
    bank_account = create(:bank_account, owner: company,
                                         ledger_account: ledger_account)

    Property.__elasticsearch__.refresh_index!
    Vendor.__elasticsearch__.refresh_index!

    visit new_accounting_payment_path

    search_for property.name, in: 'Payer'
    search_for vendor.name, in: 'Payee'
    wait_for_ajax
    select_dropdown 'Withdrawal Account', bank_account.name
    select_dropdown 'Unit', unit.name

    fill_in 'Date', with: Time.zone.today.to_date
    fill_in 'Memo', with: 'Vendor payment'
    fill_in 'Amount', with: '$120.25'

    click_on 'Save'

    expect(page).to have_content(/created successfully/i)

    payment = Payment.last!
    expect(payment.payer).to eq(property)
    expect(payment.payee).to eq(vendor)
    expect(payment.debit_bank_account).to eq(bank_account)

    entry = payment.accrual_journal_entries.last!
    expect(entry.journal).to eq(company)
    expect(entry.property).to eq(property)
    expect(entry.unit).to eq(unit)

    chart = company.chart_of_accounts

    debit = entry.debit_amounts.first!
    expect(debit.account).to eq(chart.accounts_payable)

    credit = entry.credit_amounts.first!
    expect(credit.account).to eq(ledger_account)
  end

  scenario 'quick tenant payment entry' do
    tenant = perform_enqueued_jobs { create(:resident) }
    membership = tenant.lease_memberships.last
    unit = membership.unit
    property = unit.property
    company = property.company
    rent_invoice = create(:rent_invoice, membership: membership)
    deposit_account = create(:bank_account, owner: company)

    visit tenant_path(tenant)

    click_on 'Record Payment'

    fill_in 'Amount', with: '$100.00'

    click_on 'Save'

    expect(page).to have_content(/successful/i)

    payment = Payment.last!
    expect(payment.payer).to eq(tenant)
    expect(payment.payee).to eq(property)
    expect(payment.payee_unit).to eq(unit)
    expect(payment.payer_lease_membership).to eq(membership)
    expect(payment.amount.format).to eq('$100.00')
    expect(payment.deposit_bank_account).to eq(deposit_account)
    apply = payment.invoice_payments.last
    expect(apply.invoice).to eq(rent_invoice)
    expect(apply.amount).to eq(payment.amount)
  end
end
