require 'rails_helper'

RSpec.describe 'recording owner contributions',
               :js, capybara_login: :property_manager do
  scenario 'a user records an owner contribution' do
    deposit_bank_account = \
      create(:bank_account, owner: Customer.current.client_entity)

    property = create(:property)

    company = property.company

    visit manage_company_path(company)

    click_actions
    click_action_item 'Record Contribution'

    date = Time.zone.today
    description = 'Repairs Contribution'
    amount = Money.new(1_000_00)
    check_number = '1234'

    within_modal do
      select_dropdown 'Property', property.name

      # Invoice
      fill_in 'Date', with: date
      fill_in 'Description', with: description
      fill_in 'Amount', with: amount.format

      # Payment
      check_checkbox 'Mark Contribution as Paid'
      select_dropdown 'Payment Method', 'Check'
      # fill_in 'Check Number', with: check_number
      fill_in 'Reference Number', with: check_number
      select_dropdown 'Deposit Bank Account', deposit_bank_account.name

      click_on 'Submit'
    end

    expect(page).to have_content(/successful/i)

    invoice = Invoice.last!
    expect(invoice).to have_attributes(
      date: date,
      description: description,
      amount: amount
    )

    payment = invoice.payments.first!
    expect(payment).to have_attributes(
      date: date,
      description: description,
      amount: amount,
      kind: 'check',
      check_number: check_number,
      deposit_bank_account: deposit_bank_account
    )
  end
end
