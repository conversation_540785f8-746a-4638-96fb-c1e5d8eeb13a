require 'rails_helper'

RSpec.describe 'payables action index', :elasticsearch, :js,
               capybara_login: :property_manager do
  let!(:due) do
    perform_enqueued_jobs do
      create(
        :invoice,
        :due,
        invoice_payment_approved_at: nil,
        payment_batch: build(:payment_batch, name: 'Due Invoices')
      )
    end
  end

  let!(:overdue) do
    create(:invoice, :overdue, invoice_payment_approved_at: nil)
  end

  let!(:paid) do
    create(:invoice, :paid, physical_date: 1.year.ago)
  end

  let!(:waived) { create(:invoice, :waived, invoice_payment_approved_at: nil) }

  let!(:group) { [overdue, paid, waived] }

  let(:company) { create(:company) }
  let(:property) { create(:property, company: company) }
  let(:other_property) { create(:property) }

  before do
    rule = create(:approvals_rule, action: :invoice_payment,
                                   approver_sources: [manager])

    create(:approvals_approval, approver: manager,
                                approvable: paid,
                                rule: rule)

    visit accounting_payables_path
  end

  scenario 'user sees only payable invoices in the payables index' do
    payable_invoice = create(:invoice, buyer: property, seller: other_property,
                                       description: 'Payable Invoice')
    receivable_invoice = create(:invoice, buyer: other_property, seller: property,
                                          description: 'Receivable Invoice')

    manager.update!(top_level: false)
    create(:property_membership, user: manager, target: company)

    visit accounting_payables_path
    expect(page).to have_content(payable_invoice.description)
    expect(page).to have_no_content(receivable_invoice.description)
  end

  describe 'filtering' do
    scenario 'a user searches by invoice number' do
      fill_in 'Search...', with: due.invoice_number

      group.each { |i| expect(page).to have_no_content(i.description) }
      expect(page).to have_content(due.description)
    end

    describe 'status' do
      scenario 'a user filters by open' do
        filter_index 'Status', with: 'Open'

        expect(page).to have_no_content(paid.description)
        expect(page).to have_no_content(waived.description)

        expect(page).to have_content(due.description)
        expect(page).to have_content(overdue.description)
      end

      scenario 'a user filters by due' do
        filter_index 'Status', with: 'Due'

        expect(page).to have_no_content(waived.description)
        expect(page).to have_no_content(overdue.description)
        expect(page).to have_no_content(paid.description)

        expect(page).to have_content(due.description)
      end
    end

    describe 'approval' do
      scenario 'a user filters by approval status' do
        filter_index 'Approval', with: /\AApproved\Z/

        expect(page).to have_no_content(due.description)
        expect(page).to have_no_content(overdue.description)
        expect(page).to have_no_content(waived.description)

        expect(page).to have_content(paid.description)
      end

      scenario 'a user filters by invoices that need their approval' do
        filter_index 'Approval', with: 'Needs My Approval'

        expect(page).to have_no_content(paid.description)
        expect(page).to have_content(due.description)
      end

      scenario 'a user filters by invoices they approved' do
        filter_index 'Approval', with: 'Approved By Me'

        expect(page).to have_no_content(due.description)
        expect(page).to have_content(paid.description)
      end
    end

    scenario 'a user filters by batch' do
      filter_index 'Batch', with: due.payment_batch.name

      group.each { |i| expect(page).to have_no_content(i.description) }
      expect(page).to have_content(due.description)
    end

    scenario 'a user filters by buyer' do
      filter_index_search 'Buyer', with: due.buyer.name

      group.each { |i| expect(page).to have_no_content(i.description) }
      expect(page).to have_content(due.description)
    end

    scenario 'a user filters by seller' do
      filter_index_search 'Seller', with: due.seller.name

      group.each { |i| expect(page).to have_no_content(i.description) }
      expect(page).to have_content(due.description)
    end

    scenario 'a user filters by date' do
      fill_in 'From', with: 1.month.ago.to_s
      fill_in 'Until', with: 1.year.from_now.to_s
      # Fill in Start again so End filter triggers
      fill_in 'From', with: 1.month.ago.to_s

      expect(page).to have_no_content(paid.description)

      expect(page).to have_content(due.description)
      expect(page).to have_content(overdue.description)
      expect(page).to have_content(waived.description)
    end
  end

  describe 'sorting' do
    scenario 'a user sorts by date' do
      sort_index 'Date'

      invoices = Invoice.all.sort_by(&:physical_date).map(&:description)

      expect(invoices.first).to appear_before(invoices.last)
    end

    scenario 'a user sorts by invoice number' do
      sort_index 'Invoice #'

      invoices = Invoice.all.sort_by(&:invoice_number).map(&:description)

      expect(invoices.first).to appear_before(invoices.last)
    end

    scenario 'a user sorts by description' do
      sort_index 'Description'

      invoices = Invoice.all.sort_by(&:description).map(&:description)

      expect(invoices.first).to appear_before(invoices.last)
    end

    scenario 'a user sorts by amount' do
      sort_index 'Amount'

      invoices = Invoice.all.sort_by(&:amount).map(&:description)

      expect(invoices.first).to appear_before(invoices.last)
    end
  end

  describe 'batch actions' do
    scenario 'a user batch approves' do
      create(:approvals_rule, approver_sources: [manager])

      select_all_rows

      click_on_batch_action_button 'Approve'

      expect(page).to have_content(
        "2 Invoices Approved (1 Already Approved, 1 Can't Be Approved)"
      )
    end

    scenario 'a user batch deletes' do
      select_row overdue.description
      select_row waived.description

      accept_confirm { click_on_batch_action_button 'Delete' }

      expect(page).to have_content('2 Invoices Removed Successfully')
    end

    scenario 'a user batch adds to batch' do
      select_all_rows

      filter_index 'Add to Batch', with: due.payment_batch.name

      expect(page).to have_content(
        "4 Invoices Successfully Added to Batch '#{due.payment_batch.name}'"
      )
    end

    scenario 'a user can add to batch that contain non-permitted invoices' do
      manager.update!(top_level: false)

      property = create(:property)
      create(:property_membership, target: property, user: manager)
      create(:invoice, buyer: property)

      visit accounting_payables_path

      select_all_rows

      filter_index 'Add to Batch', with: due.payment_batch.name

      expect(page).to have_content(
        "1 Invoice Successfully Added to Batch '#{due.payment_batch.name}'"
      )
    end
  end
end
