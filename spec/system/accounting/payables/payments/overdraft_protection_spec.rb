require 'rails_helper'

RSpec.describe 'payable payments overdraft protection', :elasticsearch, :js, capybara_login: :property_manager do # rubocop:disable Layout/LineLength
  let(:invoice) { create(:invoice, amount: '$123.45') }

  before do
    allow_any_instance_of( # rubocop:disable RSpec/AnyInstance
      Accounting::Payables::MakePayment::OverdraftWarningComponent
    ).to receive(:overdraft_check).and_return(overdraft_check)

    visit new_accounting_payables_payment_path(invoice_id: invoice.id)
  end

  context 'when overdraft is not likely' do
    let(:overdraft_check) do
      instance_double(
        Banking::OverdraftProtection::OverdraftCheck,
        overdraft_warning?: false
      )
    end

    scenario 'a is not prompted with an overdraft warning' do
      expect(page).to have_content('Pay $123.45')
      expect(page).to have_no_content(/overdraft warning/i)

      click_button 'Pay $123.45'
      expect(page).to have_content(/successful/i)
    end
  end

  context 'when overdraft is possible' do
    let(:overdraft_check) do
      instance_double(
        Banking::OverdraftProtection::OverdraftCheck,
        overdraft_warning?: true,
        estimated_available_balance: Monetize.parse('$100.00'),
        estimated_overdraft_amount: Monetize.parse('$23.45')
      )
    end

    scenario 'a user is prompted to acknowledge the overdraft' do
      expect(page).to have_content('Pay $123.45')
      expect(page).to have_content(/overdraft warning/i)
      expect(page).to have_content(/payment may exceed your account/i)

      click_button 'Pay $123.45'
      expect(page).to have_content(/overdraft acknowledgement cannot be blank/i)

      acknowledgement = 'Funds were recently deposited'
      fill_in 'Overdraft Acknowledgement', with: acknowledgement

      click_button 'Pay $123.45'
      expect(page).to have_content(/successful/i)

      # Payment creation audit
      audit = Audited::CustomAudit.where(auditable_type: 'Payment', action: 'create').sole
      expect(audit.comment).to eq("Overdraft Acknowledgement: #{acknowledgement}")
    end
  end
end
