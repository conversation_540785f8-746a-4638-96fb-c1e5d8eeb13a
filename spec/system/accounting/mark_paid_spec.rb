require 'rails_helper'

RSpec.describe 'marking invoices as paid', :js,
               capybara_login: :property_manager do
  let(:invoice) { create(:invoice, amount: '$300.00') }

  scenario 'a user cannot mark a paid invoice as paid' do
    # Partially Paid
    create(:invoice_payment, invoice: invoice, amount: '$50.00')
    visit accounting_payables_invoice_path(invoice)
    click_actions
    expect(page).to have_action_item('Mark Paid', disabled: false)

    # Fully Paid
    create(:invoice_payment, invoice: invoice, amount: '$250.00')
    visit accounting_payables_invoice_path(invoice)
    click_actions
    expect(page).to have_action_item('Mark Paid', disabled: true)
  end

  def mark_paid(amount: nil, with: nil)
    if invoice.payable?
      visit accounting_payables_invoice_path(invoice)
    else
      visit accounting_receivables_invoice_path(invoice)
    end

    click_actions
    click_action_item 'Mark Paid'

    select_dropdown 'Payment Method', with if with

    fill_in 'Amount', with: amount.format if amount

    expect(page).to have_css('.modal.visible.active')
    click_button 'Save'

    expect(page).to have_content(/payment recorded successfully/i)

    payment = invoice.payments.last!
    expect(payment.amount).to eq(amount || invoice.amount)
    expect(payment.payer).to eq(invoice.buyer)
  end

  scenario 'a user marks an unpaid invoice as paid' do
    mark_paid
  end

  context 'when unapproved' do
    before { invoice.update!(invoice_payment_approved_at: nil) }

    context 'with an ACH payment' do
      scenario 'the invoice gets approved' do
        mark_paid with: 'ACH'

        expect(invoice.reload).to be_invoice_payment_approved
      end
    end

    context 'with a check payment' do
      scenario 'the invoice cannot be marked paid' do
        visit accounting_payables_invoice_path(invoice)

        click_actions
        click_action_item 'Mark Paid'

        within_modal do
          select_dropdown 'Payment Method', 'Check'
          click_button 'Save'
        end

        expect(page).to have_content(/cannot be marked paid/i)

        expect(invoice.reload).not_to be_paid
        expect(invoice.reload).not_to be_invoice_payment_approved
      end
    end
  end

  context 'a vendor receivable' do
    let(:invoice) do
      create(:invoice, buyer: create(:vendor), seller: create(:property))
    end

    scenario 'a user marks an unpaid vendor receivable as paid' do
      mark_paid
    end

    scenario 'a user makes a partial vendor payment' do
      mark_paid(amount: Monetize.parse('$50.00'))
    end
  end

  context 'a tenant receivable' do
    let(:invoice) { create(:rent_invoice) }

    scenario 'a user marks an unpaid tenant receivable as paid' do
      mark_paid
    end

    scenario 'a user marks paid with deposit balance' do
      mark_paid with: 'Deposit Balance'

      security_deposit_account = invoice
                                 .buyer_lease_membership
                                 .configuration
                                 .chart_of_accounts
                                 .security_deposit_account

      payment = invoice.payments.last!
      expect(payment).to be_credit_note
      expect(payment.journal_entries.first.debit_amounts.first.account).to \
        eq(security_deposit_account)
    end
  end

  context 'management fees' do
    let(:invoice) do
      client_entity = Customer.current.client_entity

      create(:management_fee_invoice,
             seller: client_entity,
             buyer: create(:property))
    end

    scenario 'a user marks paid via owner balance' do
      mark_paid with: 'Owner Balance'

      invoice.reload
      expect(invoice).to be_paid

      payment = invoice.payments.last!

      due_from = payment.payer
                        .accounting_context
                        .chart_of_accounts
                        .due_from_client_entity_account

      due_to = payment.payee
                      .accounting_context
                      .chart_of_accounts
                      .due_to_customer_account

      expect(payment).to be_credit_note
      expect(payment.payable_credit_note_account).to eq(due_from)
      expect(payment.receivable_credit_note_account).to eq(due_to)
    end
  end
end
