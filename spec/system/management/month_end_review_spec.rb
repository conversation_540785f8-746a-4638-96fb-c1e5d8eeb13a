require 'rails_helper'

RSpec.describe 'managed entity month end review', :js,
               capybara_login: :property_manager do
  around { |example| travel_to(Date.new(2020, 7, 5)) { example.run } }

  let!(:client_entity) { Customer.current.client_entity }

  let!(:client_entity_bank_account) do
    create(:bank_account, owner: client_entity).tap do |bank_account|
      PortfoliosBankAccount.create!(portfolio: property_one.portfolio,
                                    bank_account: bank_account)
    end
  end
  let!(:merchant_account) do
    create(:merchant_account,
           :ach_credit,
           :zeamster,
           bank_account: client_entity_bank_account)
  end
  let!(:merchant_account_contact) do
    create(:merchant_account_contact,
           :zeamster,
           merchant_account: merchant_account,
           account_holder: client_entity)
  end

  let(:client_entity_chart) { client_entity.chart_of_accounts }

  let!(:batch) { create(:payment_batch) }

  let!(:entity) do
    create(:company, customer_managed: true).tap do |c|
      c.update!(chart_of_accounts: create(:chart_of_accounts))
    end
  end

  let(:managed_entity_chart) { entity.chart_of_accounts }

  let!(:property_one) { create(:property, company: entity) }

  let!(:property_two) { create(:property, company: entity) }

  # Property one has a $500.00 expense but no income
  let!(:expense) do
    create(:invoice, seller: client_entity,
                     buyer: property_one,
                     amount: '$500.00',
                     post_date: Date.new(2020, 6, 10))
  end

  # Property two has $750.00 in rent income but no expenses
  let!(:rent) do
    unit = create(:unit, property: property_two)
    membership = create(:lease_membership, unit: unit)
    create(:rent_invoice, :paid, membership: membership,
                                 amount: '$750.00',
                                 post_date: Date.new(2020, 6, 1))
  end

  before do
    rent.payments.first.update!(credit_bank_account: client_entity_bank_account)

    visit manage_company_path(entity)

    click_actions
    click_action_item 'Close Period'

    expect(page).to have_no_content('Loading')

    # Expect a $500.00 transfer to cover expenses
    within('#property-transfers') do
      expect(page).to have_content('$500.00')
    end

    # Expect a $250.00 disbursement
    expect(page).to have_content('$250.00')

    select_dropdown 'Payables Batch', batch.name

    click_on 'Submit'

    expect(page).to have_content(/period closed successfully/i)
  end

  scenario 'a user processes a month end review' do
    # Locked Journal
    entity.reload
    expect(entity.locked_at.to_date).to eq(Date.new(2020, 6, 30))

    # Paid Expense
    expect(expense).to be_paid
    expect(expense.payments.last.date).to eq(Date.new(2020, 6, 30))

    # Property Transfer Out
    transfer_out = entity.journal_entries.find_by!(
      description: "Transfer to #{property_one.name}"
    )
    expect(transfer_out.property).to eq(property_two)
    debit = transfer_out.debit_amounts.first
    expect(debit.amount).to eq(500_00)
    expect(debit.account).to eq(managed_entity_chart.property_transfers_account)
    credit = transfer_out.credit_amounts.first
    expect(credit.account).to eq(managed_entity_chart.due_from_client_entity_account)

    # Property Transfer In
    transfer_in = entity.journal_entries.find_by!(
      description: "Transfer from #{property_two.name}"
    )
    expect(transfer_in.property).to eq(property_one)
    debit = transfer_in.debit_amounts.first
    expect(debit.amount).to eq(500_00)
    expect(debit.account).to \
      eq(managed_entity_chart.due_from_client_entity_account)
    credit = transfer_in.credit_amounts.first
    expect(credit.account).to \
      eq(managed_entity_chart.property_transfers_account)

    expect(transfer_out.linked_entries).to eq([transfer_in])
    expect(transfer_in.linked_entries).to eq([transfer_out])

    # Disbursement
    invoice = Invoice.last!
    expect(invoice.seller).to eq(property_two)
    expect(invoice.buyer).to eq(client_entity)
    expect(invoice.amount).to eq(Monetize.parse('$250.00'))
    expect(invoice.date).to eq(Date.new(2020, 6, 30))
    item = invoice.line_items.first
    expect(item.receivable_account).to \
      eq(managed_entity_chart.due_from_client_entity_account)
    expect(item.payable_account).to \
      eq(client_entity.chart_of_accounts.due_to_customer_account)
  end

  scenario 'disbursement entries without deposit account' do
    invoice = Invoice.where(seller: property_two, buyer: client_entity).last!

    expect(invoice.journal_entries.count).to eq 2
    entry = invoice.journal_entries.find_by!(journal: client_entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        client_entity_chart.due_to_customer_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        client_entity_chart.accounts_payable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )
    entry = invoice.journal_entries.find_by!(journal: entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        managed_entity_chart.accounts_receivable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        managed_entity_chart.due_from_client_entity_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )

    visit accounting_payables_invoice_path(invoice)

    click_actions
    click_action_item 'Pay'

    fill_in 'Memo', with: 'Pay Owner Disbursement'

    click_on 'Pay $250.00'
    wait_for_ajax

    expect(page).to have_content(/payment successfully created/i)

    payment = Payment.last!
    expect(payment.accrual_journal_entries.count).to eq 2
    entry = payment.accrual_journal_entries.find_by!(journal: client_entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        client_entity_chart.accounts_payable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        client_entity_bank_account.ledger_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )
    entry = payment.accrual_journal_entries.find_by!(journal: entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        managed_entity_chart.owner_cash_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        managed_entity_chart.accounts_receivable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )
  end

  scenario 'disbursement entries with deposit account', :vcr do
    ba = create(:bank_account, :with_zeamster, owner: entity)
    create(:merchant_account_contact,
           :zeamster,
           merchant_account: merchant_account,
           account_holder: entity)

    invoice = Invoice.where(seller: property_two, buyer: client_entity).last!

    expect(invoice.journal_entries.count).to eq 2
    entry = invoice.journal_entries.find_by!(journal: client_entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        client_entity_chart.due_to_customer_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        client_entity_chart.accounts_payable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )
    entry = invoice.journal_entries.find_by!(journal: entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        managed_entity_chart.accounts_receivable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        managed_entity_chart.due_from_client_entity_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )

    visit accounting_payables_invoice_path(invoice)

    click_actions
    click_action_item 'Pay'

    select_dropdown 'Payment Method', 'ACH'

    click_on 'Pay $250.00'
    wait_for_ajax

    expect(page).to have_content(/payment successfully created/i)

    payment = Payment.last!
    expect(payment.kind).to eq 'ach'
    expect(payment.accrual_journal_entries.count).to eq 2
    entry = payment.accrual_journal_entries.find_by!(journal: client_entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        client_entity_chart.accounts_payable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        client_entity_bank_account.ledger_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )
    entry = payment.accrual_journal_entries.find_by!(journal: entity)
    expect(entry.amounts.pluck(:account_id, :amount, :type)).to contain_exactly(
      [
        managed_entity_chart.owner_cash_account.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::DebitAmount'
      ], [
        managed_entity_chart.accounts_receivable.id,
        BigDecimal(invoice.amount_cents),
        'Plutus::CreditAmount'
      ]
    )
  end
end
