require 'rails_helper'

RSpec.describe 'onboarding copy', :js, capybara_login: :property_manager do
  let!(:onboarding) do
    onboarding = create(:member_onboarding_configuration, :information_collection, :charge,
                        message: 'message')

    onboarding.information_collection.update!(
      nickname: true,
      additional_questions: [{ 'name' => 'question 1', 'type' => 'text' }]
    )

    onboarding
  end

  it 'starts a new onboarding setup with copied information' do
    visit onboardings_path

    click_on 'Copy'

    complete_onboarding_setup_flow

    expect(MemberOnboarding::Configuration.count).to eq(2)

    copied_onboarding = MemberOnboarding::Configuration.last

    expect(copied_onboarding.onboardable_properties).to match_array(onboarding.onboardable_properties)

    expect(copied_onboarding.name).to eq("Copy of #{onboarding.name}")
    expect(copied_onboarding.message).to eq(onboarding.message)

    expect(copied_onboarding.information_collection.nickname).to be_truthy
    expect(copied_onboarding.information_collection.address).to be_falsey
    expect(copied_onboarding.information_collection.additional_questions).to \
      eq([{ 'name' => 'question 1', 'required' => nil, 'type' => 'text' }])

    expect(onboarding.guarantor).to be_nil
    expect(copied_onboarding.guarantor).not_to be_nil
    expect(copied_onboarding.guarantor.address).not_to be_truthy

    charge_preset = onboarding.charge.charge_memberships.first.charge_preset
    copied_charge_preset = copied_onboarding.charge.charge_memberships.first.charge_preset
    expect(charge_preset).to eq(copied_charge_preset)
    expect(copied_onboarding.charge.charge_memberships.count).to eq(1)
  end

  it 'only creates property memberships that the pm has access to' do
    manager.update!(top_level: false)
    manager.property_memberships.find_each(&:destroy!)

    scoped_property = onboarding.onboardable_properties.first
    unscoped_property = onboarding.onboardable_properties.second

    manager.property_memberships.create!(target: scoped_property)

    visit onboardings_path

    click_on 'Copy'

    complete_onboarding_setup_flow

    expect(MemberOnboarding::Configuration.count).to eq(2)

    copied_onboarding = MemberOnboarding::Configuration.last

    expect(copied_onboarding.onboardable_properties).to eq([scoped_property])
  end

  context 'when onboarding_enhancements feature is enabled' do
    before { enable_feature_flag(:onboarding_enhancements) }

    it 'shows a warning banner when properties were filtered during copy' do
      manager.update!(top_level: false)
      manager.property_memberships.find_each(&:destroy!)

      scoped_property = onboarding.onboardable_properties.first
      unscoped_property = onboarding.onboardable_properties.second

      manager.property_memberships.create!(target: scoped_property)

      visit onboardings_path

      click_on 'Copy'

      # Should see the yellow warning banner
      expect(page).to have_css('.ui.yellow.message')
      expect(page).to have_content('Only the properties you have access to were copied for this onboarding. Any others were skipped.')

      complete_onboarding_setup_flow

      expect(MemberOnboarding::Configuration.count).to eq(2)

      copied_onboarding = MemberOnboarding::Configuration.last

      expect(copied_onboarding.onboardable_properties).to eq([scoped_property])
    end

    it 'does not show a warning banner when all properties were accessible' do
      visit onboardings_path

      click_on 'Copy'

      # Should not see the yellow warning banner
      expect(page).to have_no_css('.ui.yellow.message')
      expect(page).to have_no_content('Only the properties you have access to were copied for this onboarding')

      complete_onboarding_setup_flow
    end
  end

  def complete_onboarding_setup_flow
    expect(page).to have_content('New Onboarding Module')
    expect(MemberOnboarding::Configuration.count).to eq(1)

    expect(page).to have_field('Onboarding Form Name', with: "Copy of #{onboarding.name}")
    expect(page).to have_field('Message', with: onboarding.message)

    click_on 'Next'

    expect(page).to have_css('.module-tile.selected', count: 2)
    expect(page).to have_css('.module-tile.selected', text: 'Information Collection')
    expect(page).to have_css('.module-tile.selected', text: 'Charge')

    find('.module-tile', text: 'Guarantor').click

    click_on 'Next'

    nickname_checkbox = find_checkbox('Nickname')
    expect(nickname_checkbox).to be_checked

    click_on 'Next'

    check_checkbox 'Address'

    click_on 'Next'

    expect(page).to have_content(onboarding.charge.charge_memberships.first.charge_preset.name)
    click_on 'Complete'

    expect(page).to have_content('Select Specific Members')
  end
end
