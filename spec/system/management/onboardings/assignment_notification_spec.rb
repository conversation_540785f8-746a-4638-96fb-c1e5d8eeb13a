require 'rails_helper'

RSpec.describe 'onboarding assignment notification', :js, capybara_login: :tenant do
  context 'when a member is assigned' do
    let!(:assignment) { create(:member_onboarding_assignment, tenant: tenant) }

    it 'sends an email to the member to go to dashboard' do
      perform_enqueued_jobs do
        MemberOnboardingsMailer.notify_assignment(assignment:).deliver_later
      end

      open_email tenant.email

      current_email.click_link 'Start Onboarding'

      expect(page).to have_content 'Member Onboarding'
    end
  end

  context 'when a member gets a reminder to complete onboarding' do
    let!(:assignment) do
      create(:member_onboarding_assignment, tenant: tenant, notified_at: 8.days.ago)
    end

    it 'sends an email to the member to go to dashboard' do
      perform_enqueued_jobs do
        MemberOnboardingsMailer.remind_assignment(assignment:).deliver_later
      end

      open_email tenant.email

      current_email.click_link 'Log In'

      expect(page).to have_content 'Member Onboarding'
    end
  end
end
