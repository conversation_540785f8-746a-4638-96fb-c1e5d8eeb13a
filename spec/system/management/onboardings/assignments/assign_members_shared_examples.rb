RSpec.shared_examples 'onboarding assign members' do
  let!(:property) { create(:property) }

  def create_member(property)
    agreement = create(:simple_agreement,
                       :with_property,
                       property: property,
                       company: property.company)

    agreement.memberships.primary.first.tenant
  end

  let!(:member_one) { create_member(property_one) }
  let!(:member_two) { create_member(property_two) }

  let(:onboarding) do
    create(:member_onboarding_configuration, :information_collection, portfolio: property.portfolio)
  end

  let(:property_one) { onboarding.onboardable_properties.first }
  let(:property_two) { onboarding.onboardable_properties.second }

  let(:skip_next_button_assigning_context) { true }

  before do
    create(:member_onboarding_assignment, tenant: member_two, configuration: onboarding)
    visit path[onboarding]
  end

  context 'when sorting' do
    it 'sorts by name' do
      tenants = Tenant.all.sort_by(&:last_name)

      sort_index 'Name'
      expect(tenants.first.name).to appear_before(tenants.last.name)

      sort_index 'Name', direction: :descending
      expect(tenants.last.name).to appear_before(tenants.first.name)
    end

    it 'sorts by email' do
      tenants = Tenant.all.sort_by(&:email)

      sort_index 'Email'
      expect(tenants.first.name).to appear_before(tenants.last.name)

      sort_index 'Email', direction: :descending
      expect(tenants.last.name).to appear_before(tenants.first.name)
    end

    xit 'sorts by property' do
      # Not implemented yet
    end

    xit 'sorts by active onboarding' do
      # Not implemented yet
    end
  end

  context 'when filtering' do
    it 'filters by search' do
      expect(page).to have_content(Tenant.first.name)
      expect(page).to have_content(Tenant.second.name)

      fill_in 'Search...', with: Tenant.first.name

      expect(page).to have_no_content(Tenant.second.name)
      expect(page).to have_content(Tenant.first.name)
    end

    it 'filters by active onboarding status' do
      expect(page).to have_content(member_one.name)
      expect(page).to have_content(member_two.name)

      filter_index 'Active Onboarding', with: 'Assigned'

      expect(page).to have_no_content(member_one.name)
      expect(page).to have_content(member_two.name)

      visit path[onboarding]

      filter_index 'Active Onboarding', with: 'None'

      expect(page).to have_content(member_one.name)
      expect(page).to have_no_content(member_two.name)
    end

    it 'filters by property' do
      expect(page).to have_content(member_one.name)
      expect(page).to have_content(member_two.name)

      filter_index 'Property', with: member_one.onboardable_property.name

      expect(page).to have_content(member_one.name)
      expect(page).to have_no_content(member_two.name)

      visit path[onboarding]

      filter_index 'Property', with: member_two.onboardable_property.name

      expect(page).to have_no_content(member_one.name)
      expect(page).to have_content(member_two.name)
    end

    it 'filters by tag' do
      tag = create(:tag, taggable_type: 'Agreements::SimpleAgreement')
      tag2 = create(:tag, taggable_type: 'Agreements::SimpleAgreement')

      Agreements::SimpleAgreement.first.tags << tag
      Agreements::SimpleAgreement.first.save!

      visit path[onboarding]

      expect(page).to have_content(member_one.name)

      filter_index 'Membership Tag', with: tag.tag

      expect(page).to have_content(member_one.name)
      expect(page).to have_no_content(member_two.name)

      visit path[onboarding]

      filter_index 'Membership Tag', with: tag2.tag
      expect(page).to have_no_content(member_one.name)
    end
  end

  context 'when assigning members' do
    it 'assigns members to onboarding' do
      select_row member_one.name
      click_on_batch_action_button 'Assign'

      within_modal do
        expect(page).to have_content "Assign to #{onboarding.name}"

        click_on 'Yes, Assign'
      end

      expect(page).to have_content '1 member successfully assigned'
    end

    it 'warns when already assigned members are selected' do
      select_row member_two.name
      click_on_batch_action_button 'Assign'

      within_modal do
        expect(page).to have_content 'Some of the selected members currently have an active onboarding assigned.'

        click_on 'Yes, Assign'
      end

      expect(page).to have_content '1 member successfully assigned'
    end
  end

  context 'when assigning members by clicking next' do
    before do
      skip if skip_next_button_assigning_context
    end

    it 'redirects user to auto assignment page directly if no members are selected' do
      click_on 'Next'

      expect(page).to have_content 'Auto Assignment'
      expect(page).to have_no_content 'member successfully assigned'
    end

    it 'assigns members to onboarding' do
      select_row member_one.name
      click_on 'Next'

      within_modal do
        expect(page).to have_content "Assign to #{onboarding.name}"

        click_on 'Yes, Assign'
      end

      expect(page).to have_content '1 member successfully assigned'
      expect(page).to have_content 'Auto Assignment'
    end

    it 'warns when already assigned members are selected' do
      select_row member_two.name
      click_on 'Next'

      within_modal do
        expect(page).to have_content 'Some of the selected members currently have an active onboarding assigned.'

        click_on 'Yes, Assign'
      end

      expect(page).to have_content '1 member successfully assigned'
      expect(page).to have_content 'Auto Assignment'
    end
  end
end
