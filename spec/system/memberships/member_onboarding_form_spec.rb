require 'rails_helper'

require_relative '../../shared/electronic_signatures_context'

RSpec.describe 'member onboarding form', :js do
  include_context 'electronic signatures'

  let!(:property) { create(:property, name: 'Illinois Beta') }

  let!(:agreement_type) do
    create(:agreement_type, name: 'Membership',
                            document_template: create(:document))
  end

  let(:receives_membership_agreement?) { true }

  before do
    Customer.current.greek_housing!

    property.company.meta!(:university, 'University of Illinois')

    create(:property_manager) # Customer representative

    # Lease Template
    create(:document, template_options: { template_type: :lease })

    allow_any_instance_of(MemberOnboarding::CreateLease).to \
      receive(:generate_document).and_return(true)

    # Initiation Fees
    revenue_account = create(:revenue_account,
                             tenant: property.company.chart_of_accounts)

    preset = create(:charge_preset,
                    net_d: 30,
                    account: revenue_account,
                    amount: '$100.00',
                    name: 'Initiation Fee')

    allow_any_instance_of(GreekHousing::Cohort).to \
      receive(:membership_charge_presets) { [preset] }

    allow_any_instance_of(GreekHousing::Cohort).to \
      receive(:receives_membership_agreement?) {
        receives_membership_agreement?
      }

    visit new_member_onboarding_path(property_id: property.id)
  end

  def fill_in_member_information
    within '.member-fields' do
      fill_in 'Legal First Name', with: 'AMANDA'
      fill_in 'Legal Last Name', with: 'Ripley'
      fill_in 'Informal First Name', with: 'Manda'
      fill_in 'Email', with: '<EMAIL>'
      fill_in 'Phone', with: '************'
      fill_in 'Student ID Number', with: '426'
      fill_in 'Date of Birth', with: '2/22/1994'
      fill_in 'Social Security Number', with: '*********'
    end
  end

  def fill_in_guarantor_information
    within '.guarantor-fields' do
      fill_in 'Legal First Name', with: 'ellen'
      fill_in 'Legal Last Name', with: 'ripley'
      fill_in 'Email', with: '<EMAIL>'
      fill_in 'Phone', with: '************'
    end
  end

  def fill_in_home_address
    fill_in 'Line One', with: '1401 Vermont Street'
    fill_in 'Line Two', with: '210'
    fill_in 'City', with: 'Detroit'
    fill_in 'State', with: 'Michigan'
    fill_in 'Zip Code', with: '48226'
  end

  scenario 'first name is required' do
    click_on 'Continue'

    expect(page).to have_content(/first name can't be blank/i)
  end

  scenario 'a user fills out a membership onboarding form with a guarantor' do
    fill_in_member_information
    fill_in_guarantor_information
    fill_in_home_address

    click_on 'Continue'

    expect(page).to have_content(/review and sign/i)

    # Signature
    fill_in 'Name', with: 'Amanda Ripley-McClaren'
    fill_in_signature
    click_on 'Submit Signature'

    expect(page).to have_content(/please agree/i)

    check_checkbox 'I agree to use electronic records and signatures.'

    perform_enqueued_jobs do
      click_on 'Submit Signature'

      sleep(1)

      expect(page).to have_content(/thank you/i)
    end

    member = Tenant.first!
    expect(member.name).to eq('Amanda Ripley')
    expect(member.email).to eq('<EMAIL>')
    expect(member.formatted_phone).to eq('(*************')
    expect(member.meta(:nickname)).to eq('Manda')
    expect(member.meta(:student_id_number)).to eq('426')
    expect(member.date_of_birth).to eq(Date.new(1994, 2, 22))
    expect(member.agreed_to_sms_at).to be_present
    taxpayer_identification = member.taxpayer_identification
    expect(taxpayer_identification).to be_ssn
    expect(taxpayer_identification.tin).to eq('*********')
    expect(member.forwarding_address.city).to eq('Detroit')
    expect(member.meta(:completed_member_profile)).to eq(true)

    guarantor = Tenant.last!
    expect(guarantor.name).to eq('Ellen Ripley')
    expect(guarantor.email).to eq('<EMAIL>')
    expect(guarantor.formatted_phone).to eq('(*************')
    expect(guarantor.forwarding_address.city).to eq('Detroit')
    expect(guarantor.meta(:completed_member_profile)).to eq(true)

    agreement = Agreements::SimpleAgreement.last!
    expect(agreement.agreement_type).to eq(agreement_type)
    expect(agreement.primary_tenant).to eq(member)
    expect(agreement.property).to eq(property)
    expect(agreement.start_date).to eq(Time.zone.today)
    expect(agreement.end_date).to be_nil
    expect(agreement.executed_lease_document).to be_present
    expect(agreement.tenants).to contain_exactly(member, guarantor)

    signature = ElectronicSignature.first!
    expect(signature.recipient).to eq(member)
    expect(signature.full_name).to eq('Amanda Ripley-McClaren')
    expect(signature.email).to eq(member.email)
    expect(signature.signature).to be_present

    lease = Lease.last!
    expect(lease).to be_fixed
    expect(lease.unit.property).to eq(property)
    expect(lease.unit.name).to eq('Unplaced')

    expect(lease.move_in_costs_amount.format).to eq('$28,560.00')
    expect(lease.start_date).to eq(Date.new(2025, 8, 22))
    expect(lease.end_date).to eq(Date.new(2027, 5, 14))

    member_membership = lease.lease_memberships.first!
    expect(member_membership.tenant).to eq(member)
    expect(member_membership).to be_primary_tenant

    guarantor_membership = lease.lease_memberships.last!
    expect(guarantor_membership.tenant).to eq(guarantor)
    expect(guarantor_membership).to be_guarantor

    # Membership Charge
    invoice = Invoice.last!
    expect(invoice).to have_attributes(
      date: Time.zone.today,
      due_date: 30.days.from_now.to_date,
      description: 'Initiation Fee',
      amount: Monetize.parse('$100.00'),
      seller: property,
      buyer: member
    )

    # TODO: Check for Lease Signature Requests
  end

  context 'with a lease-only cohort' do
    let(:receives_membership_agreement?) { false }

    scenario 'a lease only member onboarding' do
      fill_in_member_information
      fill_in_guarantor_information
      fill_in_home_address

      click_on 'Continue'

      expect(page).to have_content(/sign your document/i)

      # Lease Agreement
      lease = Lease.last!
      expect(lease.tenants.count).to eq(2)

      # No Membership
      expect(Agreements::SimpleAgreement.count).to eq(0)
    end
  end
end
