require 'rails_helper'

RSpec.describe Vendor::Persist do
  let(:valid_params) do
    ActionController::Parameters.new(
      vendor: {
        name: 'Home Depot',
        vendor_contacts_attributes: [
          {
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON><PERSON>',
            email: '<EMAIL>',
            phone: '************'
          }
        ],
        address_attributes: {
          line_one: '42 Wallaby Way',
          city: 'Sydney',
          region: 'New South Wales',
          postal_code: '2127'
        },
        tags: %w[lumber electrical plumbing]
      }
    )
  end

  let(:invalid_params) do
    ActionController::Parameters.new(vendor: { name: '' })
  end

  let(:result) { described_class.call(params) }

  describe 'creating a new vendor' do
    context 'with valid params' do
      let(:params) { valid_params }

      it 'is successful' do
        expect(result).to be_successful
      end

      it 'persists a vendor' do
        vendor = result.vendor

        expect(vendor).to be_persisted
        expect(vendor.tags.pluck(:tag)).to eq(%w[Lumber Electrical Plumbing])
      end
    end

    context 'with invalid params' do
      let(:params) { invalid_params }

      it 'is not successful' do
        expect(result).not_to be_successful
      end

      it 'has errors' do
        expect(result.errors).to be_present
      end
    end
  end

  describe 'updating a vendor' do
    let(:vendor) { create(:vendor) }

    context 'with valid params' do
      let(:params) { valid_params.merge(id: vendor.id) }

      it 'is successful' do
        expect(result).to be_successful
      end

      it 'updates the vendor' do
        expect { result }.to change { vendor.reload.name }
      end
    end

    context 'with invalid params' do
      let(:params) { invalid_params.merge(id: vendor.id) }

      it 'is not successful' do
        expect(result).not_to be_successful
      end

      it 'has errors' do
        expect(result.errors).to be_present
      end

      it 'does not update the vendor' do
        expect { result }.not_to change { vendor.reload.name }
      end
    end
  end
end
