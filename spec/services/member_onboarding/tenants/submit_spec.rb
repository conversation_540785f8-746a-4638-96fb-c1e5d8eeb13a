require 'rails_helper'

RSpec.describe 'MemberOnboarding::Tenants::Submit' do
  before do
    create(:resident)
    create(:agreement_type, name: 'Membership', document_template: create(:document))
    create(:member_onboarding_assignment, configuration: onboarding, tenant: tenant)
    tenant.update(lead_property_id: property.id, date_of_birth: nil)

    allow_any_instance_of(MemberOnboarding::CreateLease).to \
      receive(:generate_document).and_return(true)
  end

  let(:tenant) { create(:tenant, lead_property: property) }

  let(:portfolio) { Portfolio.first }

  let(:property) { portfolio.properties.first }

  let(:property_memberships) do
    build_list(:member_onboarding_property_membership, 1, property: property)
  end

  let(:wizard) { MemberOnboarding::Tenants::Wizard.find_or_create(tenant) }

  describe 'locking' do
    def allow_sleep_and_submit(duration:)
      Customer.activate!('alever')
      allow_any_instance_of(MemberOnboarding::Tenants::Submit).to(
        receive(:ensure_membership_agreement!).and_wrap_original do |original, *args, &block|
          sleep(duration)
          original.call(*args, &block)
        end
      )
      wizard.perform_submit(
        ActionController::Parameters.new({}),
        OpenStruct.new({ remote_ip: '*******' })
      )
    end

    let(:onboarding) do
      create(:member_onboarding_configuration,
             membership_agreement: build(
               :member_onboarding_membership_agreement, membership_template: nil
             ),
             portfolio: portfolio,
             property_memberships: property_memberships)
    end

    it 'locks' do
      expect do
        Thread.new { allow_sleep_and_submit(duration: 3) }
        Thread.new { allow_sleep_and_submit(duration: 3) }
        sleep(8)
      end.to change {
               Agreements::SimpleAgreement::Membership.where(role: :primary, tenant: tenant).count
             }.by(1)
    end

    it 'works regularly' do
      expect do
        allow_sleep_and_submit(duration: 1)
        sleep(2)
      end.to change {
               Agreements::SimpleAgreement::Membership.where(role: :primary, tenant: tenant).count
             }.by(1)
    end
  end

  describe 'one time and recurring charges' do
    let(:onboarding) do
      lease_agreement = build(:member_onboarding_lease_agreement, lease_template: nil,
                                                                  proration: :prorated_whole)
      membership_agreement = build(:member_onboarding_membership_agreement,
                                   membership_template: nil)
      create(:member_onboarding_configuration,
             lease_agreement:,
             membership_agreement:,
             charge: onboarding_charge)
    end

    let(:onboarding_charge) do
      build(:member_onboarding_charge, charge_memberships:, charge_membership_count: 0)
    end

    let(:configuration) { portfolio.configuration }
    let!(:charge_presets) do
      {
        rent:
          create(:charge_preset, configuration:, amount: '$333.00', name: 'Rent'),
        security_deposit:
          create(:charge_preset, configuration:, amount: '$100.00', name: 'Security Deposit'),
        membership_fee:
          create(:charge_preset, configuration:, amount: '$765.00', name: 'Membership Fee')
      }
    end

    let(:charge_memberships) do
      [build(:member_onboarding_charge_membership,
             kind: :lease, charge_preset: charge_presets[:rent], recurring: true),
       build(:member_onboarding_charge_membership,
             kind: :membership, charge_preset: charge_presets[:membership_fee], recurring: false),
       build(:member_onboarding_charge_membership,
             kind: :lease, charge_preset: charge_presets[:security_deposit], recurring: false)]
    end

    def invoice_matcher(preset_id, unit_price)
      having_attributes(
        seller_id: property.id,
        seller_type: 'Property',
        buyer_id: tenant.id,
        buyer_type: 'Tenant',
        line_items:
          a_collection_containing_exactly(
            having_attributes(
              charge_preset_id: preset_id,
              quantity: 1,
              unit_price: Monetize.parse(unit_price)
            )
          )
      )
    end

    def charge_schedule_entry_matcher(name, recurring, allocation_amount)
      having_attributes(
        recurring: recurring,
        name: name,
        allocations:
          a_collection_containing_exactly(
            having_attributes(
              amount: Monetize.parse(allocation_amount),
              lease_membership: having_attributes(tenant_id: tenant.id)
            )
          )
      )
    end

    it 'makes the invoices for the on-time charges' do
      expect do
        wizard.perform_submit(
          ActionController::Parameters.new({}),
          OpenStruct.new({ remote_ip: '*******' })
        )
      end.to create_records(Invoice) { |invoices|
        expect(invoices).to(
          contain_exactly(
            invoice_matcher(charge_presets[:security_deposit].id, '$100.00'),
            invoice_matcher(charge_presets[:membership_fee].id, '$765.00')
          )
        )
      }
    end

    it 'creates the charge schedule for all of the lease fees' do
      expect do
        wizard.perform_submit(
          ActionController::Parameters.new({}),
          OpenStruct.new({ remote_ip: '*******' })
        )
      end.to create_records(ChargeSchedule) { |charge_schedules|
        lease_charge_schedule = charge_schedules.find { _1.chargeable_type == 'Lease' }
        expect(lease_charge_schedule.entries).to(
          contain_exactly(
            charge_schedule_entry_matcher('Security Deposit', false, '$100.00'),
            charge_schedule_entry_matcher('Rent', true, '$333.00')
          )
        )

        lease = lease_charge_schedule.chargeable

        expect(lease).to be_prorated_whole

        membership_charge_schedule = charge_schedules.find do |charge_schedule|
          charge_schedule.chargeable_type == 'Agreements::SimpleAgreement'
        end
        expect(membership_charge_schedule.entries).to be_empty
      }
    end
  end
end
