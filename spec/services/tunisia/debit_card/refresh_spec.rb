require 'rails_helper'

RSpec.describe Tunisia::DebitCard::Refresh, vcr: {
  cassette_name: 'tunisia/debit_card/list'
} do
  subject(:perform_service) { described_class.call(company:) }

  let!(:company) { create(:company, tunisia_customer_id: 'tunisia_customer_id') }

  let!(:bank_account) { create(:bank_account, owner: company) }

  let!(:deposit_account) do
    create(:tunisia_deposit_account, bank_account: bank_account, tunisia_id: 'tunisia_id')
  end

  # This matches the vcr cassette
  let!(:debit_card) do
    create(
      :tunisia_debit_card,
      debit_card_attributes.merge(deposit_account: deposit_account)
    )
  end

  let(:debit_card_attributes) do
    {
      tunisia_card_id: 'tunisia_card_id',
      status: 'Active',
      last_four: '1234',
      expiration: '2029-05',
      card_created_at: '2025-05-21T21:15:31.854Z'
    }
  end

  describe 'when deposit account not found' do
    it 'fails' do
      deposit_account.update!(tunisia_id: 'fake_tunisia_id')

      expect do
        perform_service
      end.to raise_error(RuntimeError, /not found/i)
    end
  end

  describe 'when card already exists' do
    it 'does nothing if card attributes has not changed' do
      expect do
        perform_service
      end.to not_change { debit_card.reload.attributes }
        .and not_change { debit_card.audits.count }
    end

    it 'updates the existing card if status changed' do
      debit_card.update!(status: 'Inactive')

      expect do
        perform_service
      end.to \
        change { debit_card.reload.status }
        .from('Inactive').to('Active')
        .and change { debit_card.audits.count }
        .by(1)
    end
  end

  describe 'when receiving a new card' do
    let(:debit_card) { nil }

    it 'creates a new card' do
      expect do
        perform_service
      end.to change { Tunisia::DebitCard.count }.from(0).to(1)

      new_card = deposit_account.debit_cards.last

      expect do
        new_card.update!(debit_card_attributes)
      end.to not_change { new_card.reload.attributes }
        .and not_change { new_card.audits.count }
    end
  end

  describe 'when card is missing from API response' do
    it 'raises an error' do
      missing_debit_card_attributes = debit_card_attributes.dup
      missing_debit_card_attributes[:tunisia_card_id] = 'missing_tunisia_card_id'

      create(
        :tunisia_debit_card,
        missing_debit_card_attributes.merge(deposit_account: deposit_account)
      )

      expect do
        perform_service
      end.to raise_error(RuntimeError, /missing from response/i)
    end

    it 'does not raise an error if a different company owns the card' do
      missing_debit_card_attributes = debit_card_attributes.dup
      missing_debit_card_attributes[:tunisia_card_id] = 'missing_tunisia_card_id'

      another_company = create(:company, tunisia_customer_id: 'another_tunisia_customer_id')

      another_bank_account = create(:bank_account, owner: another_company)

      another_deposit_account = create(:tunisia_deposit_account,
                                       bank_account: another_bank_account,
                                       tunisia_id: 'another_tunisia_id')

      create(
        :tunisia_debit_card,
        missing_debit_card_attributes.merge(deposit_account: another_deposit_account)
      )

      # Expect no errors
      expect(perform_service).to be_successful
    end
  end
end
