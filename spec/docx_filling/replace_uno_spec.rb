require 'rails_helper'

RSpec.describe DocxFilling::ReplaceUno do
  describe 'handling errors from python script' do
    let(:dummy_error_script) { absolute_fixture('filling/dummy_error.sh').to_s }

    before do
      allow(described_class).to receive(:python_path).and_return('sh')
      allow(described_class).to receive(:script_path).and_return(dummy_error_script)
    end

    it 'raises an error' do
      expect do
        described_class.new(Tempfile.new('path')).commit
      end.to raise_error(DocxFilling::UnoInterpolationError, /Dummy Error/)
    end
  end

  describe 'filling a docx' do
    let(:template) { absolute_fixture('filling/UnoTemplate.docx') }

    it 'replaces text' do
      output_path = absolute_fixture('filling/UnoTemplateResult.docx').to_s

      doc = described_class.build(template)
      replacements = [
        { pattern: '{full_name}', replacement: '<PERSON>' },
        { pattern: '{todays_date}', replacement: 'August 1st' },
        { pattern: '{tenant[0].signature_name}', replacement: '<PERSON>' },
        { pattern: /{[^\s]+(signature|signature_date|signature_name|initials)}/,
          replacement: 'preserved' },
        { pattern: /{[^\s]+(?<!signature|signature_date|signature_name|initials)}/,
          replacement: 'removed' }
      ]
      replacements.each do |rep|
        doc.replace(rep[:pattern], rep[:replacement], true)
      end
      doc.commit(output_path)

      sleep 1

      replaced_contents = DocxFilling::ListTags.get_doc_text(output_path)

      replacements.each do |rep|
        expect(replaced_contents).to match(rep[:replacement])
      end

      File.delete(output_path)
      sleep 1
    end
  end
end
