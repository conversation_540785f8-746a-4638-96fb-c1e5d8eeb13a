require 'rails_helper'

RSpec.describe Accounting::Payables::PaymentsController,
               devise_login: :property_manager do
  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    let(:vendor) { create(:vendor) }
    let(:invoice) { create(:invoice, seller: vendor) }
    let(:params) do
      { payee_gid: vendor.to_global_id.to_s, invoice_ids: invoice.id }
    end

    context 'successful' do
      it 'redirects to the payment' do
        payment = create(:payment)

        allow_any_instance_of(
          Accounting::CreatePayablePayment
        ).to receive(:call) do
          OpenStruct.new(successful?: true, payment: payment)
        end

        post :create, params: params

        expect(response).to redirect_to(
          accounting_payment_path(payment)
        )
      end
    end

    context 'unsuccessful' do
      it 'renders the new template' do
        allow_any_instance_of(
          Accounting::CreatePayablePayment
        ).to receive(:call) do
          OpenStruct.new(successful?: false, payment: Payment.new)
        end

        post :create, params: params

        expect(response).to render_template :new
      end
    end

    context 'unapproved' do
      let(:customer) { Customer.first }
      let(:all_params) do
        params.merge(
          payment: {
            amount: invoice.amount.to_f,
            description: 'test',
            date: Time.zone.today,
            kind: :check
          }
        )
      end

      let!(:rule) { create(:approvals_rule) }

      it 'returns 422 unprocessable' do
        post :create, params: all_params
        expect(response).to have_http_status(:unprocessable_entity)
        expect(Payment.count).to eq 0
      end
    end
  end
end
