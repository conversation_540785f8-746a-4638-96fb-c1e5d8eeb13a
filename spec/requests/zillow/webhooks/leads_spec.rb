require 'rails_helper'

RSpec.describe 'zillow leads webhook' do
  let(:customer) { Customer.find_by(subdomain: 'alever') }
  let(:floorplan) { create(:floorplan) }
  let!(:zillow_claim) { create(:zillow_claim, properties: [floorplan.property]) }
  let(:listing) { create(:listing, :zillow_syndication, :published, :with_agent, floorplan:) }
  let(:listing_presenter) do
    Zillow::Hotpads::SinglefamilyPresenter.new(zillow_claim:, listing:, customer:)
  end
  let(:hotpads_listing_id) { listing_presenter.hotpads_listing_id }

  let(:payload_string) do
    # TODO: Tell Zillow they need to correct some parts of this, corrected in our fixture
    # 'reasonForMoving':	high%rent -- malformed space
    # 'jobTitle':	Software%Engineer -- malformed space
    # 'movingDate': 20160926 -- does not match docs
    fixture_path = absolute_fixture('zillow/lead_payload.txt')
    File.read(fixture_path).gsub('p01023', hotpads_listing_id)
  end

  before do
    listing.floorplan.property.tap(&:multifamily!)
    create(:unit, floorplan: listing.floorplan, property: listing.floorplan.property)
    host! 'www.lvh.me'
  end

  context 'when unauthenticated' do
    it 'responds with unauthorized status' do
      post [zillow_webhooks_leads_url(subdomain: 'www'), payload_string].join('?')
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'processing' do
    let(:headers) do
      { 'Authorization' => "Bearer #{ENV.fetch('ZILLOW_WEBHOOK_AUTH_TOKEN')}",
        'Content-Type' => 'application/x-www-form-urlencoded' }
    end

    let(:with_payload) { [zillow_webhooks_leads_url(subdomain: 'www'), payload_string].join('?') }

    context 'when unsuccessful' do
      it 'responds with unprocessable_entity status' do
        allow(Zillow::ProcessLead).to receive(:call).
          and_return(OpenStruct.new(successful: false, errors: ['Failed to process lead.']))
        expect(Honeybadger).to receive(:notify).with(
          an_instance_of(Zillow::LeadProcessingError),
          context: { params: a_hash_including('numBedroomsSought' => '3')}
        )
        post with_payload, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    it 'produces the expected guest card' do
      post with_payload, headers: headers
      customer.activate do
        expect(GuestCard.last).to(
          have_attributes(
            first_name: 'Rachel',
            last_name: 'Lee',
            email: '<EMAIL>',
            move_in_date: Date.strptime('20160926', '%Y%m%d'),
            notes: 'Looking for spacious 3 bedroom apartment or house',
            style: 'three_bedroom',
            external_source_id: Source.find_by(source: 'Zillow.Com').id
          )
        )
        expect(Tenant.last).to(
          have_attributes(
            first_name: 'Rachel',
            last_name: 'Lee',
            email: '<EMAIL>',
            phone: '+15555558378',
            kind: 'lead'
          )
        )
      end

      expect(response).to have_http_status(:success)
    end

    context 'when listing is specified in webhook' do
      it 'assigns the guest card to the listing floor plan' do
        post with_payload, headers: headers
        customer.activate do
          expect(GuestCard.last).to(
            have_attributes(floorplan_id: listing.floorplan.id, property_id: listing.property.id)
          )
        end
      end
    end

    context 'when multiple properties and listing is not specified' do
      let(:listing_presenter) do
        Zillow::Hotpads::MultifamilyPresenter.new(zillow_claim:, listings: [listing], customer:)
      end

      let!(:another_property) do
        customer.activate { create(:property, zillow_claim:).tap { zillow_claim.reload } }
      end

      it 'makes a lead for all the properties' do

        post with_payload, headers: headers
        customer.activate do
          shared = {
            first_name: 'Rachel',
            last_name: 'Lee',
            email: '<EMAIL>',
            move_in_date: Date.strptime('20160926', '%Y%m%d'),
            notes: 'Looking for spacious 3 bedroom apartment or house',
            style: 'three_bedroom',
            external_source_id: Source.find_by(source: 'Zillow.Com').id,
            floorplan_id: nil
          }
          expect(GuestCard.where(property_id: [listing.property.id, another_property.id])).to(
            contain_exactly(
              an_object_having_attributes({property_id: listing.property.id}.merge(shared)),
              an_object_having_attributes({property_id: another_property.id}.merge(shared))
            )
          )
        end
      end
    end
  end
end
