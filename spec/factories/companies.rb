FactoryBot.define do
  factory :company do
    setup { true }
    customer_managed { true }
    sequence(:name) do |i|
      "Company #{i} #{Faker::Company.suffix}"
    end
    dba { Faker::Company.name }
    business_type { Company.business_types.keys.sample }
    address { build(:address, addressable: instance) }
    state_of_incorporation { 'Delaware' }
    chart_of_accounts { nil }
    portfolio
    phone { '+***********' }

    trait :locked do
      locked_at { 1.week.ago }
    end

    trait :client_entity do
      name { 'Client Entity, LLC' }
      chart_of_accounts
      portfolio { nil }
      customer_managed { false }
    end

    trait :client_company do
      chart_of_accounts
      portfolio { nil }
      customer_managed { false }
    end

    trait :tunisia_customer do
      tunisia_customer_id { '1497756' }
    end
  end
end
