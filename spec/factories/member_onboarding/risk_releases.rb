FactoryBot.define do
  factory :member_onboarding_risk_release, class: 'MemberOnboarding::RiskRelease' do
    account factory: :revenue_account
    monthly_cost { 25 }
    coverage_start_date { Time.zone.today }
    coverage_end_date { 1.year.from_now }
    enrollment_required { false }
    billing_frequency { :one_time }
    configuration { association(:member_onboarding_configuration, risk_release: instance) }
  end
end
