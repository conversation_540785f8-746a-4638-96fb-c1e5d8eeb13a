FactoryBot.define do
  factory :member_onboarding_charge_membership, class: 'MemberOnboarding::ChargeMembership' do
    charge factory: :member_onboarding_charge
    charge_preset
    kind { :membership }
    recurring { false }

    after(:create) do |charge_membership|
      configuration = charge_membership.charge.configuration.portfolio.configuration
      charge_membership.charge_preset.update!(configuration: configuration)
    end
  end
end
