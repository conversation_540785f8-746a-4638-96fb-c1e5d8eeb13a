FactoryBot.define do
  factory :member_onboarding_configuration, class: 'MemberOnboarding::Configuration' do
    portfolio
    sequence(:name) { |n| "Fall Onboarding v#{n}" }

    transient do
      num_property_memberships { 2 }
    end

    trait :information_collection do
      information_collection do
        association(:member_onboarding_information_collection, configuration: instance)
      end
    end

    trait :guarantor do
      guarantor { association(:member_onboarding_guarantor, configuration: instance) }
    end

    trait :lease_agreement do
      lease_agreement { association(:member_onboarding_lease_agreement, configuration: instance) }
    end

    trait :charge do
      charge { association(:member_onboarding_charge, configuration: instance) }
    end

    trait :membership_agreement do
      membership_agreement do
        association(:member_onboarding_membership_agreement, configuration: instance)
      end
    end

    trait :risk_release do
      risk_release do
        association(:member_onboarding_risk_release, configuration: instance)
      end
    end

    after(:build) do |configuration, evaluator|
      if Feature.enabled?(:onboarding_enhancements) && evaluator.num_property_memberships.positive?
        company = build(:company, portfolio: configuration.portfolio)
        evaluator.num_property_memberships.times do
          configuration.onboardable_property_memberships << build(
            :member_onboarding_property_membership,
            property: build(:property, company:),
            enhanced: true,
            configuration:
          )
        end
      end
    end
  end
end
