FactoryBot.define do
  factory :member_onboarding_membership_agreement, class: 'MemberOnboarding::MembershipAgreement' do
    membership_template factory: %i[document membership_template]
    require_member_signature { false }
    require_countersigner { false }
    require_guarantor_signature { false }
    include_damage_waiver { false }
    countersigner { nil }
    configuration { association(:member_onboarding_configuration, membership_agreement: instance) }
  end
end
