FactoryBot.define do
  factory :member_onboarding_lease_agreement, class: 'MemberOnboarding::LeaseAgreement' do
    lease_template factory: %i[document lease_template]
    require_member_signature { false }
    require_guarantor_signature { false }
    require_countersigner { false }
    countersigner { nil }
    lease_start_date { Time.zone.today }
    lease_end_date { Time.zone.today + 1.year }
    configuration { association(:member_onboarding_configuration, lease_agreement: instance) }
  end
end
