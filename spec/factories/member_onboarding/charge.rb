FactoryBot.define do
  factory :member_onboarding_charge, class: 'MemberOnboarding::Charge' do
    group_invoices { true }
    transient do
      charge_membership_count { 1 }
    end

    after(:build) do |charge, evaluator|
      create_list(:member_onboarding_charge_membership,
                  evaluator.charge_membership_count,
                  charge: charge)
    end
    configuration { association(:member_onboarding_configuration, charge: instance) }
  end
end
