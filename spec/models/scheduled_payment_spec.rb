require 'rails_helper'

RSpec.describe ScheduledPayment do
  describe 'the factory' do
    subject { build(:scheduled_payment) }

    it { is_expected.to be_valid }

    it { is_expected.not_to be_recurring }

    it { is_expected.not_to be_pay_balance }

    it 'has an amount' do
      expect(subject.amount).to be_a(Money)
    end
  end

  describe 'the agreement only factory' do
    subject { build(:scheduled_payment, :agreement_only) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    let(:sp) { build(:scheduled_payment) }

    it { is_expected.to validate_presence_of(:date) }

    it { is_expected.to belong_to(:property).required }

    it 'requires positive amounts unless paying the balance' do
      sp.pay_balance = false

      sp.amount = nil
      expect(sp).not_to be_valid

      sp.amount = Money.new(-100, 'USD')
      expect(sp).not_to be_valid

      sp.amount = Money.new(0, 'USD')
      expect(sp).not_to be_valid

      sp.amount = Money.new(1, 'USD')
      expect(sp).to be_valid
    end
  end

  describe '#next_payment_date' do
    subject { scheduled_payment.next_payment_date }

    let(:jan_31) { Date.new(2000, 1, 31) }
    let(:feb_29) { Date.new(2000, 2, 29) }
    let(:mar_31) { Date.new(2000, 3, 31) }

    let(:scheduled_payment) { create(:scheduled_payment, date: jan_31) }

    context 'before the date' do
      before { travel_to(jan_31 - 2.days) }

      it { is_expected.to eq(jan_31) } # because it's in the future
    end

    context 'after the date' do
      before { travel_to(jan_31 + 2.days) }

      it { is_expected.to eq(Time.zone.today) } # because it hasn't ran yet

      context 'with #last_ran_at' do
        before { scheduled_payment.update!(last_ran_at: jan_31) }

        it { is_expected.to be_nil } # because it's not recurring

        describe '#recurring?' do
          before { scheduled_payment.update!(recurring: true) }

          it { is_expected.to eq(feb_29) } # leap year

          context 'farther in the future' do
            before do
              travel_to(feb_29 + 5.days)
              # 5 days late
              scheduled_payment.update!(last_ran_at: Time.zone.today)
            end

            it { is_expected.to eq(mar_31) }
          end

          context 'an a forward dated #date' do
            before { scheduled_payment.update!(date: mar_31) }

            # Skip over Feburary
            it { is_expected.to eq(mar_31) }
          end
        end
      end
    end
  end

  context 'with a lease membership' do
    subject { create(:scheduled_payment) }

    describe '#accounting_context' do
      its(:accounting_context) do
        is_expected.to be_a(Accounting::Context::LeaseMembership)
      end
    end

    describe '#ledger' do
      its(:ledger) { is_expected.to be_a(Accounting::Ledger::LeaseChain) }
    end
  end

  context 'with an agreement' do
    subject { create(:scheduled_payment, :agreement_only) }

    describe '#accounting_context' do
      its(:accounting_context) do
        is_expected.to be_a(Accounting::Context::Agreements::SimpleAgreement)
      end
    end

    describe '#ledger' do
      its(:ledger) { is_expected.to be_a(Accounting::Ledger::Tenant) }
    end
  end
end
