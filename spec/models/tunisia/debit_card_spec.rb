require 'rails_helper'

RSpec.describe Tunisia::DebitCard do
  describe 'the factory' do
    subject { build(:tunisia_debit_card) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to belong_to(:deposit_account).required }

    it { is_expected.to validate_presence_of(:tunisia_card_id) }

    it { is_expected.to validate_presence_of(:last_four) }

    it { is_expected.to validate_presence_of(:expiration) }

    it { is_expected.to validate_presence_of(:status) }

    it { is_expected.to validate_presence_of(:card_created_at) }
  end

  describe '#expiration=' do
    context 'when in string format' do
      it 'parses and sets the last day of the month' do
        debit_card = build(:tunisia_debit_card, expiration: '2023-10')

        expect(debit_card.expiration).to eq(Date.new(2023, 10, -1))
      end
    end
  end

  context 'when in date format' do
    it 'sets to the last day of the month' do
      debit_card = build(:tunisia_debit_card, expiration: Date.new(2023, 10, 1))

      expect(debit_card.expiration).to eq(Date.new(2023, 10, -1))
    end
  end
end
