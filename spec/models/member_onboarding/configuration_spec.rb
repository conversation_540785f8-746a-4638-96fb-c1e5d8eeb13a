require 'rails_helper'
require_relative 'auto_assignment_options'

RSpec.describe MemberOnboarding::Configuration do
  describe 'the factory' do
    subject { build(:member_onboarding_configuration, :information_collection) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }

    it 'validates name uniqueness' do
      create(:member_onboarding_configuration, :information_collection)
      is_expected.to validate_uniqueness_of(:name)
    end

    it { is_expected.to belong_to(:portfolio).required }

    describe '#at_least_one_module_selected' do
      it 'is valid with at least one module selected' do
        configuration = build(:member_onboarding_configuration)

        expect(configuration).not_to be_valid

        expect(configuration.errors[:base]).to include(/At least one module must be selected/)
      end
    end

    describe 'inter-module constraints' do
      describe 'when guarantor optional' do
        it 'lease cannot require guarantor signature' do
          expect do
            create(:member_onboarding_configuration, :information_collection,
                   guarantor: build(:member_onboarding_guarantor, optional: true),
                   lease_agreement: build(:member_onboarding_lease_agreement,
                                          require_member_signature: true,
                                          require_guarantor_signature: true))
          end.to raise_error(ActiveRecord::RecordNotSaved) \
                             { |e| expect(e.record).to be_a(MemberOnboarding::LeaseAgreement) }
        end
      end
    end
  end

  describe '#module_ids' do
    it 'returns the names of the modules selected for the configuration' do
      info_collection_and_guarantor = create(:member_onboarding_configuration,
                                             :information_collection,
                                             :guarantor)

      expect(info_collection_and_guarantor.module_ids).to \
        eq(['information_collection', 'guarantor'])
    end
  end

  it_behaves_like 'auto assignment for properties'

  describe 'associations' do
    describe 'properties' do
      let(:onboarding_property_membership) do
        create(:member_onboarding_property_membership, enhanced:, auto_assign:)
      end

      let(:onboarding) { onboarding_property_membership.configuration.reload }

      let(:property) { onboarding_property_membership.property }

      describe 'when both enhanced is false and auto_assign is false' do
        let(:enhanced) { false }
        let(:auto_assign) { false }

        it 'is a property with current onboarding as default' do
          expect(onboarding.property_memberships).to eq([onboarding_property_membership])
          expect(onboarding.properties).to eq([property])
          expect(onboarding.onboardable_property_memberships).not_to be_present
          expect(onboarding.auto_assign_property_memberships).not_to be_present
        end
      end

      describe 'when enhanced is true and auto_assign is false' do
        let(:enhanced) { true }
        let(:auto_assign) { false }

        it 'is a regular onboarding property membership' do
          expect(onboarding.property_memberships).not_to be_present
          expect(onboarding.onboardable_property_memberships).to eq([onboarding_property_membership])
          expect(onboarding.onboardable_properties).to eq([property])
          expect(onboarding.auto_assign_property_memberships).not_to be_present
        end
      end

      describe 'when both enhanced is true and auto_assign is true' do
        let(:enhanced) { true }
        let(:auto_assign) { true }

        it 'is both a regular and auto assign onboarding property membership' do
          expect(onboarding.property_memberships).not_to be_present
          expect(onboarding.onboardable_property_memberships).to eq([onboarding_property_membership])
          expect(onboarding.onboardable_properties).to eq([property])
          expect(onboarding.auto_assign_property_memberships).to eq([onboarding_property_membership])
          expect(onboarding.auto_assign_properties).to eq([property])
        end
      end
    end
  end
end
