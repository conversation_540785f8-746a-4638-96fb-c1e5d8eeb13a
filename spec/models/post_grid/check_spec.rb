require 'rails_helper'

RSpec.describe PostGrid::Check do
  describe 'the factory' do
    subject { build(:post_grid_check) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to belong_to(:payment).required }

    it { is_expected.to validate_presence_of(:check_id) }

    it { is_expected.to validate_presence_of(:status) }

    it 'is invalid if check_id does not start with "cheque_"' do
      check = build(:post_grid_check)
      check.check_id = 'foo_123'
      expect(check).not_to be_valid
    end

    it 'is valid if check_id starts with "cheque_"' do
      check = build(:post_grid_check)
      check.check_id = 'cheque_abc123'
      expect(check).to be_valid
    end

    it 'is invalid if the same check_id is used twice' do
      existing = create(:post_grid_check)
      new = build(:post_grid_check)

      expect(new).to be_valid

      new.check_id = existing.check_id
      expect(new).not_to be_valid
    end
  end
end
