require 'rails_helper'

RSpec.describe Tunisia::DebitCard::RefreshAllJob do
  it 'calls the update refresh company debit card service for tunisia_customer' do
    tunisia_customer = create(:company, tunisia_customer_id: 'tunisia-customer-id')

    expect(Tunisia::DebitCard::RefreshCompanyJob).to \
      receive(:perform_later)
      .once
      .with(tunisia_customer)

    perform_enqueued_jobs { described_class.perform_later }
  end

  it 'does not call the update refresh company debit card service for non tunisia_customer' do
    create(:company) # Not tunisia customer

    expect(Tunisia::DebitCard::RefreshCompanyJob).not_to receive(:perform_later)

    perform_enqueued_jobs { described_class.perform_later }
  end
end
