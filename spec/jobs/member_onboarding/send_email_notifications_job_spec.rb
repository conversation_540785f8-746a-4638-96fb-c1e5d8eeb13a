require 'rails_helper'

RSpec.describe MemberOnboarding::SendEmailNotificationsJob do
  it 'sends notifications and reminders' do
    start_date = Date.new(2025, 8, 1)
    a_day_after_start_date = start_date + 1.day
    fourty_six_days_after_start_date = start_date + 46.days

    assignment = nil
    completed_assignment = nil

    travel_to start_date do
      assignment = create(:member_onboarding_assignment)
      completed_assignment = create(:member_onboarding_assignment, :completed)

      expect(assignment.notified_at).not_to be_present
      expect(completed_assignment.notified_at).not_to be_present

      perform_enqueued_jobs { MemberOnboarding::SendEmailNotificationsJob.perform_later }

      expect(assignment.reload.notified_at.to_date).to eq(start_date)
      expect(completed_assignment.reload.notified_at).not_to be_present
    end

    travel_to a_day_after_start_date do
      perform_enqueued_jobs { MemberOnboarding::SendEmailNotificationsJob.perform_later }

      # No change
      expect(assignment.reload.notified_at.to_date).to eq(start_date)
    end

    6.times do |i|
      num_weeks = i + 1
      current_date = start_date + num_weeks.week
      travel_to current_date do
        perform_enqueued_jobs { MemberOnboarding::SendEmailNotificationsJob.perform_later }

        # remind_assignment should be called
        expect(assignment.reload.notified_at.to_date).to eq(current_date)
      end
    end

    travel_to fourty_six_days_after_start_date do
      previous_notified_at = assignment.notified_at
      perform_enqueued_jobs { MemberOnboarding::SendEmailNotificationsJob.perform_later }

      expect(assignment.reload.notified_at).to eq(previous_notified_at)
    end
  end
end
