require 'rails_helper'

RSpec.describe Address::GeocodeJob do
  let(:address) do
    create(:address,
           line_one: '1401 Vermont Street',
           line_two: nil,
           city: 'Detroit',
           region: 'Michigan',
           postal_code: '48226')
  end

  it 'geocodes an address', vcr: {
    cassette_name: 'geocode_address_successfully'
  } do
    perform_enqueued_jobs { described_class.perform_later(address) }
    address.reload
    expect(address.latitude).to eq(42.3254265)
    expect(address.longitude).to eq(-83.0706584)
    expect(address.sub_region).to eq('Wayne County')
  end
end
