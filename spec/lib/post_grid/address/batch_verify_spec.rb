require 'rails_helper'

RSpec.describe PostGrid::Address::BatchVerify do
  let(:address) { create(:address) }

  it 'returns a post_grid verification blob', vcr: {
    cassette_name: 'post_grid/address/batch_verify_successfully'
  } do
    result = described_class.call(addresses: address)
    expect(result.successful?).to be(true)
    id = address.id

    case result.response.first
    in { id: ^id, original: original, corrected: corrected, status: 'corrected', errors: errors }
      expect(original.line_one).to eq(address.line_one)
      expect(original.line_two).to eq(address.line_two)
      expect(original.city).to eq(address.city)
      expect(original.region).to eq(address.region)
      expect(original.postal_code).to eq(address.postal_code)
      expect(original.country).to eq(address.country)

      expect(corrected.line_one).to be_present
      expect(corrected.line_two).to be_present
      expect(corrected.city).to be_present
      expect(corrected.region).to be_present
      expect(corrected.postal_code).to be_present
      expect(corrected.country).to be_present

      expect(errors['provinceOrState'].first).to match(/province/i)
    end
  end
end
