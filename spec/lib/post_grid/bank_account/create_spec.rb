require 'rails_helper'

RSpec.describe PostGrid::BankAccount::Create do
  let(:bank_account) { create(:bank_account) }

  it 'fails if the owner does not have a signature' do
    result = described_class.call(bank_account:)

    expect(result.successful?).to be false
    expect(result.errors).to include 'No signature available'
  end

  it 'returns the post grid bank account id', vcr: {
    cassette_name: 'post_grid/bank_account/create_successfully'
  } do
    allow_any_instance_of(described_class).to receive(:signature).and_return( # rubocop:disable RSpec/AnyInstance
      'https://as1.ftcdn.net/jpg/02/36/99/22/1000_F_236992283_sNOxCVQeFLd5pdqaKGh8DRGMZy7P4XKm.jpg' # Random cat picture
    )

    result = described_class.call(bank_account:)

    expect(result.successful?).to be true
    expect(result.id).to be_present
    expect(result.id).to start_with 'bank_'
    expect(bank_account.reload.postgrid_account_id).to eq result.id
  end
end
