require 'rails_helper'

RSpec.describe InvoicesQuery do
  let!(:due) do
    create(
      :invoice,
      :due,
      note: 'This is a note',
      invoice_payment_approved_at: nil,
      payment_batch: build(:payment_batch)
    )
  end

  let!(:overdue) do
    create(:invoice, :overdue, invoice_payment_approved_at: nil)
  end

  let!(:paid) { create(:invoice, :paid, physical_date: 1.year.ago) }

  let!(:waived) { create(:invoice, :waived, invoice_payment_approved_at: nil) }

  describe '#filter_text' do
    it 'finds by invoice number' do
      res = described_class.new.search.filter_text(due.invoice_number)

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end

    it 'finds by line item' do
      res = described_class.new.search.filter_text(
        due.line_items.first.description
      )

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end

    it 'finds by invoice description' do
      res = described_class.new.search.filter_text(due.description)

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end

    it 'finds by invoice note' do
      res = described_class.new.search.filter_text(due.note)

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end
  end

  describe '#filter_status' do
    it 'finds by open' do
      res = described_class.new.search.filter_status('open')

      expect(res.length).to eq 2
      expect(res).to include(due, overdue)
    end

    it 'finds by closed' do
      res = described_class.new.search.filter_status('closed')

      expect(res.length).to eq 2
      expect(res).to include(paid, waived)
    end

    it 'finds by unpaid' do
      res = described_class.new.search.filter_status('unpaid')

      expect(res.length).to eq 3
      expect(res).to include(due, overdue, waived)
    end

    it 'finds by due' do
      res = described_class.new.search.filter_status('due')

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end

    it 'finds by overdue' do
      res = described_class.new.search.filter_status('overdue')

      expect(res.length).to eq 1
      expect(res.first).to eq overdue
    end

    it 'finds by paid' do
      res = described_class.new.search.filter_status('paid')

      expect(res.length).to eq 1
      expect(res.first).to eq paid
    end

    it 'finds by waived' do
      res = described_class.new.search.filter_status('waived')

      expect(res.length).to eq 1
      expect(res.first).to eq waived
    end
  end

  describe '#filter_approval' do
    it 'finds by approved' do
      res = described_class.new.search.filter_approval_status('approved')

      expect(res.length).to eq 1
      expect(res.first).to eq paid
    end

    it 'finds by unapproved' do
      res = described_class.new.search.filter_approval_status('unapproved')

      expect(res.length).to eq 3
      expect(res).to include(due, overdue, waived)
    end
  end

  describe '#filter_invoices_batch' do
    it 'finds by payment batch' do
      res = described_class.new.search.filter_invoices_batch(due.payment_batch)

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end
  end

  describe '#filter_buyer' do
    it 'finds by sgid' do
      res = described_class.new.search.filter_buyer(due.buyer.to_sgid.to_s)

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end
  end

  describe '#filter_seller' do
    it 'finds by sgid' do
      res = described_class.new.search.filter_seller(due.seller.to_sgid.to_s)

      expect(res.length).to eq 1
      expect(res.first).to eq due
    end
  end

  describe '#filter_start_date and #filter_end_date' do
    it 'finds by start and end date' do
      res = described_class.new.search
                           .filter_start_date(2.years.ago)
                           .filter_end_date(6.months.ago)

      expect(res.length).to eq 1
      expect(res.first).to eq paid
    end
  end

  describe '#with_one_line_item' do
    let!(:multiple_line_item_invoice) { create(:invoice, line_item_count: 2) }

    it 'finds invoices with one line item' do
      result = described_class.new.search.with_one_line_item

      expect(result).to contain_exactly(due, overdue, paid, waived)
    end
  end

  describe '#without_payment_plan' do
    # TODO: Move these into their own context above
    let!(:due) { nil }
    let!(:overdue) { nil }
    let!(:paid) { nil }
    let!(:waived) { nil }

    it 'partitions by active or upcoming payment plans' do
      active_plan, inactive_plan = nil

      ten_days_ago = 10.days.ago
      two_months_ago = 60.days.ago
      one_month_from_now = 1.month.from_now

      # Travel back to make sure installments are scheduled in the future
      travel_to 1.year.ago do
        active_plan = create(:payment_plan,
                             start_date: ten_days_ago,
                             end_date: one_month_from_now)

        inactive_plan = create(:payment_plan,
                               start_date: two_months_ago,
                               end_date: ten_days_ago)
      end

      # Active,    Inactive,    None
      invoice_one, invoice_two, invoice_three = create_list(:invoice, 3)

      create(:payment_plan_invoice_membership,
             invoice: invoice_one,
             payment_plan: active_plan)

      create(:payment_plan_invoice_membership,
             invoice: invoice_two,
             payment_plan: inactive_plan)

      date = Time.zone.today
      results = described_class.new.search.without_payment_plan(date)
      expect(results).to contain_exactly(invoice_two, invoice_three)

      date = 45.days.ago
      results = described_class.new.search.without_payment_plan(date)
      expect(results).to contain_exactly(invoice_three)
    end
  end

  describe '#appliable_for' do
    it 'finds vendor invoices' do
      payment = create(:payment)
      expect(payment.payer).to be_a(Company)
      expect(payment.payee).to be_a(Vendor)
      invoice = payment.invoices.first

      results = described_class.new.search.appliable_for(payment)
      expect(results).to be_empty

      payment.invoice_payments.destroy_all

      results = described_class.new.search.appliable_for(payment)
      expect(results).to contain_exactly(invoice)
    end

    it 'finds lease ledger invoices' do
      payment = create(:payment, :lease_payment)
      invoice = payment.invoices.first
      payment.invoice_payments.destroy_all

      results = described_class.new.search.appliable_for(payment)
      expect(results).to contain_exactly(invoice)

      invoice.update!(buyer_lease_membership: nil)
      results = described_class.new.search.appliable_for(payment)
      expect(results).to be_empty
    end

    it 'finds tenant refund invoices' do
      tenant = create(:tenant)
      property = create(:property)
      payment = create(:payment, payer: property, payee: tenant)
      invoice = payment.invoices.first
      payment.invoice_payments.destroy_all

      results = described_class.new.search.appliable_for(payment)
      expect(results).to contain_exactly(invoice)
    end
  end
end
